-- إضافة الأعمدة المفقودة لطرق الدفع في جدول Orders

-- التحقق من وجود العمود PaymentMethod وإضافته إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS
               WHERE TABLE_NAME = 'Orders' AND COLUMN_NAME = 'PaymentMethod')
BEGIN
    ALTER TABLE Orders ADD PaymentMethod INT NOT NULL DEFAULT 0;
    PRINT 'تم إضافة عمود PaymentMethod';
END
ELSE
BEGIN
    PRINT 'عمود PaymentMethod موجود بالفعل';
END

-- التحقق من وجود العمود PaymentPhoneNumber وإضافته إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS
               WHERE TABLE_NAME = 'Orders' AND COLUMN_NAME = 'PaymentPhoneNumber')
BEGIN
    ALTER TABLE Orders ADD PaymentPhoneNumber NVARCHAR(20) NULL;
    PRINT 'تم إضافة عمود PaymentPhoneNumber';
END
ELSE
BEGIN
    PRINT 'عمود PaymentPhoneNumber موجود بالفعل';
END

-- التحقق من وجود العمود PaymentReceiptPath وإضافته إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS
               WHERE TABLE_NAME = 'Orders' AND COLUMN_NAME = 'PaymentReceiptPath')
BEGIN
    ALTER TABLE Orders ADD PaymentReceiptPath NVARCHAR(500) NULL;
    PRINT 'تم إضافة عمود PaymentReceiptPath';
END
ELSE
BEGIN
    PRINT 'عمود PaymentReceiptPath موجود بالفعل';
END

-- التحقق من وجود العمود PaymentNotes وإضافته إذا لم يكن موجوداً
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS
               WHERE TABLE_NAME = 'Orders' AND COLUMN_NAME = 'PaymentNotes')
BEGIN
    ALTER TABLE Orders ADD PaymentNotes NVARCHAR(1000) NULL;
    PRINT 'تم إضافة عمود PaymentNotes';
END
ELSE
BEGIN
    PRINT 'عمود PaymentNotes موجود بالفعل';
END

PRINT 'تم الانتهاء من إضافة أعمدة طرق الدفع';
