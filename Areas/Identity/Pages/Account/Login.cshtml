@page
@model Abayat.Areas.Identity.Pages.Account.LoginModel
@{
    ViewData["Title"] = "تسجيل الدخول";
}

<div class="auth-container fade-in">
    <h2>@ViewData["Title"]</h2>

    <form id="account" method="post">
        <div asp-validation-summary="ModelOnly" class="validation-summary-errors"></div>
        <input type="hidden" asp-for="ReturnUrl" />

        <div class="mb-3">
            <label asp-for="Input.Email" class="form-label">البريد الإلكتروني</label>
            <input asp-for="Input.Email" class="form-control" autocomplete="username" aria-required="true" />
            <span asp-validation-for="Input.Email" class="field-validation-error"></span>
        </div>

        <div class="mb-3">
            <label asp-for="Input.Password" class="form-label">كلمة المرور</label>
            <input asp-for="Input.Password" class="form-control" autocomplete="current-password" aria-required="true" />
            <span asp-validation-for="Input.Password" class="field-validation-error"></span>
        </div>

        <div class="mb-3 form-check">
            <input asp-for="Input.RememberMe" class="form-check-input" />
            <label asp-for="Input.RememberMe" class="form-check-label">تذكرني</label>
        </div>

        <div class="mb-3">
            <button id="login-submit" type="submit" class="w-100 btn btn-primary btn-lg" onclick="return true;">تسجيل الدخول</button>
        </div>

        <div class="mb-3 text-center">
            <p>
                <a id="forgot-password" href="/Identity/Account/ForgotPassword">نسيت كلمة المرور؟</a>
            </p>
            <p>
                <a href="/Identity/Account/Register?returnUrl=@System.Net.WebUtility.UrlEncode(Model.ReturnUrl)">تسجيل حساب جديد</a>
            </p>
        </div>
    </form>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        // كود مخصص لصفحة تسجيل الدخول
        $(document).ready(function() {
            console.log("تم تحميل صفحة تسجيل الدخول");

            // إضافة معالج حدث إرسال النموذج
            $("#account").on("submit", function(e) {
                console.log("تم إرسال نموذج تسجيل الدخول");

                // التحقق من صحة البيانات
                var email = $("#Input_Email").val();
                var password = $("#Input_Password").val();

                if (!email || !password) {
                    console.log("يرجى ملء جميع الحقول المطلوبة");
                    return false;
                }

                // السماح بإرسال النموذج بشكل طبيعي
                return true;
            });
        });
    </script>
}
