@page
@model Abayat.Areas.Identity.Pages.Account.ForgotPasswordModel
@{
    ViewData["Title"] = "نسيت كلمة المرور؟";
}

<div class="auth-container fade-in">
    <h2>@ViewData["Title"]</h2>

    <form method="post">
        <div asp-validation-summary="ModelOnly" class="validation-summary-errors"></div>

        <div class="mb-3">
            <label asp-for="Input.Email" class="form-label">البريد الإلكتروني</label>
            <input asp-for="Input.Email" class="form-control" autocomplete="username" aria-required="true" />
            <span asp-validation-for="Input.Email" class="field-validation-error"></span>
        </div>

        <button type="submit" class="w-100 btn btn-primary btn-lg">إرسال رابط إعادة تعيين كلمة المرور</button>
    </form>

    <div class="mt-4 text-center">
        <p><a asp-page="./Login">العودة إلى تسجيل الدخول</a></p>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
