@page
@model Abayat.Areas.Identity.Pages.Account.AccessDeniedModel
@{
    ViewData["Title"] = "تم رفض الوصول";
}

<div class="error-page">
    <div class="container py-5">
        <div class="error-container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="error-content slide-in-left">
                        <div class="error-icon">
                            <i class="bi bi-shield-lock-fill"></i>
                        </div>
                        <h1 class="error-title">@ViewData["Title"]</h1>
                        <p class="error-message">ليس لديك صلاحية للوصول إلى هذه الصفحة.</p>

                        <div class="error-actions">
                            <a asp-area="" asp-controller="Home" asp-action="Index" class="btn btn-primary error-btn">
                                <i class="bi bi-house-fill me-2"></i> العودة للصفحة الرئيسية
                            </a>
                            <a href="/Categories" class="btn btn-outline-primary error-btn">
                                <i class="bi bi-grid-fill me-2"></i> تصفح الفئات
                            </a>
                        </div>

                        <div class="error-support mt-4">
                            <h5><i class="bi bi-headset me-2"></i> هل تحتاج إلى مساعدة؟</h5>
                            <p>يمكنك التواصل مع فريق الدعم الفني على البريد الإلكتروني: <a href="mailto:<EMAIL>"><EMAIL></a></p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="error-image zoom-in">
                        <img src="/images/access-denied.svg" alt="تم رفض الوصول" onerror="this.src='/images/error-fallback.png'" class="img-fluid">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
