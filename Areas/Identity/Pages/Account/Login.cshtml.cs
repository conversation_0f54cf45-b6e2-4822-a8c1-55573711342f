using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.Extensions.Logging;
using Abayat.Models;

namespace Abayat.Areas.Identity.Pages.Account
{
    public class LoginModel : PageModel
    {
        private readonly SignInManager<ApplicationUser> _signInManager;
        private readonly ILogger<LoginModel> _logger;

        public LoginModel(SignInManager<ApplicationUser> signInManager, ILogger<LoginModel> logger)
        {
            _signInManager = signInManager;
            _logger = logger;
        }

        [BindProperty]
        public InputModel Input { get; set; }

        public string? ReturnUrl { get; set; }

        [TempData]
        public string? ErrorMessage { get; set; }

        public class InputModel
        {
            [Required(ErrorMessage = "البريد الإلكتروني مطلوب")]
            [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صالح")]
            [Display(Name = "البريد الإلكتروني")]
            public string Email { get; set; }

            [Required(ErrorMessage = "كلمة المرور مطلوبة")]
            [DataType(DataType.Password)]
            [Display(Name = "كلمة المرور")]
            public string Password { get; set; }

            [Display(Name = "تذكرني")]
            public bool RememberMe { get; set; }
        }

        public async Task OnGetAsync(string? returnUrl = null)
        {
            if (!string.IsNullOrEmpty(ErrorMessage))
            {
                ModelState.AddModelError(string.Empty, ErrorMessage);
            }

            returnUrl ??= Url.Content("~/");

            // Clear the existing external cookie to ensure a clean login process
            await HttpContext.SignOutAsync(IdentityConstants.ExternalScheme);

            ReturnUrl = returnUrl;
        }

        public async Task<IActionResult> OnPostAsync(string? returnUrl = null)
        {
            returnUrl ??= Url.Content("~/");

            // إضافة سجلات تشخيصية
            _logger.LogInformation("بدء محاولة تسجيل دخول للمستخدم: {Email}", Input.Email);

            if (ModelState.IsValid)
            {
                try
                {
                    // This doesn't count login failures towards account lockout
                    // To enable password failures to trigger account lockout, set lockoutOnFailure: true
                    // البحث عن المستخدم أولاً
                    _logger.LogInformation("البحث عن المستخدم بالبريد الإلكتروني: {Email}", Input.Email);
                    var user = await _signInManager.UserManager.FindByEmailAsync(Input.Email);

                    if (user != null)
                    {
                        _logger.LogInformation("تم العثور على المستخدم، محاولة تسجيل الدخول: {UserName}", user.UserName);
                        // استخدام اسم المستخدم للتسجيل
                        var result = await _signInManager.PasswordSignInAsync(user.UserName, Input.Password, Input.RememberMe, lockoutOnFailure: false);

                        if (result.Succeeded)
                        {
                            _logger.LogInformation("تم تسجيل دخول المستخدم بنجاح: {Email}", Input.Email);
                            return LocalRedirect(returnUrl);
                        }
                        else if (result.RequiresTwoFactor)
                        {
                            _logger.LogInformation("المستخدم يحتاج إلى المصادقة الثنائية: {Email}", Input.Email);
                            return RedirectToPage("./LoginWith2fa", new { ReturnUrl = returnUrl, RememberMe = Input.RememberMe });
                        }
                        else if (result.IsLockedOut)
                        {
                            _logger.LogWarning("تم قفل حساب المستخدم: {Email}", Input.Email);
                            return RedirectToPage("./Lockout");
                        }
                        else
                        {
                            _logger.LogWarning("كلمة المرور غير صحيحة للمستخدم: {Email}", Input.Email);
                            ModelState.AddModelError(string.Empty, "كلمة المرور غير صحيحة. يرجى التحقق من كلمة المرور والمحاولة مرة أخرى.");
                            return Page();
                        }
                    }
                    else
                    {
                        _logger.LogWarning("لم يتم العثور على المستخدم بالبريد الإلكتروني: {Email}", Input.Email);
                        ModelState.AddModelError(string.Empty, "البريد الإلكتروني غير مسجل في النظام. يرجى التحقق من البريد الإلكتروني أو إنشاء حساب جديد.");
                        return Page();
                    }

                    // تم نقل هذا الكود إلى الأعلى
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "حدث خطأ أثناء محاولة تسجيل الدخول: {Message}", ex.Message);
                    ModelState.AddModelError(string.Empty, "حدث خطأ أثناء محاولة تسجيل الدخول. يرجى المحاولة مرة أخرى.");
                    return Page();
                }
            }

            // If we got this far, something failed, redisplay form
            _logger.LogWarning("نموذج تسجيل الدخول غير صالح");
            foreach (var error in ModelState.Values.SelectMany(v => v.Errors))
            {
                _logger.LogWarning("خطأ في نموذج تسجيل الدخول: {Error}", error.ErrorMessage);
            }
            return Page();
        }
    }
}
