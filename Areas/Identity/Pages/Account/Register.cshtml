@page
@model Abayat.Areas.Identity.Pages.Account.RegisterModel
@{
    ViewData["Title"] = "تسجيل حساب جديد";
}

<div class="auth-container fade-in">
    <h2>@ViewData["Title"]</h2>

    <form id="registerForm" asp-route-returnUrl="@Model.ReturnUrl" method="post">
        <div asp-validation-summary="ModelOnly" class="validation-summary-errors"></div>

        <div class="mb-3">
            <label asp-for="Input.Email" class="form-label">البريد الإلكتروني</label>
            <input asp-for="Input.Email" class="form-control" autocomplete="username" aria-required="true" />
            <span asp-validation-for="Input.Email" class="field-validation-error"></span>
        </div>

        <div class="mb-3">
            <label asp-for="Input.Password" class="form-label">كلمة المرور</label>
            <input asp-for="Input.Password" class="form-control" type="password" autocomplete="new-password" aria-required="true" />
            <span asp-validation-for="Input.Password" class="field-validation-error"></span>
        </div>

        <div class="mb-3">
            <label asp-for="Input.ConfirmPassword" class="form-label">تأكيد كلمة المرور</label>
            <input asp-for="Input.ConfirmPassword" class="form-control" type="password" autocomplete="new-password" aria-required="true" />
            <span asp-validation-for="Input.ConfirmPassword" class="field-validation-error"></span>
        </div>

        <div class="mb-3">
            <label asp-for="Input.Name" class="form-label">الاسم</label>
            <input asp-for="Input.Name" class="form-control" aria-required="true" />
            <span asp-validation-for="Input.Name" class="field-validation-error"></span>
        </div>

        <div class="mb-3">
            <label asp-for="Input.PhoneNumber" class="form-label">رقم الهاتف</label>
            <input asp-for="Input.PhoneNumber" class="form-control" aria-required="true" />
            <span asp-validation-for="Input.PhoneNumber" class="field-validation-error"></span>
        </div>

        <button id="registerSubmit" type="submit" class="w-100 btn btn-primary btn-lg">تسجيل</button>
    </form>

    <div class="mt-4 text-center">
        <p>لديك حساب بالفعل؟ <a asp-page="./Login" asp-route-returnUrl="@Model.ReturnUrl">تسجيل الدخول</a></p>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
