using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;
using Abayat.Models;
using Abayat.Data;
using Microsoft.EntityFrameworkCore;

namespace Abayat.Controllers
{
    public class CompareController : Controller
    {
        private readonly ApplicationDbContext _context;

        public CompareController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Compare/Index
        public IActionResult Index()
        {
            return View();
        }

        // GET: Compare/GetCompareItemsCount
        [HttpGet]
        public IActionResult GetCompareItemsCount()
        {
            var compareJson = Request.Cookies["compareList"];
            var compareItems = new List<int>();

            if (!string.IsNullOrEmpty(compareJson))
            {
                try
                {
                    compareItems = System.Text.Json.JsonSerializer.Deserialize<List<int>>(compareJson);
                }
                catch
                {
                    // Return empty list in case of error
                    return Json(new { count = 0 });
                }
            }

            return Json(new { count = compareItems.Count });
        }

        // GET: Compare/GetCompareItems
        [HttpGet]
        public IActionResult GetCompareItems()
        {
            // استرجاع قائمة المقارنة من cookie
            var compareJson = Request.Cookies["compareList"];
            var compareItems = new List<int>();

            if (!string.IsNullOrEmpty(compareJson))
            {
                try
                {
                    compareItems = System.Text.Json.JsonSerializer.Deserialize<List<int>>(compareJson);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"خطأ في تحليل قائمة المقارنة في GetCompareItems: {ex.Message}");
                }
            }
            else
            {
                Console.WriteLine("لم يتم العثور على ملف تعريف ارتباط للمقارنة في GetCompareItems");
            }

            return Json(new { items = compareItems });
        }

        // POST: Compare/AddToCompare
        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult AddToCompare(int productId)
        {
            Console.WriteLine($"تم استلام طلب AddToCompare للمنتج: {productId}");
            Console.WriteLine($"هل الطلب AJAX؟ {Request.Headers["X-Requested-With"]}");

            // التحقق من توفر المنتج قبل إضافته للمقارنة
            var product = _context.Products.FirstOrDefault(p => p.Id == productId);

            if (product == null)
            {
                if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                {
                    return Json(new { success = false, message = "المنتج غير موجود" });
                }

                TempData["ErrorMessage"] = "المنتج غير موجود";
                string refUrl = Request.Headers["Referer"].ToString();
                return Redirect(!string.IsNullOrEmpty(refUrl) ? refUrl : "/Products");
            }

            // استرجاع قائمة المقارنة من cookie
            var compareJson = Request.Cookies["compareList"];
            var compareItems = new List<int>();

            if (!string.IsNullOrEmpty(compareJson))
            {
                try
                {
                    compareItems = System.Text.Json.JsonSerializer.Deserialize<List<int>>(compareJson);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"خطأ في تحليل قائمة المقارنة: {ex.Message}");
                }
            }
            else
            {
                Console.WriteLine("لم يتم العثور على ملف تعريف ارتباط للمقارنة");
            }

            // التحقق مما إذا كان المنتج موجودًا بالفعل في المقارنة
            bool isInCompareList = compareItems?.Contains(productId) ?? false;
            bool success = false;
            string message = "";
            int removedId = -1;

            if (isInCompareList)
            {
                // إزالة المنتج من المقارنة
                compareItems.Remove(productId);
                message = "تمت إزالة المنتج من المقارنة";
                success = true;
            }
            else
            {
                // إضافة المنتج إلى المقارنة
                compareItems ??= new List<int>();

                // تحديد عدد المنتجات في المقارنة (4 كحد أقصى)
                if (compareItems.Count >= 4)
                {
                    // إزالة أول منتج من القائمة
                    removedId = compareItems[0];
                    compareItems.RemoveAt(0);
                    message = "تمت إضافة المنتج إلى المقارنة (تم إزالة منتج آخر لتجاوز الحد الأقصى)";
                }
                else
                {
                    message = "تمت إضافة المنتج إلى المقارنة";
                }

                compareItems.Add(productId);
                success = true;
            }

            // حفظ قائمة المقارنة المحدثة في cookie
            var cookieOptions = new CookieOptions
            {
                Expires = DateTime.Now.AddDays(30),
                HttpOnly = false, // السماح بالوصول من JavaScript
                SameSite = SameSiteMode.Lax,
                Secure = Request.IsHttps,
                Path = "/"
            };

            Response.Cookies.Append("compareList", System.Text.Json.JsonSerializer.Serialize(compareItems), cookieOptions);

            // إرجاع استجابة JSON للطلبات AJAX
            if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
            {
                return Json(new {
                    success = success,
                    message = message,
                    isInCompareList = !isInCompareList, // عكس الحالة السابقة
                    count = compareItems.Count,
                    removedId = removedId
                });
            }

            // إرجاع إلى الصفحة السابقة للطلبات العادية
            TempData[success ? "SuccessMessage" : "ErrorMessage"] = message;
            string backUrl = Request.Headers["Referer"].ToString();
            return Redirect(!string.IsNullOrEmpty(backUrl) ? backUrl : "/Products");
        }

        // POST: Compare/ClearCompare
        [HttpPost]
        public IActionResult ClearCompare()
        {
            try
            {
                // حذف ملف تعريف الارتباط للمقارنة
                Response.Cookies.Delete("compareList");

                // إرجاع استجابة نجاح للطلبات AJAX
                if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                {
                    return Json(new { success = true, message = "تم حذف جميع المنتجات من المقارنة" });
                }

                // إرجاع إلى الصفحة السابقة للطلبات العادية
                TempData["SuccessMessage"] = "تم حذف جميع المنتجات من المقارنة";
                return RedirectToAction("Index");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في حذف المقارنة: {ex.Message}");

                if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                {
                    return Json(new { success = false, message = "حدث خطأ أثناء حذف المقارنة" });
                }

                TempData["ErrorMessage"] = "حدث خطأ أثناء حذف المقارنة";
                return RedirectToAction("Index");
            }
        }
    }
}
