using System;
using System.IO;
using System.Threading.Tasks;
using Abayat.Data;
using Abayat.Models;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Abayat.Controllers
{
    public class SimpleCarouselController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly IWebHostEnvironment _hostEnvironment;
        private readonly ILogger<SimpleCarouselController> _logger;

        public SimpleCarouselController(
            ApplicationDbContext context,
            IWebHostEnvironment hostEnvironment,
            ILogger<SimpleCarouselController> logger)
        {
            _context = context;
            _hostEnvironment = hostEnvironment;
            _logger = logger;
        }

        public IActionResult Index()
        {
            return View();
        }

        [HttpPost]
        public async Task<IActionResult> Upload(IFormFile imageFile, string title, string description, string linkUrl, int displayOrder = 0, bool isActive = true)
        {
            try
            {
                // التحقق من وجود الملف
                if (imageFile == null || imageFile.Length == 0)
                {
                    return BadRequest("يرجى تحميل صورة");
                }

                // التحقق من البيانات الإلزامية
                if (string.IsNullOrEmpty(title))
                {
                    return BadRequest("يرجى إدخال عنوان الصورة");
                }

                // إنشاء مسار لحفظ الصورة
                string uploadsFolder = Path.Combine(_hostEnvironment.WebRootPath, "images", "carousel");
                _logger.LogInformation($"مسار حفظ الصورة: {uploadsFolder}");

                // التأكد من وجود المجلد
                if (!Directory.Exists(uploadsFolder))
                {
                    _logger.LogInformation("المجلد غير موجود، جاري إنشاؤه...");
                    Directory.CreateDirectory(uploadsFolder);
                }

                // إنشاء اسم فريد للملف
                string uniqueFileName = Guid.NewGuid().ToString() + "_" + Path.GetFileName(imageFile.FileName);
                string filePath = Path.Combine(uploadsFolder, uniqueFileName);
                _logger.LogInformation($"مسار الملف الكامل: {filePath}");

                // حفظ الملف
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    await imageFile.CopyToAsync(fileStream);
                    _logger.LogInformation("تم نسخ الملف بنجاح");
                }

                // إنشاء كائن CarouselImage جديد
                var carouselImage = new CarouselImage
                {
                    Title = title,
                    Description = description,
                    LinkUrl = linkUrl,
                    DisplayOrder = displayOrder,
                    IsActive = isActive,
                    ImageUrl = "/images/carousel/" + uniqueFileName,
                    CreatedAt = DateTime.Now
                };

                // إضافة الصورة إلى قاعدة البيانات
                _context.CarouselImages.Add(carouselImage);
                var result = await _context.SaveChangesAsync();
                _logger.LogInformation($"تم حفظ التغييرات في قاعدة البيانات. النتيجة: {result}");

                // إضافة رسالة نجاح
                TempData["SuccessMessage"] = "تمت إضافة الصورة بنجاح إلى الشريط المتحرك";

                // إرجاع استجابة نجاح
                return Ok(new { success = true, imageUrl = carouselImage.ImageUrl, id = carouselImage.Id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل صورة الشريط المتحرك");
                return StatusCode(500, $"حدث خطأ أثناء تحميل الصورة: {ex.Message}");
            }
        }
    }
}
