using System;
using System.IO;
using System.Threading.Tasks;
using Abayat.Data;
using Abayat.Models;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;

namespace Abayat.Controllers
{
    [Route("api/carousel")]
    [ApiController]
    public class CarouselApiController : ControllerBase
    {
        private readonly ApplicationDbContext _context;
        private readonly IWebHostEnvironment _hostEnvironment;
        private readonly ILogger<CarouselApiController> _logger;

        public CarouselApiController(
            ApplicationDbContext context,
            IWebHostEnvironment hostEnvironment,
            ILogger<CarouselApiController> logger)
        {
            _context = context;
            _hostEnvironment = hostEnvironment;
            _logger = logger;
        }

        [HttpPost]
        public async Task<IActionResult> UploadCarouselImage()
        {
            try
            {
                // التحقق من وجود الملف
                var imageFile = Request.Form.Files["imageFile"];
                if (imageFile == null || imageFile.Length == 0)
                {
                    _logger.LogWarning("لم يتم تحميل ملف صورة");
                    return BadRequest("يرجى تحميل صورة");
                }

                // الحصول على بيانات النموذج
                var title = Request.Form["title"].ToString();
                var description = Request.Form["description"].ToString();
                var linkUrl = Request.Form["linkUrl"].ToString();
                var displayOrderStr = Request.Form["displayOrder"].ToString();
                var isActiveStr = Request.Form["isActive"].ToString();

                // التحقق من البيانات الإلزامية
                if (string.IsNullOrEmpty(title))
                {
                    _logger.LogWarning("لم يتم إدخال عنوان الصورة");
                    return BadRequest("يرجى إدخال عنوان الصورة");
                }

                // تحويل البيانات
                int displayOrder = 0;
                if (!string.IsNullOrEmpty(displayOrderStr))
                {
                    int.TryParse(displayOrderStr, out displayOrder);
                }

                bool isActive = true;
                if (!string.IsNullOrEmpty(isActiveStr))
                {
                    isActive = isActiveStr.ToLower() == "on" || isActiveStr.ToLower() == "true";
                }

                // إنشاء مسار لحفظ الصورة
                string uploadsFolder = Path.Combine(_hostEnvironment.WebRootPath, "images", "carousel");
                _logger.LogInformation($"مسار حفظ الصورة: {uploadsFolder}");

                // التأكد من وجود المجلد
                if (!Directory.Exists(uploadsFolder))
                {
                    _logger.LogInformation("المجلد غير موجود، جاري إنشاؤه...");
                    Directory.CreateDirectory(uploadsFolder);
                }

                // إنشاء اسم فريد للملف
                string uniqueFileName = Guid.NewGuid().ToString() + "_" + Path.GetFileName(imageFile.FileName);
                string filePath = Path.Combine(uploadsFolder, uniqueFileName);
                _logger.LogInformation($"مسار الملف الكامل: {filePath}");

                // حفظ الملف
                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    await imageFile.CopyToAsync(fileStream);
                    _logger.LogInformation("تم نسخ الملف بنجاح");
                }

                // إنشاء كائن CarouselImage جديد
                var carouselImage = new CarouselImage
                {
                    Title = title,
                    Description = description,
                    LinkUrl = linkUrl,
                    DisplayOrder = displayOrder,
                    IsActive = isActive,
                    ImageUrl = "/images/carousel/" + uniqueFileName,
                    CreatedAt = DateTime.Now
                };

                // إضافة الصورة إلى قاعدة البيانات
                _context.CarouselImages.Add(carouselImage);
                var result = await _context.SaveChangesAsync();
                _logger.LogInformation($"تم حفظ التغييرات في قاعدة البيانات. النتيجة: {result}");

                // إرجاع استجابة نجاح
                return Ok(new { success = true, imageUrl = carouselImage.ImageUrl, id = carouselImage.Id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل صورة الشريط المتحرك");
                return StatusCode(500, $"حدث خطأ أثناء تحميل الصورة: {ex.Message}");
            }
        }
    }
}
