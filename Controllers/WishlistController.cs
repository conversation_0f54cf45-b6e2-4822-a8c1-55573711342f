using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Authorization;
using System.Security.Claims;
using Abayat.Models;
using Abayat.Data;
using Microsoft.EntityFrameworkCore;

namespace Abayat.Controllers
{
    public class WishlistController : Controller
    {
        private readonly ApplicationDbContext _context;

        public WishlistController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: Wishlist
        public IActionResult Index()
        {
            // استرجاع قائمة المفضلة من cookie
            var wishlistJson = Request.Cookies["wishlist"];
            var wishlistItems = new List<int>();

            if (!string.IsNullOrEmpty(wishlistJson))
            {
                try
                {
                    wishlistItems = System.Text.Json.JsonSerializer.Deserialize<List<int>>(wishlistJson);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"خطأ في تحليل قائمة المفضلة في Index: {ex.Message}");
                }
            }

            // الحصول على المنتجات المفضلة من قاعدة البيانات
            var products = new List<Product>();
            if (wishlistItems.Any())
            {
                products = _context.Products
                    .Where(p => wishlistItems.Contains(p.Id))
                    .ToList();
            }

            // إذا كان هناك تناقض بين عدد العناصر في القائمة وعدد المنتجات المسترجعة، قم بتحديث ملف تعريف الارتباط
            if (wishlistItems.Count != products.Count)
            {
                // تحديث قائمة المفضلة لتحتوي فقط على المنتجات الموجودة
                wishlistItems = products.Select(p => p.Id).ToList();

                // حفظ قائمة المفضلة المحدثة في cookie
                var cookieOptions = new CookieOptions
                {
                    Expires = DateTime.Now.AddDays(30),
                    HttpOnly = false, // السماح بالوصول من JavaScript
                    SameSite = SameSiteMode.Lax,
                    Secure = Request.IsHttps,
                    Path = "/"
                };

                Response.Cookies.Append("wishlist", System.Text.Json.JsonSerializer.Serialize(wishlistItems), cookieOptions);
            }

            return View(products);
        }

        // POST: Wishlist/FixWishlistCounter
        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult FixWishlistCounter(int actualCount)
        {
            try
            {
                // استرجاع قائمة المفضلة من cookie
                var wishlistJson = Request.Cookies["wishlist"];
                var wishlistItems = new List<int>();

                if (!string.IsNullOrEmpty(wishlistJson))
                {
                    try
                    {
                        wishlistItems = System.Text.Json.JsonSerializer.Deserialize<List<int>>(wishlistJson);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"خطأ في تحليل قائمة المفضلة في FixWishlistCounter: {ex.Message}");
                    }
                }

                // التحقق من وجود تناقض بين العداد الفعلي وعدد العناصر في القائمة
                if (wishlistItems.Count != actualCount)
                {
                    // إذا كانت القائمة فارغة ولكن العداد يشير إلى وجود عناصر، قم بإعادة تعيين الكوكي
                    if (wishlistItems.Count == 0 && actualCount > 0)
                    {
                        // في هذه الحالة، نحتاج إلى استرجاع المنتجات الفعلية من قاعدة البيانات
                        // ولكن بما أننا لا نعرف أي المنتجات كانت في المفضلة، سنقوم بإعادة تعيين الكوكي فقط
                    }
                    // إذا كان العداد يشير إلى عدم وجود عناصر ولكن القائمة تحتوي على عناصر، قم بتحديث العداد
                    else if (wishlistItems.Count > 0 && actualCount == 0)
                    {
                    }

                    // حفظ قائمة المفضلة المحدثة في cookie
                    var cookieOptions = new CookieOptions
                    {
                        Expires = DateTime.Now.AddDays(30),
                        HttpOnly = false, // السماح بالوصول من JavaScript
                        SameSite = SameSiteMode.Lax,
                        Secure = Request.IsHttps,
                        Path = "/"
                    };

                    Response.Cookies.Append("wishlist", System.Text.Json.JsonSerializer.Serialize(wishlistItems), cookieOptions);

                    return Json(new { success = true, message = "تم إصلاح عداد المفضلة بنجاح", count = wishlistItems.Count });
                }

                return Json(new { success = true, message = "لا يوجد تناقض في عداد المفضلة", count = wishlistItems.Count });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إصلاح عداد المفضلة: {ex.Message}");
                return Json(new { success = false, message = "حدث خطأ أثناء إصلاح عداد المفضلة" });
            }
        }

        // GET: Wishlist/GetWishlistItemsCount
        [HttpGet]
        public IActionResult GetWishlistItemsCount()
        {
            var wishlistJson = Request.Cookies["wishlist"];
            var wishlistItems = new List<int>();

            if (!string.IsNullOrEmpty(wishlistJson))
            {
                try
                {
                    wishlistItems = System.Text.Json.JsonSerializer.Deserialize<List<int>>(wishlistJson);
                }
                catch
                {
                    // Return empty list in case of error
                    return Json(new { count = 0 });
                }
            }

            return Json(new { count = wishlistItems.Count });
        }

        // GET: Wishlist/GetWishlistItems
        [HttpGet]
        public IActionResult GetWishlistItems()
        {
            try
            {
                // استرجاع قائمة المفضلة من cookie
                var wishlistJson = Request.Cookies["wishlist"];
                var wishlistItems = new List<int>();

                if (!string.IsNullOrEmpty(wishlistJson))
                {
                    try
                    {
                        wishlistItems = System.Text.Json.JsonSerializer.Deserialize<List<int>>(wishlistJson);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"خطأ في تحليل قائمة المفضلة في GetWishlistItems: {ex.Message}");
                    }
                }

                // الحصول على تفاصيل المنتجات المفضلة من قاعدة البيانات
                var products = new List<object>();
                if (wishlistItems.Any())
                {
                    products = _context.Products
                        .Where(p => wishlistItems.Contains(p.Id) && p.IsAvailable)
                        .Select(p => new
                        {
                            Id = p.Id,
                            Name = p.Name,
                            Price = p.Price,
                            ImageUrl = p.ImageUrl ?? "/images/placeholder.jpg",
                            IsAvailable = p.IsAvailable
                        })
                        .ToList<object>();
                }

                return Json(new {
                    success = true,
                    items = products,
                    count = products.Count
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في GetWishlistItems: {ex.Message}");
                return Json(new {
                    success = false,
                    message = "حدث خطأ أثناء تحميل المنتجات المفضلة",
                    items = new List<object>(),
                    count = 0
                });
            }
        }

        // POST: Wishlist/AddToWishlist
        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult AddToWishlist(int productId)
        {
            // التحقق من توفر المنتج قبل إضافته للمفضلة
            var product = _context.Products.FirstOrDefault(p => p.Id == productId);

            if (product == null)
            {
                if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                {
                    return Json(new { success = false, message = "المنتج غير موجود" });
                }

                TempData["ErrorMessage"] = "المنتج غير موجود";
                string refUrl = Request.Headers["Referer"].ToString();
                return Redirect(!string.IsNullOrEmpty(refUrl) ? refUrl : "/Products");
            }

            // استرجاع قائمة المفضلة من cookie
            var wishlistJson = Request.Cookies["wishlist"];
            var wishlistItems = new List<int>();

            if (!string.IsNullOrEmpty(wishlistJson))
            {
                try
                {
                    wishlistItems = System.Text.Json.JsonSerializer.Deserialize<List<int>>(wishlistJson);
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"خطأ في تحليل قائمة المفضلة: {ex.Message}");
                }
            }

            // التحقق مما إذا كان المنتج موجودًا بالفعل في المفضلة
            bool isInWishlist = wishlistItems?.Contains(productId) ?? false;
            bool success = false;
            string message = "";

            if (isInWishlist)
            {
                // إزالة المنتج من المفضلة
                wishlistItems.Remove(productId);
                message = "تمت إزالة المنتج من المفضلة";
                success = true;
            }
            else
            {
                // إضافة المنتج إلى المفضلة
                wishlistItems ??= new List<int>();
                wishlistItems.Add(productId);
                message = "تمت إضافة المنتج إلى المفضلة";
                success = true;
            }

            // حفظ قائمة المفضلة المحدثة في cookie
            var cookieOptions = new CookieOptions
            {
                Expires = DateTime.Now.AddDays(30),
                HttpOnly = false, // السماح بالوصول من JavaScript
                SameSite = SameSiteMode.Lax,
                Secure = Request.IsHttps,
                Path = "/"
            };

            Response.Cookies.Append("wishlist", System.Text.Json.JsonSerializer.Serialize(wishlistItems), cookieOptions);

            // إرجاع استجابة JSON للطلبات AJAX
            if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
            {
                return Json(new {
                    success = success,
                    message = message,
                    isInWishlist = !isInWishlist, // الحالة الجديدة بعد التغيير
                    count = wishlistItems?.Count ?? 0,
                    items = wishlistItems
                });
            }

            // إرجاع إلى الصفحة السابقة للطلبات العادية
            TempData[success ? "SuccessMessage" : "ErrorMessage"] = message;
            string backUrl = Request.Headers["Referer"].ToString();
            return Redirect(!string.IsNullOrEmpty(backUrl) ? backUrl : "/Products");
        }

        // POST: Wishlist/RemoveFromWishlist
        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult RemoveFromWishlist(int productId)
        {
            try
            {
                // استرجاع قائمة المفضلة من cookie
                var wishlistJson = Request.Cookies["wishlist"];
                var wishlistItems = new List<int>();

                if (!string.IsNullOrEmpty(wishlistJson))
                {
                    try
                    {
                        wishlistItems = System.Text.Json.JsonSerializer.Deserialize<List<int>>(wishlistJson);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"خطأ في تحليل قائمة المفضلة في RemoveFromWishlist: {ex.Message}");
                    }
                }

                // إزالة المنتج من المفضلة
                bool removed = wishlistItems.Remove(productId);
                string message = removed ? "تمت إزالة المنتج من المفضلة" : "المنتج غير موجود في المفضلة";

                // حفظ قائمة المفضلة المحدثة في cookie
                var cookieOptions = new CookieOptions
                {
                    Expires = DateTime.Now.AddDays(30),
                    HttpOnly = false,
                    SameSite = SameSiteMode.Lax,
                    Secure = Request.IsHttps,
                    Path = "/"
                };

                Response.Cookies.Append("wishlist", System.Text.Json.JsonSerializer.Serialize(wishlistItems), cookieOptions);

                // إرجاع استجابة JSON للطلبات AJAX
                if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                {
                    return Json(new {
                        success = true,
                        message = message,
                        count = wishlistItems.Count,
                        items = wishlistItems
                    });
                }

                // إرجاع إلى الصفحة السابقة للطلبات العادية
                TempData["SuccessMessage"] = message;
                string backUrl = Request.Headers["Referer"].ToString();
                return Redirect(!string.IsNullOrEmpty(backUrl) ? backUrl : "/Wishlist");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في RemoveFromWishlist: {ex.Message}");

                if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                {
                    return Json(new { success = false, message = "حدث خطأ أثناء إزالة المنتج من المفضلة" });
                }

                TempData["ErrorMessage"] = "حدث خطأ أثناء إزالة المنتج من المفضلة";
                string backUrl = Request.Headers["Referer"].ToString();
                return Redirect(!string.IsNullOrEmpty(backUrl) ? backUrl : "/Wishlist");
            }
        }
    }
}
