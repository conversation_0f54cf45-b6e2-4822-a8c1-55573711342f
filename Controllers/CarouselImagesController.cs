using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Abayat.Data;
using Abayat.Models;
using Microsoft.AspNetCore.Authorization;
using System.IO;
using Microsoft.AspNetCore.Hosting;

namespace Abayat.Controllers
{
    [Authorize(Roles = "Admin")]
    public class CarouselImagesController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly IWebHostEnvironment _hostEnvironment;

        public CarouselImagesController(ApplicationDbContext context, IWebHostEnvironment hostEnvironment)
        {
            _context = context;
            _hostEnvironment = hostEnvironment;
        }

        // GET: CarouselImages
        public async Task<IActionResult> Index()
        {
            return View(await _context.CarouselImages.OrderBy(c => c.DisplayOrder).ToListAsync());
        }

        // GET: CarouselImages/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var carouselImage = await _context.CarouselImages
                .FirstOrDefaultAsync(m => m.Id == id);
            if (carouselImage == null)
            {
                return NotFound();
            }

            return View(carouselImage);
        }

        // GET: CarouselImages/Create
        public IActionResult Create()
        {
            return View();
        }

        // POST: CarouselImages/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create([Bind("Id,Title,Description,LinkUrl,DisplayOrder,IsActive")] CarouselImage carouselImage, IFormFile imageFile)
        {
            try
            {
                // طباعة معلومات تشخيصية
                Console.WriteLine($"بدء عملية إضافة صورة جديدة للشريط المتحرك: {carouselImage.Title}");

                // التحقق من وجود الملف
                if (imageFile == null || imageFile.Length == 0)
                {
                    Console.WriteLine("لم يتم تحميل ملف صورة");
                    ModelState.AddModelError("imageFile", "يرجى تحميل صورة");
                    return View(carouselImage);
                }

                Console.WriteLine($"تم استلام ملف الصورة بحجم: {imageFile.Length} بايت");

                try
                {
                    // إنشاء مسار لحفظ الصورة
                    string uploadsFolder = Path.Combine(_hostEnvironment.WebRootPath, "images", "carousel");
                    Console.WriteLine($"مسار حفظ الصورة: {uploadsFolder}");

                    // التأكد من وجود المجلد
                    if (!Directory.Exists(uploadsFolder))
                    {
                        Console.WriteLine("المجلد غير موجود، جاري إنشاؤه...");
                        Directory.CreateDirectory(uploadsFolder);
                    }

                    // إنشاء اسم فريد للملف
                    string uniqueFileName = Guid.NewGuid().ToString() + "_" + Path.GetFileName(imageFile.FileName);
                    string filePath = Path.Combine(uploadsFolder, uniqueFileName);
                    Console.WriteLine($"مسار الملف الكامل: {filePath}");

                    // حفظ الملف
                    using (var fileStream = new FileStream(filePath, FileMode.Create))
                    {
                        await imageFile.CopyToAsync(fileStream);
                        Console.WriteLine("تم نسخ الملف بنجاح");
                    }

                    // تحديث مسار الصورة في النموذج
                    carouselImage.ImageUrl = "/images/carousel/" + uniqueFileName;
                    Console.WriteLine($"تم تعيين مسار الصورة: {carouselImage.ImageUrl}");

                    // تعيين تاريخ الإنشاء
                    carouselImage.CreatedAt = DateTime.Now;

                    // إضافة الصورة إلى قاعدة البيانات
                    _context.CarouselImages.Add(carouselImage);
                    Console.WriteLine("تمت إضافة الصورة إلى السياق");

                    var result = await _context.SaveChangesAsync();
                    Console.WriteLine($"تم حفظ التغييرات في قاعدة البيانات. النتيجة: {result}");

                    // إضافة رسالة نجاح
                    TempData["SuccessMessage"] = "تمت إضافة الصورة بنجاح إلى الشريط المتحرك";

                    // التحقق مما إذا كان الطلب من AJAX
                    if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
                    {
                        return Json(new { success = true, redirectUrl = Url.Action("Index") });
                    }

                    return RedirectToAction(nameof(Index));
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"خطأ: {ex.Message}");
                    Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");

                    if (ex.InnerException != null)
                    {
                        Console.WriteLine($"الخطأ الداخلي: {ex.InnerException.Message}");
                    }

                    ModelState.AddModelError("", $"حدث خطأ أثناء حفظ الصورة: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                // إضافة رسالة خطأ
                ModelState.AddModelError("", $"حدث خطأ أثناء حفظ الصورة: {ex.Message}");
                // تسجيل الخطأ في السجل
                Console.WriteLine($"خطأ في إضافة صورة الشريط المتحرك: {ex.Message}");
                Console.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");

                if (ex.InnerException != null)
                {
                    Console.WriteLine($"الخطأ الداخلي: {ex.InnerException.Message}");
                }
            }

            // التحقق مما إذا كان الطلب من AJAX
            if (Request.Headers["X-Requested-With"] == "XMLHttpRequest")
            {
                return Json(new { success = false, error = "حدث خطأ أثناء حفظ الصورة" });
            }

            return View(carouselImage);
        }

        // GET: CarouselImages/Edit/5
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var carouselImage = await _context.CarouselImages.FindAsync(id);
            if (carouselImage == null)
            {
                return NotFound();
            }
            return View(carouselImage);
        }

        // POST: CarouselImages/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Title,Description,ImageUrl,LinkUrl,DisplayOrder,IsActive,CreatedAt")] CarouselImage carouselImage, IFormFile imageFile)
        {
            if (id != carouselImage.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    // إذا تم تحميل ملف صورة جديد
                    if (imageFile != null && imageFile.Length > 0)
                    {
                        // إنشاء مسار لحفظ الصورة
                        string uploadsFolder = Path.Combine(_hostEnvironment.WebRootPath, "images", "carousel");

                        // التأكد من وجود المجلد
                        if (!Directory.Exists(uploadsFolder))
                        {
                            Directory.CreateDirectory(uploadsFolder);
                        }

                        // إنشاء اسم فريد للملف
                        string uniqueFileName = Guid.NewGuid().ToString() + "_" + imageFile.FileName;
                        string filePath = Path.Combine(uploadsFolder, uniqueFileName);

                        // حفظ الملف
                        using (var fileStream = new FileStream(filePath, FileMode.Create))
                        {
                            await imageFile.CopyToAsync(fileStream);
                        }

                        // حذف الصورة القديمة إذا كانت موجودة
                        if (!string.IsNullOrEmpty(carouselImage.ImageUrl))
                        {
                            string oldImagePath = Path.Combine(_hostEnvironment.WebRootPath, carouselImage.ImageUrl.TrimStart('/'));
                            if (System.IO.File.Exists(oldImagePath))
                            {
                                System.IO.File.Delete(oldImagePath);
                            }
                        }

                        // تحديث مسار الصورة في النموذج
                        carouselImage.ImageUrl = "/images/carousel/" + uniqueFileName;
                    }

                    _context.Update(carouselImage);
                    await _context.SaveChangesAsync();
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!CarouselImageExists(carouselImage.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                return RedirectToAction(nameof(Index));
            }
            return View(carouselImage);
        }

        // GET: CarouselImages/Delete/5
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var carouselImage = await _context.CarouselImages
                .FirstOrDefaultAsync(m => m.Id == id);
            if (carouselImage == null)
            {
                return NotFound();
            }

            return View(carouselImage);
        }

        // POST: CarouselImages/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var carouselImage = await _context.CarouselImages.FindAsync(id);

            // حذف الصورة من المجلد
            if (carouselImage != null && !string.IsNullOrEmpty(carouselImage.ImageUrl))
            {
                string imagePath = Path.Combine(_hostEnvironment.WebRootPath, carouselImage.ImageUrl.TrimStart('/'));
                if (System.IO.File.Exists(imagePath))
                {
                    System.IO.File.Delete(imagePath);
                }
            }

            if (carouselImage != null)
            {
                _context.CarouselImages.Remove(carouselImage);
            }

            await _context.SaveChangesAsync();
            return RedirectToAction(nameof(Index));
        }

        private bool CarouselImageExists(int id)
        {
            return _context.CarouselImages.Any(e => e.Id == id);
        }
    }
}
