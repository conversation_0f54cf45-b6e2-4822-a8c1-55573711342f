using Microsoft.AspNetCore.Mvc;
using System;
using System.Collections.Generic;

namespace Abayat.Controllers
{
    public class CookiesController : Controller
    {
        // GET: Cookies/ClearAll
        [HttpGet]
        public IActionResult ClearAll()
        {
            // حذف ملف تعريف ارتباط المفضلة
            Response.Cookies.Delete("wishlist");
            
            // حذف ملف تعريف ارتباط المقارنة
            Response.Cookies.Delete("compareList");
            
            // حذف ملف تعريف ارتباط السلة (إذا كان موجودًا)
            Response.Cookies.Delete("cart");
            
            // إضافة رسالة نجاح
            TempData["SuccessMessage"] = "تم حذف جميع ملفات تعريف الارتباط بنجاح";
            
            // العودة إلى الصفحة السابقة أو الصفحة الرئيسية
            string backUrl = Request.Headers["Referer"].ToString();
            return Redirect(!string.IsNullOrEmpty(backUrl) ? backUrl : "/");
        }
        
        // GET: Cookies/ClearWishlist
        [HttpGet]
        public IActionResult ClearWishlist()
        {
            // حذف ملف تعريف ارتباط المفضلة فقط
            Response.Cookies.Delete("wishlist");
            
            // إضافة رسالة نجاح
            TempData["SuccessMessage"] = "تم حذف قائمة المفضلة بنجاح";
            
            // العودة إلى الصفحة السابقة أو الصفحة الرئيسية
            string backUrl = Request.Headers["Referer"].ToString();
            return Redirect(!string.IsNullOrEmpty(backUrl) ? backUrl : "/");
        }
        
        // GET: Cookies/ClearCompare
        [HttpGet]
        public IActionResult ClearCompare()
        {
            // حذف ملف تعريف ارتباط المقارنة فقط
            Response.Cookies.Delete("compareList");
            
            // إضافة رسالة نجاح
            TempData["SuccessMessage"] = "تم حذف قائمة المقارنة بنجاح";
            
            // العودة إلى الصفحة السابقة أو الصفحة الرئيسية
            string backUrl = Request.Headers["Referer"].ToString();
            return Redirect(!string.IsNullOrEmpty(backUrl) ? backUrl : "/");
        }
        
        // POST: Cookies/ClearAll (للاستخدام مع نماذج HTML)
        [HttpPost]
        [ValidateAntiForgeryToken]
        public IActionResult ClearAllPost()
        {
            // حذف ملفات تعريف الارتباط
            Response.Cookies.Delete("wishlist");
            Response.Cookies.Delete("compareList");
            Response.Cookies.Delete("cart");
            
            // إضافة رسالة نجاح
            TempData["SuccessMessage"] = "تم حذف جميع ملفات تعريف الارتباط بنجاح";
            
            // العودة إلى الصفحة السابقة أو الصفحة الرئيسية
            string backUrl = Request.Headers["Referer"].ToString();
            return Redirect(!string.IsNullOrEmpty(backUrl) ? backUrl : "/");
        }
    }
}
