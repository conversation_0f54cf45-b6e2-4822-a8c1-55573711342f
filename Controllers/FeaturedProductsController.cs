using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Abayat.Data;
using Abayat.Models;

namespace Abayat.Controllers
{
    [Authorize(Roles = "Admin")]
    public class FeaturedProductsController : Controller
    {
        private readonly ApplicationDbContext _context;

        public FeaturedProductsController(ApplicationDbContext context)
        {
            _context = context;
        }

        // GET: FeaturedProducts
        public async Task<IActionResult> Index()
        {
            var featuredProducts = await _context.FeaturedProducts
                .Include(fp => fp.Product)
                .OrderBy(fp => fp.DisplayOrder)
                .ToListAsync();

            return View(featuredProducts);
        }

        // GET: FeaturedProducts/Create
        public async Task<IActionResult> Create()
        {
            // الحصول على المنتجات المتاحة التي ليست مميزة بالفعل
            var availableProducts = await _context.Products
                .Where(p => p.IsAvailable && !_context.FeaturedProducts.Any(fp => fp.ProductId == p.Id))
                .OrderBy(p => p.Name)
                .ToListAsync();

            ViewBag.AvailableProducts = availableProducts;
            return View();
        }

        // POST: FeaturedProducts/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(int productId)
        {
            try
            {
                // التحقق من أن المنتج غير مميز بالفعل
                var existingFeatured = await _context.FeaturedProducts
                    .FirstOrDefaultAsync(fp => fp.ProductId == productId);

                if (existingFeatured != null)
                {
                    TempData["Error"] = "هذا المنتج مميز بالفعل";
                    return RedirectToAction(nameof(Index));
                }

                // الحصول على أعلى ترتيب عرض
                var maxDisplayOrder = await _context.FeaturedProducts
                    .MaxAsync(fp => (int?)fp.DisplayOrder) ?? 0;

                var featuredProduct = new FeaturedProduct
                {
                    ProductId = productId,
                    DisplayOrder = maxDisplayOrder + 1,
                    CreatedAt = DateTime.Now
                };

                _context.FeaturedProducts.Add(featuredProduct);
                await _context.SaveChangesAsync();

                TempData["Success"] = "تم إضافة المنتج للمنتجات المميزة بنجاح";
                return RedirectToAction(nameof(Index));
            }
            catch (Exception ex)
            {
                TempData["Error"] = "حدث خطأ أثناء إضافة المنتج";
                return RedirectToAction(nameof(Index));
            }
        }

        // POST: FeaturedProducts/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Delete(int id)
        {
            try
            {
                var featuredProduct = await _context.FeaturedProducts.FindAsync(id);
                if (featuredProduct != null)
                {
                    _context.FeaturedProducts.Remove(featuredProduct);
                    await _context.SaveChangesAsync();
                    TempData["Success"] = "تم حذف المنتج من المنتجات المميزة بنجاح";
                }
                else
                {
                    TempData["Error"] = "المنتج غير موجود";
                }
            }
            catch (Exception ex)
            {
                TempData["Error"] = "حدث خطأ أثناء حذف المنتج";
            }

            return RedirectToAction(nameof(Index));
        }

        // POST: FeaturedProducts/UpdateOrder
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateOrder(int id, int newOrder)
        {
            try
            {
                var featuredProduct = await _context.FeaturedProducts.FindAsync(id);
                if (featuredProduct != null)
                {
                    featuredProduct.DisplayOrder = newOrder;
                    await _context.SaveChangesAsync();
                    return Json(new { success = true });
                }
                return Json(new { success = false, message = "المنتج غير موجود" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "حدث خطأ أثناء تحديث الترتيب" });
            }
        }

        // POST: FeaturedProducts/AutoPopulate
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> AutoPopulate()
        {
            try
            {
                // حذف المنتجات المميزة الحالية
                var existingFeatured = await _context.FeaturedProducts.ToListAsync();
                _context.FeaturedProducts.RemoveRange(existingFeatured);

                // إضافة أول 6 منتجات متاحة كمنتجات مميزة
                var products = await _context.Products
                    .Where(p => p.IsAvailable)
                    .OrderBy(p => p.Id)
                    .Take(6)
                    .ToListAsync();

                for (int i = 0; i < products.Count; i++)
                {
                    var featuredProduct = new FeaturedProduct
                    {
                        ProductId = products[i].Id,
                        DisplayOrder = i + 1,
                        CreatedAt = DateTime.Now
                    };
                    _context.FeaturedProducts.Add(featuredProduct);
                }

                await _context.SaveChangesAsync();
                TempData["Success"] = $"تم إضافة {products.Count} منتجات مميزة تلقائياً";
            }
            catch (Exception ex)
            {
                TempData["Error"] = "حدث خطأ أثناء الإضافة التلقائية";
            }

            return RedirectToAction(nameof(Index));
        }
    }
}
