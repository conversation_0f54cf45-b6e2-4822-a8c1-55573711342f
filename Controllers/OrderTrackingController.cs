using Microsoft.AspNetCore.Mvc;
using Abayat.Services;
using Abayat.Models;
using Abayat.ViewModels;
using System.Threading.Tasks;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Abayat.Data;
using System.Diagnostics;

namespace Abayat.Controllers
{
    public class OrderTrackingController : Controller
    {
        private readonly IOrderService _orderService;
        private readonly ApplicationDbContext _context;

        public OrderTrackingController(IOrderService orderService, ApplicationDbContext context)
        {
            _orderService = orderService;
            _context = context;
        }

        // GET: OrderTracking
        public IActionResult Index()
        {
            return View();
        }

        // POST: OrderTracking/Track
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Track(OrderTrackingViewModel model)
        {
            if (!ModelState.IsValid)
            {
                return View("Index", model);
            }

            try
            {
                Order? order = null;

                // البحث حسب رقم الطلب
                if (model.OrderId.HasValue && model.OrderId > 0)
                {
                    order = await _context.Orders
                        .Include(o => o.OrderItems!)
                        .ThenInclude(i => i.Product)
                        .FirstOrDefaultAsync(o => o.Id == model.OrderId);

                    // إذا تم إدخال رقم هاتف أيضًا، تحقق من تطابقه مع الطلب
                    if (order != null && !string.IsNullOrWhiteSpace(model.PhoneNumber) && order.PhoneNumber != model.PhoneNumber)
                    {
                        TempData["ErrorMessage"] = "رقم الهاتف غير متطابق مع الطلب";
                        return View("Index", model);
                    }
                }
                // البحث حسب رقم الهاتف
                else if (!string.IsNullOrWhiteSpace(model.PhoneNumber))
                {
                    order = await _context.Orders
                        .Include(o => o.OrderItems!)
                        .ThenInclude(i => i.Product)
                        .Where(o => o.PhoneNumber == model.PhoneNumber)
                        .OrderByDescending(o => o.OrderDate)
                        .FirstOrDefaultAsync();
                }

                if (order == null)
                {
                    TempData["ErrorMessage"] = "لم يتم العثور على طلب بالمعلومات المدخلة";
                    return View("Index", model);
                }

                return View("TrackingResult", order);
            }
            catch (System.Exception ex)
            {
                TempData["ErrorMessage"] = "حدث خطأ أثناء البحث عن الطلب";
                return View("Index", model);
            }
        }
    }
}
