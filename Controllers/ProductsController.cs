using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using Abayat.Data;
using Abayat.Models;
using Microsoft.AspNetCore.Authorization;
using System.IO;
using Microsoft.AspNetCore.Hosting;

namespace Abayat.Controllers
{
    public class ProductsController : Controller
    {
        private readonly ApplicationDbContext _context;
        private readonly IWebHostEnvironment _hostEnvironment;

        public ProductsController(ApplicationDbContext context, IWebHostEnvironment hostEnvironment)
        {
            _context = context;
            _hostEnvironment = hostEnvironment;
        }

        // GET: Products
        public async Task<IActionResult> Index(string? category, int? pageNumber, int? pageSize)
        {
            IQueryable<Product> productsQuery = _context.Products.Include(p => p.Category);

            if (!string.IsNullOrEmpty(category))
            {
                productsQuery = productsQuery.Where(p => p.Category.Slug == category);
            }

            // تحديد حجم الصفحة
            int actualPageSize = pageSize ?? 16; // القيمة الافتراضية هي 16 منتج في الصفحة
            int actualPageNumber = pageNumber ?? 1; // القيمة الافتراضية هي الصفحة الأولى

            var products = await PaginatedList<Product>.CreateAsync(productsQuery, actualPageNumber, actualPageSize);
            ViewData["Categories"] = await _context.Categories.OrderBy(c => c.DisplayOrder).ToListAsync();
            ViewData["SelectedCategory"] = category;
            ViewData["PageSize"] = actualPageSize;

            return View(products);
        }

        // GET: Products/Details/5
        public async Task<IActionResult> Details(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var product = await _context.Products
                .Include(p => p.Category)
                .FirstOrDefaultAsync(m => m.Id == id);
            if (product == null)
            {
                return NotFound();
            }

            return View(product);
        }

        // GET: Products/TestPrices - صفحة اختبار الأسعار
        public async Task<IActionResult> TestPrices()
        {
            var products = await _context.Products
                .Where(p => p.DiscountPercentage > 0)
                .OrderBy(p => p.Name)
                .ToListAsync();

            return View(products);
        }

        // GET: Products/Create
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> Create()
        {
            ViewData["Categories"] = new SelectList(await _context.Categories.ToListAsync(), "Id", "Name");
            return View();
        }

        // POST: Products/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> Create([Bind("Id,Name,Description,Price,DiscountPercentage,ImageUrl,IsAvailable,ShowInCarousel,CategoryId,ProductType,CreatedAt")] Product product, IFormFile? imageFile)
        {
            if (ModelState.IsValid)
            {
                // Handle image upload
                if (imageFile != null && imageFile.Length > 0)
                {
                    string uploadsFolder = Path.Combine(_hostEnvironment.WebRootPath, "images", "products");

                    // التأكد من وجود المجلد
                    if (!Directory.Exists(uploadsFolder))
                    {
                        Directory.CreateDirectory(uploadsFolder);
                    }

                    string uniqueFileName = Guid.NewGuid().ToString() + "_" + imageFile.FileName;
                    string filePath = Path.Combine(uploadsFolder, uniqueFileName);

                    using (var fileStream = new FileStream(filePath, FileMode.Create))
                    {
                        await imageFile.CopyToAsync(fileStream);
                    }

                    product.ImageUrl = "/images/products/" + uniqueFileName;
                }

                product.CreatedAt = DateTime.Now;
                _context.Add(product);
                await _context.SaveChangesAsync();
                TempData["SuccessMessage"] = "تمت إضافة المنتج بنجاح";
                return RedirectToAction(nameof(Index));
            }

            ViewData["Categories"] = new SelectList(await _context.Categories.ToListAsync(), "Id", "Name", product.CategoryId);
            return View(product);
        }

        // GET: Products/Edit/5
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> Edit(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var product = await _context.Products.FindAsync(id);
            if (product == null)
            {
                return NotFound();
            }

            ViewData["Categories"] = new SelectList(await _context.Categories.ToListAsync(), "Id", "Name", product.CategoryId);
            return View(product);
        }

        // POST: Products/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> Edit(int id, [Bind("Id,Name,Description,Price,DiscountPercentage,ImageUrl,IsAvailable,ShowInCarousel,CategoryId,ProductType,CreatedAt")] Product product, IFormFile? imageFile)
        {
            if (id != product.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                try
                {
                    // Handle image upload
                    if (imageFile != null && imageFile.Length > 0)
                    {
                        string uploadsFolder = Path.Combine(_hostEnvironment.WebRootPath, "images", "products");

                        // التأكد من وجود المجلد
                        if (!Directory.Exists(uploadsFolder))
                        {
                            Directory.CreateDirectory(uploadsFolder);
                        }

                        string uniqueFileName = Guid.NewGuid().ToString() + "_" + imageFile.FileName;
                        string filePath = Path.Combine(uploadsFolder, uniqueFileName);

                        using (var fileStream = new FileStream(filePath, FileMode.Create))
                        {
                            await imageFile.CopyToAsync(fileStream);
                        }

                        // Delete old image if exists
                        if (!string.IsNullOrEmpty(product.ImageUrl))
                        {
                            string oldImagePath = Path.Combine(_hostEnvironment.WebRootPath, product.ImageUrl.TrimStart('/'));
                            if (System.IO.File.Exists(oldImagePath))
                            {
                                System.IO.File.Delete(oldImagePath);
                            }
                        }

                        product.ImageUrl = "/images/products/" + uniqueFileName;
                    }

                    _context.Update(product);
                    await _context.SaveChangesAsync();
                    TempData["SuccessMessage"] = "تم تحديث المنتج بنجاح";
                }
                catch (DbUpdateConcurrencyException)
                {
                    if (!ProductExists(product.Id))
                    {
                        return NotFound();
                    }
                    else
                    {
                        throw;
                    }
                }
                catch (Exception ex)
                {
                    ModelState.AddModelError("", $"حدث خطأ أثناء حفظ التغييرات: {ex.Message}");
                    ViewData["Categories"] = new SelectList(await _context.Categories.ToListAsync(), "Id", "Name", product.CategoryId);
                    return View(product);
                }
                return RedirectToAction(nameof(Index));
            }

            ViewData["Categories"] = new SelectList(await _context.Categories.ToListAsync(), "Id", "Name", product.CategoryId);
            return View(product);
        }

        // GET: Products/Delete/5
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> Delete(int? id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var product = await _context.Products
                .FirstOrDefaultAsync(m => m.Id == id);
            if (product == null)
            {
                return NotFound();
            }

            return View(product);
        }

        // POST: Products/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> DeleteConfirmed(int id)
        {
            var product = await _context.Products.FindAsync(id);
            if (product != null)
            {
                // Delete image if exists
                if (!string.IsNullOrEmpty(product.ImageUrl))
                {
                    string imagePath = Path.Combine(_hostEnvironment.WebRootPath, product.ImageUrl.TrimStart('/'));
                    if (System.IO.File.Exists(imagePath))
                    {
                        System.IO.File.Delete(imagePath);
                    }
                }

                _context.Products.Remove(product);
                await _context.SaveChangesAsync();
            }

            return RedirectToAction(nameof(Index));
        }

        private bool ProductExists(int id)
        {
            return _context.Products.Any(e => e.Id == id);
        }

        // GET: Products/Wishlist
        public IActionResult Wishlist()
        {
            return View();
        }

        // GET: Products/Compare
        public IActionResult Compare()
        {
            return View();
        }

        // POST: Products/ToggleAvailability/5
        [HttpPost]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> ToggleAvailability(int id)
        {
            var product = await _context.Products.FindAsync(id);
            if (product == null)
            {
                return Json(new { success = false, message = "المنتج غير موجود" });
            }

            // تبديل حالة IsAvailable
            product.IsAvailable = !product.IsAvailable;

            try
            {
                await _context.SaveChangesAsync();
                return Json(new {
                    success = true,
                    isAvailable = product.IsAvailable,
                    message = product.IsAvailable ?
                        "تم تعيين المنتج كمتوفر للبيع" :
                        "تم تعيين المنتج كغير متوفر للبيع"
                });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "حدث خطأ أثناء تحديث المنتج: " + ex.Message });
            }
        }

        // POST: Products/ToggleCarousel/5
        [HttpPost]
        [Authorize(Roles = "Admin")]
        public async Task<IActionResult> ToggleCarousel(int id)
        {
            var product = await _context.Products.FindAsync(id);
            if (product == null)
            {
                return Json(new { success = false, message = "المنتج غير موجود" });
            }

            // تبديل حالة ShowInCarousel
            product.ShowInCarousel = !product.ShowInCarousel;

            try
            {
                await _context.SaveChangesAsync();
                return Json(new {
                    success = true,
                    showInCarousel = product.ShowInCarousel,
                    productId = id,
                    message = product.ShowInCarousel ?
                        "تمت إضافة المنتج إلى الشريط المتحرك" :
                        "تمت إزالة المنتج من الشريط المتحرك"
                });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "حدث خطأ أثناء تحديث المنتج: " + ex.Message });
            }
        }

        // GET: Products/GetProductsByIds
        [HttpGet]
        public async Task<IActionResult> GetProductsByIds(string ids)
        {
            if (string.IsNullOrEmpty(ids))
            {
                return Json(new List<object>());
            }

            var productIds = ids.Split(',').Select(id => int.TryParse(id, out int parsedId) ? parsedId : 0).Where(id => id > 0).ToList();

            if (!productIds.Any())
            {
                return Json(new List<object>());
            }

            var products = await _context.Products
                .Include(p => p.Category)
                .Where(p => productIds.Contains(p.Id))
                .ToListAsync();

            // تحويل المنتجات إلى كائنات بسيطة لتجنب مشكلة الدورة في العلاقات
            var simplifiedProducts = products.Select(p => new
            {
                p.Id,
                p.Name,
                p.Description,
                p.Price,
                p.ImageUrl,
                p.IsAvailable,
                p.ProductType,
                Category = new
                {
                    p.Category.Id,
                    p.Category.Name,
                    p.Category.Slug
                }
            }).ToList();

            return Json(simplifiedProducts);
        }

        // GET: Products/GetBestSellers
        [HttpGet]
        public async Task<IActionResult> GetBestSellers()
        {
            try
            {
                // أولاً، محاولة الحصول على المنتجات المميزة من جدول FeaturedProducts
                var featuredProducts = await _context.FeaturedProducts
                    .Include(fp => fp.Product)
                    .Where(fp => fp.Product.IsAvailable)
                    .OrderBy(fp => fp.DisplayOrder)
                    .Take(6)
                    .Select(fp => new
                    {
                        Id = fp.Product.Id,
                        Name = fp.Product.Name,
                        Price = fp.Product.Price,
                        ImageUrl = fp.Product.ImageUrl ?? "/images/placeholder.jpg",
                        IsAvailable = fp.Product.IsAvailable
                    })
                    .ToListAsync();

                // إذا لم توجد منتجات مميزة، استخدم المنتجات العادية
                if (!featuredProducts.Any())
                {
                    featuredProducts = await _context.Products
                        .Where(p => p.IsAvailable)
                        .OrderBy(p => p.Id) // أو أي ترتيب آخر
                        .Take(6)
                        .Select(p => new
                        {
                            Id = p.Id,
                            Name = p.Name,
                            Price = p.Price,
                            ImageUrl = p.ImageUrl ?? "/images/placeholder.jpg",
                            IsAvailable = p.IsAvailable
                        })
                        .ToListAsync();
                }

                return Json(new { success = true, products = featuredProducts });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = "حدث خطأ أثناء جلب المنتجات الأكثر مبيعاً" });
            }
        }
    }
}
