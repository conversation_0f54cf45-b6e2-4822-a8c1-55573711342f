using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Abayat.Models;
using Abayat.ViewModels;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abayat.Services;
using Abayat.Data;

namespace Abayat.Controllers
{
    [Authorize(Roles = "Admin")]
    public class UsersController : Controller
    {
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly RoleManager<IdentityRole> _roleManager;
        private readonly IOrderService _orderService;
        private readonly ApplicationDbContext _context;

        public UsersController(UserManager<ApplicationUser> userManager, RoleManager<IdentityRole> roleManager,
            IOrderService orderService, ApplicationDbContext context)
        {
            _userManager = userManager;
            _roleManager = roleManager;
            _orderService = orderService;
            _context = context;
        }

        // GET: Users
        public async Task<IActionResult> Index()
        {
            var users = await _userManager.Users.ToListAsync();
            var userViewModels = new List<UserViewModel>();

            foreach (var user in users)
            {
                var roles = await _userManager.GetRolesAsync(user);

                // الحصول على طلبات المستخدم
                var orders = await _context.Orders
                    .Where(o => o.UserId == user.Id)
                    .ToListAsync();

                // الحصول على آخر 5 طلبات
                var recentOrders = await _context.Orders
                    .Where(o => o.UserId == user.Id)
                    .OrderByDescending(o => o.OrderDate)
                    .Take(5)
                    .Include(o => o.OrderItems)
                    .ToListAsync();

                userViewModels.Add(new UserViewModel
                {
                    Id = user.Id,
                    Name = user.Name,
                    Email = user.Email,
                    PhoneNumber = user.PhoneNumber,
                    Roles = roles.ToList(),
                    OrdersCount = orders.Count,
                    TotalOrdersAmount = orders.Sum(o => o.TotalAmount),
                    RecentOrders = recentOrders
                });
            }

            return View(userViewModels);
        }

        // GET: Users/Details/5
        public async Task<IActionResult> Details(string id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var user = await _userManager.FindByIdAsync(id);
            if (user == null)
            {
                return NotFound();
            }

            var roles = await _userManager.GetRolesAsync(user);
            var userViewModel = new UserViewModel
            {
                Id = user.Id,
                Name = user.Name,
                Email = user.Email,
                PhoneNumber = user.PhoneNumber,
                Roles = roles.ToList()
            };

            return View(userViewModel);
        }

        // GET: Users/Edit/5
        public async Task<IActionResult> Edit(string id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var user = await _userManager.FindByIdAsync(id);
            if (user == null)
            {
                return NotFound();
            }

            var userRoles = await _userManager.GetRolesAsync(user);
            var allRoles = _roleManager.Roles.Select(r => r.Name).ToList();

            var viewModel = new EditUserViewModel
            {
                Id = user.Id,
                Name = user.Name,
                Email = user.Email,
                PhoneNumber = user.PhoneNumber,
                UserRoles = userRoles.ToList(),
                AllRoles = allRoles
            };

            return View(viewModel);
        }

        // POST: Users/Edit/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Edit(string id, EditUserViewModel model)
        {
            if (id != model.Id)
            {
                return NotFound();
            }

            if (ModelState.IsValid)
            {
                var user = await _userManager.FindByIdAsync(id);
                if (user == null)
                {
                    return NotFound();
                }

                user.Name = model.Name;
                user.PhoneNumber = model.PhoneNumber;

                var result = await _userManager.UpdateAsync(user);
                if (!result.Succeeded)
                {
                    foreach (var error in result.Errors)
                    {
                        ModelState.AddModelError(string.Empty, error.Description);
                    }
                    return View(model);
                }

                // تحديث الأدوار
                var userRoles = await _userManager.GetRolesAsync(user);

                // إزالة الأدوار الحالية
                await _userManager.RemoveFromRolesAsync(user, userRoles);

                // إضافة الأدوار المحددة
                if (model.SelectedRoles != null && model.SelectedRoles.Any())
                {
                    await _userManager.AddToRolesAsync(user, model.SelectedRoles);
                }
                else
                {
                    // إذا لم يتم تحديد أي دور، أضف دور "زبون" افتراضيًا
                    await _userManager.AddToRoleAsync(user, "Customer");
                }

                return RedirectToAction(nameof(Index));
            }

            // إذا وصلنا إلى هنا، فهناك خطأ ما، قم بإعادة تحميل الأدوار
            var allRoles = _roleManager.Roles.Select(r => r.Name).ToList();
            model.AllRoles = allRoles;

            return View(model);
        }

        // GET: Users/Delete/5
        public async Task<IActionResult> Delete(string id)
        {
            if (id == null)
            {
                return NotFound();
            }

            var user = await _userManager.FindByIdAsync(id);
            if (user == null)
            {
                return NotFound();
            }

            var roles = await _userManager.GetRolesAsync(user);
            var userViewModel = new UserViewModel
            {
                Id = user.Id,
                Name = user.Name,
                Email = user.Email,
                PhoneNumber = user.PhoneNumber,
                Roles = roles.ToList()
            };

            return View(userViewModel);
        }

        // POST: Users/Delete/5
        [HttpPost, ActionName("Delete")]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteConfirmed(string id)
        {
            var user = await _userManager.FindByIdAsync(id);
            if (user == null)
            {
                return NotFound();
            }

            // لا تسمح بحذف المستخدم الحالي
            if (User.Identity.Name == user.Email)
            {
                ModelState.AddModelError(string.Empty, "لا يمكنك حذف حسابك الحالي.");
                var roles = await _userManager.GetRolesAsync(user);
                var userViewModel = new UserViewModel
                {
                    Id = user.Id,
                    Name = user.Name,
                    Email = user.Email,
                    PhoneNumber = user.PhoneNumber,
                    Roles = roles.ToList()
                };
                return View(userViewModel);
            }

            var result = await _userManager.DeleteAsync(user);
            if (!result.Succeeded)
            {
                ModelState.AddModelError(string.Empty, "حدث خطأ أثناء حذف المستخدم.");
                var roles = await _userManager.GetRolesAsync(user);
                var userViewModel = new UserViewModel
                {
                    Id = user.Id,
                    Name = user.Name,
                    Email = user.Email,
                    PhoneNumber = user.PhoneNumber,
                    Roles = roles.ToList()
                };
                return View(userViewModel);
            }

            return RedirectToAction(nameof(Index));
        }

        // GET: Users/Create
        public IActionResult Create()
        {
            var allRoles = _roleManager.Roles.Select(r => r.Name).ToList();
            var viewModel = new CreateUserViewModel
            {
                AllRoles = allRoles
            };
            return View(viewModel);
        }

        // POST: Users/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> Create(CreateUserViewModel model)
        {
            if (ModelState.IsValid)
            {
                var user = new ApplicationUser
                {
                    UserName = model.Email,
                    Email = model.Email,
                    Name = model.Name,
                    PhoneNumber = model.PhoneNumber
                };

                var result = await _userManager.CreateAsync(user, model.Password);
                if (result.Succeeded)
                {
                    // إضافة الأدوار المحددة
                    if (model.SelectedRoles != null && model.SelectedRoles.Any())
                    {
                        await _userManager.AddToRolesAsync(user, model.SelectedRoles);
                    }
                    else
                    {
                        // إذا لم يتم تحديد أي دور، أضف دور "زبون" افتراضيًا
                        await _userManager.AddToRoleAsync(user, "Customer");
                    }

                    return RedirectToAction(nameof(Index));
                }

                foreach (var error in result.Errors)
                {
                    ModelState.AddModelError(string.Empty, error.Description);
                }
            }

            // إذا وصلنا إلى هنا، فهناك خطأ ما، قم بإعادة تحميل الأدوار
            var allRoles = _roleManager.Roles.Select(r => r.Name).ToList();
            model.AllRoles = allRoles;

            return View(model);
        }
    }
}
