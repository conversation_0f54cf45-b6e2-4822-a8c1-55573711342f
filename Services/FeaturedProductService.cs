using Abayat.Data;
using Abayat.Models;
using Abayat.ViewModels;
using Microsoft.EntityFrameworkCore;

namespace Abayat.Services
{
    public class FeaturedProductService : IFeaturedProductService
    {
        private readonly ApplicationDbContext _context;
        private const int MAX_FEATURED_PRODUCTS = 5;

        public FeaturedProductService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<List<FeaturedProductViewModel>> GetFeaturedProductsAsync()
        {
            var featuredProducts = await _context.FeaturedProducts
                .Include(fp => fp.Product)
                .OrderBy(fp => fp.DisplayOrder)
                .Select(fp => new FeaturedProductViewModel
                {
                    ProductId = fp.ProductId,
                    ProductName = fp.Product.Name,
                    ProductImageUrl = fp.Product.ImageUrl,
                    ProductPrice = fp.Product.DiscountedPrice,
                    IsAvailable = fp.Product.IsAvailable,
                    ShowInCarousel = fp.Product.ShowInCarousel,
                    DisplayOrder = fp.DisplayOrder,
                    CreatedAt = fp.CreatedAt
                })
                .ToListAsync();

            // إضافة بيانات المبيعات
            foreach (var product in featuredProducts)
            {
                var salesData = await _context.OrderItems
                    .Where(oi => oi.ProductId == product.ProductId)
                    .GroupBy(oi => oi.ProductId)
                    .Select(g => new
                    {
                        TotalQuantity = g.Sum(oi => oi.Quantity),
                        TotalRevenue = g.Sum(oi => oi.Quantity * oi.UnitPrice)
                    })
                    .FirstOrDefaultAsync();

                if (salesData != null)
                {
                    product.TotalQuantity = salesData.TotalQuantity;
                    product.TotalRevenue = salesData.TotalRevenue;
                }
            }

            return featuredProducts;
        }

        public async Task<ManageFeaturedProductsViewModel> GetManageFeaturedProductsViewModelAsync()
        {
            var featuredProducts = await GetFeaturedProductsAsync();
            var availableProducts = await GetAvailableProductsAsync();

            return new ManageFeaturedProductsViewModel
            {
                FeaturedProducts = featuredProducts,
                AvailableProducts = availableProducts,
                MaxFeaturedProducts = MAX_FEATURED_PRODUCTS
            };
        }

        public async Task<List<AvailableProductViewModel>> GetAvailableProductsAsync(string searchTerm = "")
        {
            var featuredProductIds = await _context.FeaturedProducts
                .Select(fp => fp.ProductId)
                .ToListAsync();

            var query = _context.Products
                .Include(p => p.Category)
                .Where(p => p.IsAvailable && !featuredProductIds.Contains(p.Id));

            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(p => p.Name.Contains(searchTerm) || 
                                        p.Description.Contains(searchTerm));
            }

            return await query
                .Select(p => new AvailableProductViewModel
                {
                    Id = p.Id,
                    Name = p.Name,
                    ImageUrl = p.ImageUrl,
                    Price = p.DiscountedPrice,
                    IsAvailable = p.IsAvailable,
                    CategoryName = p.Category.Name,
                    IsAlreadyFeatured = featuredProductIds.Contains(p.Id)
                })
                .OrderBy(p => p.Name)
                .ToListAsync();
        }

        public async Task<bool> AddToFeaturedAsync(int productId)
        {
            try
            {
                // التحقق من أن المنتج غير موجود في القائمة
                var existingFeatured = await _context.FeaturedProducts
                    .FirstOrDefaultAsync(fp => fp.ProductId == productId);

                if (existingFeatured != null)
                    return false;

                // التحقق من عدم تجاوز الحد الأقصى
                var currentCount = await _context.FeaturedProducts.CountAsync();
                if (currentCount >= MAX_FEATURED_PRODUCTS)
                    return false;

                // التحقق من وجود المنتج
                var product = await _context.Products.FindAsync(productId);
                if (product == null || !product.IsAvailable)
                    return false;

                // الحصول على الترتيب التالي
                var nextOrder = await GetNextDisplayOrderAsync();

                var featuredProduct = new FeaturedProduct
                {
                    ProductId = productId,
                    DisplayOrder = nextOrder,
                    CreatedAt = DateTime.Now,
                    UpdatedAt = DateTime.Now
                };

                _context.FeaturedProducts.Add(featuredProduct);
                await _context.SaveChangesAsync();

                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> RemoveFromFeaturedAsync(int productId)
        {
            try
            {
                var featuredProduct = await _context.FeaturedProducts
                    .FirstOrDefaultAsync(fp => fp.ProductId == productId);

                if (featuredProduct == null)
                    return false;

                var removedOrder = featuredProduct.DisplayOrder;
                _context.FeaturedProducts.Remove(featuredProduct);

                // إعادة ترتيب العناصر المتبقية
                var remainingProducts = await _context.FeaturedProducts
                    .Where(fp => fp.DisplayOrder > removedOrder)
                    .ToListAsync();

                foreach (var product in remainingProducts)
                {
                    product.DisplayOrder--;
                    product.UpdatedAt = DateTime.Now;
                }

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateDisplayOrderAsync(int productId, int newOrder)
        {
            try
            {
                var featuredProduct = await _context.FeaturedProducts
                    .FirstOrDefaultAsync(fp => fp.ProductId == productId);

                if (featuredProduct == null)
                    return false;

                var oldOrder = featuredProduct.DisplayOrder;
                
                if (oldOrder == newOrder)
                    return true;

                // تحديث ترتيب العناصر الأخرى
                if (newOrder > oldOrder)
                {
                    var affectedProducts = await _context.FeaturedProducts
                        .Where(fp => fp.DisplayOrder > oldOrder && fp.DisplayOrder <= newOrder)
                        .ToListAsync();

                    foreach (var product in affectedProducts)
                    {
                        product.DisplayOrder--;
                        product.UpdatedAt = DateTime.Now;
                    }
                }
                else
                {
                    var affectedProducts = await _context.FeaturedProducts
                        .Where(fp => fp.DisplayOrder >= newOrder && fp.DisplayOrder < oldOrder)
                        .ToListAsync();

                    foreach (var product in affectedProducts)
                    {
                        product.DisplayOrder++;
                        product.UpdatedAt = DateTime.Now;
                    }
                }

                featuredProduct.DisplayOrder = newOrder;
                featuredProduct.UpdatedAt = DateTime.Now;

                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> CanAddMoreFeaturedAsync()
        {
            var currentCount = await _context.FeaturedProducts.CountAsync();
            return currentCount < MAX_FEATURED_PRODUCTS;
        }

        public async Task<int> GetFeaturedCountAsync()
        {
            return await _context.FeaturedProducts.CountAsync();
        }

        private async Task<int> GetNextDisplayOrderAsync()
        {
            var maxOrder = await _context.FeaturedProducts
                .MaxAsync(fp => (int?)fp.DisplayOrder) ?? 0;
            return maxOrder + 1;
        }
    }
}
