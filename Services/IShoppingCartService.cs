using Abayat.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Abayat.Services
{
    public interface IShoppingCartService
    {
        Task<ShoppingCart> GetCartAsync(string userId);
        Task<CartItem> AddItemToCartAsync(string userId, int productId, int quantity);
        Task<CartItem> UpdateCartItemAsync(string userId, int cartItemId, int quantity);
        Task RemoveCartItemAsync(string userId, int cartItemId);
        Task ClearCartAsync(string userId);
        Task<int> GetCartItemsCountAsync(string userId);
        Task<ShoppingCart> GetCartWithItemsAsync(string userId);
    }
}
