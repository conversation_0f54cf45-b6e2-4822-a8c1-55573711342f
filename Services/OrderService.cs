using Abayat.Data;
using Abayat.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Abayat.Services
{
    public class OrderService : IOrderService
    {
        private readonly ApplicationDbContext _context;
        private readonly IShoppingCartService _cartService;

        public OrderService(ApplicationDbContext context, IShoppingCartService cartService)
        {
            _context = context;
            _cartService = cartService;
        }

        public async Task<Order> CreateOrderFromCartAsync(string userId, string fullName, string phoneNumber, string address, string notes, PaymentMethod paymentMethod = PaymentMethod.CashOnDelivery, string? paymentPhoneNumber = null, string? paymentReceiptPath = null, string? paymentNotes = null)
        {
            // الحصول على سلة المشتريات مع العناصر
            var cart = await _cartService.GetCartWithItemsAsync(userId);

            if (cart.CartItems == null || !cart.CartItems.Any())
            {
                throw new InvalidOperationException("لا يمكن إنشاء طلب من سلة فارغة");
            }

            // إنشاء الطلب
            var order = new Order
            {
                UserId = userId,
                FullName = fullName,
                PhoneNumber = phoneNumber,
                Address = address,
                Notes = notes,
                TotalAmount = cart.TotalAmount,
                Status = OrderStatus.Pending,
                OrderDate = DateTime.Now,
                PaymentMethod = paymentMethod,
                PaymentPhoneNumber = paymentPhoneNumber,
                PaymentReceiptPath = paymentReceiptPath,
                PaymentNotes = paymentNotes
            };

            // إضافة الطلب إلى قاعدة البيانات
            _context.Orders.Add(order);

            // حفظ التغييرات في قاعدة البيانات
            Console.WriteLine("جاري حفظ الطلب في قاعدة البيانات...");
            await _context.SaveChangesAsync();
            Console.WriteLine($"تم حفظ الطلب بنجاح. رقم الطلب: {order.Id}");

            // إضافة عناصر الطلب
            var orderItems = new List<OrderItem>();
            foreach (var cartItem in cart.CartItems)
            {
                var orderItem = new OrderItem
                {
                    OrderId = order.Id,
                    ProductId = cartItem.ProductId,
                    ProductName = cartItem.Product.Name,
                    UnitPrice = cartItem.Product.DiscountedPrice,
                    Quantity = cartItem.Quantity,
                    TotalPrice = cartItem.TotalPrice
                };
                orderItems.Add(orderItem);
            }

            _context.OrderItems.AddRange(orderItems);
            await _context.SaveChangesAsync();

            // تفريغ سلة المشتريات
            await _cartService.ClearCartAsync(userId);

            // إعادة تحميل الطلب مع العناصر
            return await GetOrderByIdAsync(order.Id, userId);
        }

        public async Task<Order> CreateGuestOrderAsync(List<CartItem> cartItems, string fullName, string phoneNumber, string address, string notes, PaymentMethod paymentMethod = PaymentMethod.CashOnDelivery, string? paymentPhoneNumber = null, string? paymentReceiptPath = null, string? paymentNotes = null)
        {
            if (cartItems == null || !cartItems.Any())
            {
                throw new InvalidOperationException("لا يمكن إنشاء طلب من سلة فارغة");
            }

            // حساب المبلغ الإجمالي للطلب
            decimal totalAmount = 0;

            // تحميل معلومات المنتجات لعناصر السلة
            var productIds = cartItems.Select(item => item.ProductId).ToList();
            var products = await _context.Products.Where(p => productIds.Contains(p.Id)).ToListAsync();

            foreach (var item in cartItems)
            {
                var product = products.FirstOrDefault(p => p.Id == item.ProductId);
                if (product != null)
                {
                    totalAmount += product.DiscountedPrice * item.Quantity;
                }
            }

            // إنشاء طلب للزائر
            var order = new Order
            {
                UserId = null, // استخدام null للزوار غير المسجلين
                FullName = fullName,
                PhoneNumber = phoneNumber,
                Address = address,
                Notes = notes,
                TotalAmount = totalAmount,
                Status = OrderStatus.Pending,
                OrderDate = DateTime.Now,
                IsGuestOrder = true, // تعليم الطلب كطلب زائر
                PaymentMethod = paymentMethod,
                PaymentPhoneNumber = paymentPhoneNumber,
                PaymentReceiptPath = paymentReceiptPath,
                PaymentNotes = paymentNotes
            };

            // إضافة الطلب إلى قاعدة البيانات
            _context.Orders.Add(order);

            // حفظ التغييرات في قاعدة البيانات
            Console.WriteLine("جاري حفظ طلب الزائر في قاعدة البيانات...");
            await _context.SaveChangesAsync();
            Console.WriteLine($"تم حفظ طلب الزائر بنجاح. رقم الطلب: {order.Id}");

            // إضافة عناصر الطلب
            var orderItems = new List<OrderItem>();
            foreach (var cartItem in cartItems)
            {
                var product = products.FirstOrDefault(p => p.Id == cartItem.ProductId);
                if (product != null)
                {
                    var orderItem = new OrderItem
                    {
                        OrderId = order.Id,
                        ProductId = cartItem.ProductId,
                        ProductName = product.Name,
                        UnitPrice = product.DiscountedPrice,
                        Quantity = cartItem.Quantity,
                        TotalPrice = product.DiscountedPrice * cartItem.Quantity
                    };
                    orderItems.Add(orderItem);
                }
            }

            _context.OrderItems.AddRange(orderItems);
            await _context.SaveChangesAsync();

            // إعادة تحميل الطلب مع العناصر
            return await GetOrderByIdAsync(order.Id);
        }

        public async Task<Order> GetOrderByIdAsync(int orderId, string userId)
        {
            return await _context.Orders
                .Include(o => o.OrderItems)
                .ThenInclude(i => i.Product)
                .FirstOrDefaultAsync(o => o.Id == orderId && o.UserId == userId);
        }

        public async Task<Order> GetOrderByIdAsync(int orderId)
        {
            // للمدير فقط - يمكنه عرض أي طلب بغض النظر عن المستخدم
            var order = await _context.Orders
                .Include(o => o.OrderItems)
                .ThenInclude(i => i.Product)
                .Include(o => o.User)
                .FirstOrDefaultAsync(o => o.Id == orderId);

            if (order != null)
            {
                Console.WriteLine($"تم العثور على الطلب رقم: {order.Id}، الحالة: {order.Status}، رقم الهاتف: {order.PhoneNumber}");
                Console.WriteLine($"طريقة الدفع: {order.PaymentMethod} ({(int)order.PaymentMethod})");
                Console.WriteLine($"رقم الهاتف للتحويل: {order.PaymentPhoneNumber}");
                Console.WriteLine($"ملاحظات الدفع: {order.PaymentNotes}");

                // التأكد من تحميل عناصر الطلب
                if (order.OrderItems != null)
                {
                    Console.WriteLine($"عدد عناصر الطلب: {order.OrderItems.Count}");
                }
                else
                {
                    Console.WriteLine("لا توجد عناصر في الطلب");
                }
            }
            else
            {
                Console.WriteLine($"لم يتم العثور على طلب برقم: {orderId}");
            }

            return order;
        }

        public async Task<List<Order>> GetUserOrdersAsync(string userId)
        {
            return await _context.Orders
                .Include(o => o.OrderItems)
                .Where(o => o.UserId == userId)
                .OrderByDescending(o => o.OrderDate)
                .ToListAsync();
        }

        public async Task<List<Order>> GetAllOrdersAsync()
        {
            // تسجيل عملية استرجاع الطلبات
            Console.WriteLine("جاري استرجاع جميع الطلبات من قاعدة البيانات...");

            var orders = await _context.Orders
                .Include(o => o.OrderItems)
                .Include(o => o.User)
                .OrderByDescending(o => o.OrderDate)
                .ToListAsync();

            Console.WriteLine($"تم استرجاع {orders.Count} طلب من قاعدة البيانات");

            // طباعة معلومات كل طلب للتحقق
            foreach (var order in orders)
            {
                Console.WriteLine($"الطلب رقم: {order.Id}, المستخدم: {order.UserId}, الحالة: {order.Status}, المبلغ: {order.TotalAmount}");
            }

            return orders;
        }

        public async Task<Order> UpdateOrderStatusAsync(int orderId, OrderStatus status)
        {
            var order = await _context.Orders.FindAsync(orderId);
            if (order == null)
            {
                throw new ArgumentException("الطلب غير موجود");
            }

            // لا يمكن تعديل حالة الطلب إذا كان ملغياً
            if (order.Status == OrderStatus.Cancelled)
            {
                throw new InvalidOperationException("لا يمكن تعديل حالة الطلب لأنه تم إلغاؤه بالفعل");
            }

            // لا يمكن تغيير حالة الطلب إلى الانتظار أو التأكيد إذا كان في مرحلة الشحن أو التسليم
            if ((order.Status == OrderStatus.Shipped || order.Status == OrderStatus.Delivered) &&
                (status == OrderStatus.Pending || status == OrderStatus.Confirmed || status == OrderStatus.Processing))
            {
                throw new InvalidOperationException("لا يمكن تغيير حالة الطلب إلى مرحلة سابقة بعد الشحن");
            }

            order.Status = status;
            order.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();

            return order;
        }

        public async Task<bool> CancelOrderAsync(int orderId, string userId)
        {
            var order = await _context.Orders
                .FirstOrDefaultAsync(o => o.Id == orderId && o.UserId == userId);

            if (order == null)
            {
                throw new ArgumentException("الطلب غير موجود");
            }

            // لا يمكن إلغاء الطلب إذا كان ملغياً بالفعل
            if (order.Status == OrderStatus.Cancelled)
            {
                throw new InvalidOperationException("لا يمكن إلغاء الطلب لأنه تم إلغاؤه بالفعل");
            }

            // لا يمكن إلغاء الطلب إذا كان في مرحلة الشحن أو التسليم
            if (order.Status == OrderStatus.Shipped || order.Status == OrderStatus.Delivered || order.Status == OrderStatus.Processing)
            {
                throw new InvalidOperationException("لا يمكن إلغاء الطلب بعد مرحلة التأكيد");
            }

            order.Status = OrderStatus.Cancelled;
            order.UpdatedAt = DateTime.Now;
            await _context.SaveChangesAsync();

            return true;
        }
    }
}
