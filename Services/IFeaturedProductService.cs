using Abayat.ViewModels;

namespace Abayat.Services
{
    public interface IFeaturedProductService
    {
        Task<List<FeaturedProductViewModel>> GetFeaturedProductsAsync();
        Task<ManageFeaturedProductsViewModel> GetManageFeaturedProductsViewModelAsync();
        Task<List<AvailableProductViewModel>> GetAvailableProductsAsync(string searchTerm = "");
        Task<bool> AddToFeaturedAsync(int productId);
        Task<bool> RemoveFromFeaturedAsync(int productId);
        Task<bool> UpdateDisplayOrderAsync(int productId, int newOrder);
        Task<bool> CanAddMoreFeaturedAsync();
        Task<int> GetFeaturedCountAsync();
    }
}
