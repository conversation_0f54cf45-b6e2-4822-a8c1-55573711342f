using Abayat.Data;
using Abayat.Models;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Abayat.Services
{
    public class ShoppingCartService : IShoppingCartService
    {
        private readonly ApplicationDbContext _context;

        public ShoppingCartService(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<ShoppingCart> GetCartAsync(string userId)
        {
            var cart = await _context.ShoppingCarts
                .FirstOrDefaultAsync(c => c.UserId == userId);

            if (cart == null)
            {
                cart = new ShoppingCart
                {
                    UserId = userId,
                    CreatedAt = DateTime.Now
                };
                _context.ShoppingCarts.Add(cart);
                await _context.SaveChangesAsync();
            }

            return cart;
        }

        public async Task<ShoppingCart> GetCartWithItemsAsync(string userId)
        {
            var cart = await _context.ShoppingCarts
                .Include(c => c.CartItems)
                .ThenInclude(i => i.Product)
                .ThenInclude(p => p.Category)
                .FirstOrDefaultAsync(c => c.UserId == userId);

            if (cart == null)
            {
                cart = new ShoppingCart
                {
                    UserId = userId,
                    CreatedAt = DateTime.Now
                };
                _context.ShoppingCarts.Add(cart);
                await _context.SaveChangesAsync();
            }

            return cart;
        }

        public async Task<CartItem> AddItemToCartAsync(string userId, int productId, int quantity)
        {
            var cart = await GetCartAsync(userId);
            var product = await _context.Products.FindAsync(productId);

            if (product == null)
            {
                throw new ArgumentException("المنتج غير موجود");
            }

            if (!product.IsAvailable)
            {
                throw new InvalidOperationException("المنتج غير متوفر حالياً");
            }

            var cartItem = await _context.CartItems
                .FirstOrDefaultAsync(i => i.ShoppingCartId == cart.Id && i.ProductId == productId);

            if (cartItem != null)
            {
                // المنتج موجود بالفعل في السلة، قم بتحديث الكمية
                cartItem.Quantity += quantity;
                cartItem.UpdatedAt = DateTime.Now;
            }
            else
            {
                // إضافة منتج جديد إلى السلة
                cartItem = new CartItem
                {
                    ShoppingCartId = cart.Id,
                    ProductId = productId,
                    Quantity = quantity,
                    CreatedAt = DateTime.Now
                };
                _context.CartItems.Add(cartItem);
            }

            // تحديث وقت تحديث السلة
            cart.UpdatedAt = DateTime.Now;
            _context.ShoppingCarts.Update(cart);

            await _context.SaveChangesAsync();

            // إعادة تحميل العنصر مع معلومات المنتج
            return await _context.CartItems
                .Include(i => i.Product)
                .FirstOrDefaultAsync(i => i.Id == cartItem.Id);
        }

        public async Task<CartItem> UpdateCartItemAsync(string userId, int cartItemId, int quantity)
        {
            var cart = await GetCartAsync(userId);
            var cartItem = await _context.CartItems
                .FirstOrDefaultAsync(i => i.Id == cartItemId && i.ShoppingCartId == cart.Id);

            if (cartItem == null)
            {
                throw new ArgumentException("العنصر غير موجود في السلة");
            }

            if (quantity <= 0)
            {
                // إذا كانت الكمية صفر أو أقل، قم بإزالة العنصر
                await RemoveCartItemAsync(userId, cartItemId);
                return null;
            }

            cartItem.Quantity = quantity;
            cartItem.UpdatedAt = DateTime.Now;

            // تحديث وقت تحديث السلة
            cart.UpdatedAt = DateTime.Now;
            _context.ShoppingCarts.Update(cart);

            await _context.SaveChangesAsync();

            // إعادة تحميل العنصر مع معلومات المنتج
            return await _context.CartItems
                .Include(i => i.Product)
                .FirstOrDefaultAsync(i => i.Id == cartItem.Id);
        }

        public async Task RemoveCartItemAsync(string userId, int cartItemId)
        {
            var cart = await GetCartAsync(userId);
            var cartItem = await _context.CartItems
                .FirstOrDefaultAsync(i => i.Id == cartItemId && i.ShoppingCartId == cart.Id);

            if (cartItem != null)
            {
                _context.CartItems.Remove(cartItem);

                // تحديث وقت تحديث السلة
                cart.UpdatedAt = DateTime.Now;
                _context.ShoppingCarts.Update(cart);

                await _context.SaveChangesAsync();
            }
        }

        public async Task ClearCartAsync(string userId)
        {
            var cart = await GetCartAsync(userId);
            var cartItems = await _context.CartItems
                .Where(i => i.ShoppingCartId == cart.Id)
                .ToListAsync();

            _context.CartItems.RemoveRange(cartItems);

            // تحديث وقت تحديث السلة
            cart.UpdatedAt = DateTime.Now;
            _context.ShoppingCarts.Update(cart);

            await _context.SaveChangesAsync();
        }

        public async Task<int> GetCartItemsCountAsync(string userId)
        {
            var cart = await GetCartWithItemsAsync(userId);
            return cart.TotalItems;
        }
    }
}
