<!-- الشريط العلوي -->
<div class="top-header">
    <div class="container">
        <div class="row align-items-center">
            <!-- القسم الأيمن: للاستفسارات -->
            <div class="col-md-4 text-end">
                <div class="contact-info">
                    <div class="contact-title">للاستفسارات</div>
                    <div class="contact-email" dir="ltr"><EMAIL></div>
                    <div class="contact-phone" dir="ltr">00968 78243541</div>
                </div>
            </div>

            <!-- القسم الأوسط: الشعار -->
            <div class="col-md-4 d-flex justify-content-center">
                <a class="navbar-brand" asp-controller="Home" asp-action="Index">
                    <img src="~/images/logo3.png" alt="راعي المخور" class="logo-img" />
                </a>
            </div>

            <!-- القسم الأيسر: تسجيل الدخول والبحث والمفضلة والسلة -->
            <div class="col-md-4" >

                <div class="header-icons-container d-flex justify-content-end" dir="rtl">

                    <!-- التسجيل على اليمين -->
                    <div class="login-register">
                        @if (User.Identity?.IsAuthenticated == true)
                        {
                            <div class="dropdown">
                                <a class="dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <div class="d-flex align-items-center">
                                        <span class="user-text me-2">
                                            حسابي <i class="bi bi-chevron-down"></i>
                                        </span>
                                        <span class="user-icon">
                                            <i class="bi bi-person-fill"></i>
                                        </span>
                                    </div>
                                </a>
                                <ul class="dropdown-menu">
                                    <li>
                                        <a class="dropdown-item" asp-controller="Orders" asp-action="Index">
                                            <i class="bi bi-bag"></i> طلباتي
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" asp-area="Identity" asp-page="/Account/Manage/Index">
                                            <i class="bi bi-gear"></i> إعدادات الحساب
                                        </a>
                                    </li>
                                    @if (User.IsInRole("Admin"))
                                    {
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item" asp-controller="Admin" asp-action="Index">
                                                <i class="bi bi-speedometer2"></i> لوحة التحكم
                                            </a>
                                        </li>
                                    }
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form asp-controller="Account" asp-action="Logout" method="post">
                                            @Html.AntiForgeryToken()
                                            <button type="submit" class="dropdown-item">
                                                <i class="bi bi-box-arrow-right"></i> تسجيل الخروج
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </div>
                        }
                        else
                        {
                            <a href="/Account/Login" class="icon-link" id="loginButton">
                                <i class="bi bi-person-fill"></i>
                            </a>
                        }
                    </div>

                    <!-- البحث في الوسط -->
                    <a href="#" class="icon-link">
                        <i class="bi bi-search" align="right"></i>
                    </a>

                    <!-- المفضلة في الوسط -->
                    <a href="javascript:void(0);" onclick="goToWishlist(event)" class="icon-link">
                        <i class="bi bi-heart"></i>
                        <span class="badge rounded-pill bg-danger wishlist-count">0</span>
                    </a>

                    <!-- المقارنة في الوسط -->
                    <a href="javascript:void(0);" onclick="goToCompare(event)" class="icon-link">
                        <i class="bi bi-arrow-left-right"></i>
                        <span class="badge rounded-pill bg-danger compare-count">0</span>
                    </a>

                    <!-- السلة والسعر على اليسار ---->
                    <div class="cart-price-container">
                        <a href="javascript:void(0);" onclick="goToCart(event)" class="icon-link">
                            <i class="bi bi-cart3"></i>
                            <span class="badge rounded-pill bg-danger cart-count">0</span>
                        </a>
                        <span class="cart-price" >ر.ع 0.000</span>
                    </div>

                </div>



        </div>

        </div>
    </div>
</div>

<script>
   let price = 0;
document.querySelector('.cart-price').textContent = price.toLocaleString('en-UK', {
  minimumFractionDigits: 3
}) + ' ر.ع';

// دالة الانتقال إلى صفحة المقارنة
function goToCompare(event) {
    event.preventDefault();
    window.location.href = '/Compare';
}
</script>
