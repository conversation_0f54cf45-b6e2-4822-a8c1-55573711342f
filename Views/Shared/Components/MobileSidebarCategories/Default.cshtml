@model IEnumerable<Abayat.Models.Category>

@foreach (var category in Model)
{
    <li class="mobile-sidebar-item">
        <a class="mobile-sidebar-link @(ViewContext.RouteData.Values["category"]?.ToString() == category.Slug ? "active" : "")"
           asp-controller="Products" asp-action="Index" asp-route-category="@category.Slug">
            <i class="bi bi-tag"></i> @category.Name
        </a>
    </li>
}
