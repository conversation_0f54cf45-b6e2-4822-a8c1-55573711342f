<!-- شريط التنقل الرئيسي -->

<nav class="navbar navbar-expand-lg navbar-light bg-light main-navigation d-none d-lg-block">

    <div class="container">
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            @await Component.InvokeAsync("NavigationMenu")
        </div>
    </div>
</nav>
