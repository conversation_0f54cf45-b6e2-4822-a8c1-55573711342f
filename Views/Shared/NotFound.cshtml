@{
    ViewData["Title"] = "الصفحة غير موجودة";
    var statusCode = ViewBag.StatusCode as int? ?? 404;
    var errorTitle = statusCode == 404 ? "عذراً، الصفحة غير موجودة!" : "عذراً، حدث خطأ!";
    var errorMessage = statusCode == 404
        ? "الصفحة التي تبحث عنها غير موجودة أو تم نقلها إلى مكان آخر."
        : "نعتذر عن هذا الخطأ. يبدو أن هناك مشكلة أثناء معالجة طلبك.";
}

<div class="error-page">
    <div class="container py-5">
        <div class="error-container">
            <div class="row align-items-center">
                <div class="col-lg-6 order-lg-2">
                    <div class="error-image zoom-in">
                        <img src="/images/404-illustration.svg" alt="صفحة غير موجودة" onerror="this.src='/images/error-fallback.png'" class="img-fluid">
                    </div>
                </div>
                <div class="col-lg-6 order-lg-1">
                    <div class="error-content slide-in-right">
                        <div class="error-icon">
                            <i class="bi bi-search"></i>
                        </div>
                        <h1 class="error-title">@errorTitle</h1>
                        <p class="error-message">@errorMessage</p>

                        @if (statusCode != 0)
                        {
                            <div class="error-details mb-3">
                                <p><strong>رمز الخطأ:</strong> <code>@statusCode</code></p>
                            </div>
                        }

                        <div class="error-actions">
                            <a href="/" class="btn btn-primary error-btn">
                                <i class="bi bi-house-fill me-2"></i> العودة للصفحة الرئيسية
                            </a>
                            <a href="/Categories" class="btn btn-outline-primary error-btn">
                                <i class="bi bi-grid-fill me-2"></i> تصفح الفئات
                            </a>
                        </div>

                        <div class="error-support mt-4">
                            <h5><i class="bi bi-headset me-2"></i> هل تحتاج إلى مساعدة؟</h5>
                            <p>يمكنك التواصل مع فريق الدعم الفني على البريد الإلكتروني: <a href="mailto:<EMAIL>"><EMAIL></a></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
