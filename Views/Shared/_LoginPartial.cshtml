@using Microsoft.AspNetCore.Identity
@using Abayat.Models

@inject SignInManager<ApplicationUser> SignInManager
@inject UserManager<ApplicationUser> UserManager

@if (SignInManager.IsSignedIn(User))
{
    <ul class="navbar-nav">
        <li class="nav-item dropdown">
            <a class="nav-link dropdown-toggle" href="#" id="userAccountDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="bi bi-person-circle"></i>
            </a>
            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userAccountDropdown">
                <li>
                    <a class="dropdown-item" href="javascript:void(0)">
                        <i class="bi bi-person"></i> @User.Identity?.Name
                    </a>
                </li>
                <li><hr class="dropdown-divider"></li>
                <li>
                    <a class="dropdown-item" href="javascript:void(0)" onclick="document.getElementById('logoutForm').submit();">
                        <i class="bi bi-box-arrow-right"></i> تسجيل الخروج
                    </a>
                </li>
                <li>
                    <a class="dropdown-item" asp-controller="Home" asp-action="Privacy">
                        <i class="bi bi-shield-check"></i> سياسة الخصوصية
                    </a>
                </li>
            </ul>
            <form id="logoutForm" class="d-none" asp-area="Identity" asp-page="/Account/Logout" asp-route-returnUrl="@Url.Action("Index", "Home", new { area = "" })" method="post"></form>
        </li>
    </ul>
}
else
{
    <!-- No se muestra nada aquí porque ya está en el menú desplegable del layout -->
    <div></div>
}