<!-- شريط التنقل السفلي للهواتف المحمولة -->
<div class="mobile-bottom-nav">
    <div class="mobile-bottom-nav-item">
        <a href="/" class="mobile-bottom-nav-link">
            <i class="bi bi-shop"></i>
            <span>متجر</span>
        </a>
    </div>
    <div class="mobile-bottom-nav-item">
        <a href="javascript:void(0);" onclick="goToWishlist(event)" class="mobile-bottom-nav-link">
            <i class="bi bi-heart"></i>
            <span class="badge rounded-pill bg-danger wishlist-count mobile-badge">0</span>
            <span>مفضلة</span>
        </a>
    </div>
    <div class="mobile-bottom-nav-item">
        <a href="javascript:void(0);" onclick="goToCart(event)" class="mobile-bottom-nav-link">
            <i class="bi bi-cart3"></i>
            <span class="badge rounded-pill bg-danger cart-count mobile-badge">0</span>
            <span>عربة</span>
        </a>
    </div>
    <div class="mobile-bottom-nav-item">
        <a href="javascript:void(0);" onclick="goToCompare(event)" class="mobile-bottom-nav-link">
            <i class="bi bi-arrow-left-right"></i>
            <span class="badge rounded-pill bg-danger compare-count mobile-badge">0</span>
            <span>مقارنة</span>
        </a>
    </div>
    <div class="mobile-bottom-nav-item">
        @if (User.Identity?.IsAuthenticated == true)
        {
            <a href="/Identity/Account/Manage" class="mobile-bottom-nav-link">
                <i class="bi bi-person-fill"></i>
                <span>حسابي</span>
            </a>
        }
        else
        {
            <a href="#" onclick="openSideLogin()" class="mobile-bottom-nav-link">
                <i class="bi bi-person"></i>
                <span>حساب</span>
            </a>
        }
    </div>
</div>


