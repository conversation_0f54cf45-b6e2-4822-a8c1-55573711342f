@using Microsoft.AspNetCore.Identity
@inject SignInManager<ApplicationUser> SignInManager
@inject UserManager<ApplicationUser> UserManager

@if (SignInManager.IsSignedIn(User) && User.IsInRole("Admin"))
{
    <div class="admin-menu">
        <div class="admin-menu-header">
            <h5>قائمة الإدارة</h5>
        </div>
        <div class="admin-menu-body">
            <ul class="admin-menu-list">
                <li class="admin-menu-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Admin" && ViewContext.RouteData.Values["Action"]?.ToString() == "Index" ? "active" : "")">
                    <a asp-controller="Admin" asp-action="Index" class="admin-menu-link">
                        <i class="bi bi-speedometer2"></i>
                        <span>لوحة التحكم</span>
                    </a>
                </li>
                <li class="admin-menu-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Products" ? "active" : "")">
                    <a asp-controller="Products" asp-action="Index" class="admin-menu-link">
                        <i class="bi bi-box-seam"></i>
                        <span>المنتجات</span>
                    </a>
                </li>
                <li class="admin-menu-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Categories" ? "active" : "")">
                    <a asp-controller="Categories" asp-action="Index" class="admin-menu-link">
                        <i class="bi bi-tags"></i>
                        <span>الفئات</span>
                    </a>
                </li>
                <li class="admin-menu-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Orders" && ViewContext.RouteData.Values["Action"]?.ToString() == "Manage" ? "active" : "")">
                    <a asp-controller="Orders" asp-action="Manage" class="admin-menu-link">
                        <i class="bi bi-bag"></i>
                        <span>الطلبات</span>
                    </a>
                </li>
                <li class="admin-menu-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "Users" ? "active" : "")">
                    <a asp-controller="Users" asp-action="Index" class="admin-menu-link">
                        <i class="bi bi-people"></i>
                        <span>المستخدمين</span>
                    </a>
                </li>
                <li class="admin-menu-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "CarouselImages" ? "active" : "")">
                    <a asp-controller="CarouselImages" asp-action="Index" class="admin-menu-link">
                        <i class="bi bi-images"></i>
                        <span>الشريط المتحرك</span>
                    </a>
                </li>
                <li class="admin-menu-item @(ViewContext.RouteData.Values["Controller"]?.ToString() == "FeaturedProducts" ? "active" : "")">
                    <a asp-controller="FeaturedProducts" asp-action="Index" class="admin-menu-link">
                        <i class="bi bi-star-fill"></i>
                        <span>المنتجات المميزة</span>
                    </a>
                </li>
                <li class="admin-menu-item">
                    <a asp-controller="Home" asp-action="Index" class="admin-menu-link">
                        <i class="bi bi-house"></i>
                        <span>العودة للموقع</span>
                    </a>
                </li>
            </ul>
        </div>
    </div>
}
