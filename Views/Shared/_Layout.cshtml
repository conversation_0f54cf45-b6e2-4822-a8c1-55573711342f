<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0, minimum-scale=1.0" />
    <title>@ViewData["Title"] - راعي المخور</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
    <link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/cart.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/wishlist-only.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/display-options.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/order-tracking.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/wishlist-compare.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/product-display.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/animations.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/navbar-custom.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/navbar-font.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/header-ameera.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/reverse-navbar-items.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/badge-position-fix.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/login-register-buttons.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/header-icons.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/dropdown-fix.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/side-login.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/rtl-fixes.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/mobile-nav.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/mobile-menu.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/mobile-header.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/mobile-touch-fix.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/side-filter.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/main-best-sellers.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/sticky-nav.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/carousel-custom.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/admin-dashboard.css" asp-append-version="true" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700;800&family=Amiri:ital,wght@0,400;0,700;1,400;1,700&family=Reem+Kufi:wght@400;500;600;700&family=Tajawal:wght@300;400;500;700;800&display=swap" rel="stylesheet">

    <!-- خط Tajawal للقائمة -->
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- شريط التنقل العلوي القابل للاختفاء -->
    @await Html.PartialAsync("_StickyNavPartial")

    <header>
        <!-- الهيدر العادي للشاشات الكبيرة -->
        @await Html.PartialAsync("_HeaderPartial")

        <!-- الهيدر في وضع الهاتف للشاشات الصغيرة -->
        @await Html.PartialAsync("_MobileHeaderPartial")
    </header>

    <!-- شريط القوائم الرئيسي -->
    @await Html.PartialAsync("_NavbarPartial")

    <!-- نافذة تسجيل الدخول الجانبية -->
    <div id="sideLoginOverlay" class="side-login-overlay">
        <div id="sideLoginPanel" class="side-login-panel">
            <!-- قسم تسجيل الدخول -->
            <div id="loginSection" class="side-panel-section">
                <div class="side-login-header">
                    <button id="sideLoginClose" class="side-login-close">
                        <i class="bi bi-x-lg"></i> قريب
                    </button>
                    <h2>تسجيل الدخول</h2>
                </div>
                <div class="side-login-body">
                    <form class="side-login-form" asp-area="Identity" asp-page="/Account/Login" method="post">
                        <div class="form-group">
                            <label for="username">
                                <span class="required">*</span> اسم المستخدم أو البريد الإلكتروني
                            </label>
                            <input type="text" id="username" name="Input.Email" class="form-control" required />
                        </div>
                        <div class="form-group">
                            <label for="password">
                                <span class="required">*</span> كلمة المرور
                            </label>
                            <div class="password-field">
                                <input type="password" id="password" name="Input.Password" class="form-control" required />
                                <button type="button" id="passwordToggle" class="password-toggle">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                        </div>
                        <button type="submit" class="submit-btn">تسجيل الدخول</button>
                        <div class="form-footer">
                            <div class="remember-me">
                                <label for="remember">تذكرني</label>
                                <input type="checkbox" id="remember" name="Input.RememberMe" />
                            </div>
                            <a href="javascript:void(0);" id="showForgotPassword" class="forgot-password">فقدت كلمة المرور الخاصة بك ؟</a>
                        </div>
                    </form>
                    <div class="side-login-divider"></div>
                    <div class="side-login-register">
                        <div class="register-icon">
                            <i class="bi bi-person-fill"></i>
                        </div>
                        <p>أي حساب حتى الآن ؟</p>
                        <a href="javascript:void(0);" id="showRegister">إنشاء حساب</a>
                    </div>
                </div>
            </div>

            <!-- قسم استعادة كلمة المرور -->
            <div id="forgotPasswordSection" class="side-panel-section" style="display: none;">
                <div class="side-login-header">
                    <button class="side-login-back" id="backToLogin1">
                        <i class="bi bi-arrow-right"></i> رجوع
                    </button>
                    <h2>استعادة كلمة المرور</h2>
                </div>
                <div class="side-login-body">
                    <form class="side-login-form" asp-area="Identity" asp-page="/Account/ForgotPassword" method="post">
                        <div class="form-group">
                            <label for="forgotEmail">
                                <span class="required">*</span> البريد الإلكتروني
                            </label>
                            <input type="email" id="forgotEmail" name="Input.Email" class="form-control" required />
                        </div>
                        <p class="text-muted">أدخل بريدك الإلكتروني وسنرسل لك رابطًا لإعادة تعيين كلمة المرور الخاصة بك.</p>
                        <button type="submit" class="submit-btn">إرسال</button>
                    </form>
                </div>
            </div>

            <!-- قسم التسجيل الجديد -->
            <div id="registerSection" class="side-panel-section" style="display: none;">
                <div class="side-login-header">
                    <button class="side-login-back" id="backToLogin2">
                        <i class="bi bi-arrow-right"></i> رجوع
                    </button>
                    <h2>إنشاء حساب جديد</h2>
                </div>
                <div class="side-login-body">
                    <form class="side-login-form" asp-area="Identity" asp-page="/Account/Register" method="post">
                        <div class="form-group">
                            <label for="registerEmail">
                                <span class="required">*</span> البريد الإلكتروني
                            </label>
                            <input type="email" id="registerEmail" name="Input.Email" class="form-control" required />
                        </div>
                        <div class="form-group">
                            <label for="registerPassword">
                                <span class="required">*</span> كلمة المرور
                            </label>
                            <div class="password-field">
                                <input type="password" id="registerPassword" name="Input.Password" class="form-control" required />
                                <button type="button" id="registerPasswordToggle" class="password-toggle">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="registerConfirmPassword">
                                <span class="required">*</span> تأكيد كلمة المرور
                            </label>
                            <div class="password-field">
                                <input type="password" id="registerConfirmPassword" name="Input.ConfirmPassword" class="form-control" required />
                                <button type="button" id="registerConfirmPasswordToggle" class="password-toggle">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                        </div>
                        <button type="submit" class="submit-btn">تسجيل</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <main role="main">
        <!-- رمز التحقق من CSRF للطلبات AJAX -->
        @Html.AntiForgeryToken()

        <div class="container py-4">
            <div class="row">
                @if (User.IsInRole("Admin") &&
                    (ViewContext.RouteData.Values["Controller"]?.ToString() == "Admin" ||
                     ViewContext.RouteData.Values["Controller"]?.ToString() == "CarouselImages"))
                {
                    <div class="col-md-3 d-none d-md-block">
                        <partial name="_AdminMenuPartial" />
                    </div>
                    <div class="col-md-9">
                        @RenderBody()
                    </div>
                }
                else
                {
                    <div class="col-12">
                        @RenderBody()
                    </div>
                }
            </div>
        </div>
    </main>

    <!-- شريط التنقل السفلي للهواتف المحمولة -->
    @await Html.PartialAsync("_MobileNavPartial")

    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-column">
                    <h3 class="footer-title">راعي المخور</h3>
                    <div class="footer-social">
                        <a href="#"><i class="bi bi-facebook"></i></a>
                        <a href="#"><i class="bi bi-instagram"></i></a>
                        <a href="#"><i class="bi bi-twitter-x"></i></a>
                        <a href="#"><i class="bi bi-snapchat"></i></a>
                    </div>
                </div>
                 
                <div class="footer-column1">
                    <h3 class="footer-title">الفئات</h3>
                    @await Component.InvokeAsync("FooterCategories")
                </div>
            </div>
            <div class="footer-bottom">
                &copy; @DateTime.Now.Year - جميع الحقوق محفوظة لراعي المخور
            </div>
        </div>
    </footer>

    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- ملفات JavaScript الأساسية -->
    <script src="~/js/site.js" asp-append-version="true"></script>
    <script src="~/js/product-actions.js" asp-append-version="true"></script>
    <script src="~/js/wishlist-actions.js" asp-append-version="true"></script>





    <script>
        // كود مبسط للتطبيق - تم نقل جميع دوال المفضلة والسلة إلى ملفات منفصلة
        $(document).ready(function () {
            console.log('تم تحميل التطبيق بنجاح');
        });

        // ملاحظة: تم نقل جميع دوال المفضلة والسلة إلى ملفات منفصلة

        // وظائف التنقل البسيطة
        function goToWishlist(event) {
            if (event) event.preventDefault();
            window.location.href = '@Url.Action("Index", "Wishlist")';
        }

        function goToCompare(event) {
            if (event) event.preventDefault();
            window.location.href = '@Url.Action("Index", "Compare")';
        }

        function goToCart(event) {
            if (event) event.preventDefault();
            window.location.href = '@Url.Action("Index", "Cart")';
        }

        // ملاحظة: تم نقل جميع دوال تحديث العدادات إلى ملفات منفصلة
    </script>
    <script src="~/js/modal-fix.js" asp-append-version="true"></script>
    <script src="~/js/display-options.js" asp-append-version="true"></script>
    <script src="~/js/active-link.js" asp-append-version="true"></script>
    <script src="~/js/side-login.js" asp-append-version="true"></script>
    <script src="~/js/mobile-sidebar.js" asp-append-version="true"></script>

    <!-- ملاحظة: تم نقل جميع event listeners للسلة إلى ملف product-actions.js لتجنب التكرار -->
    <script src="~/js/side-filter.js" asp-append-version="true"></script>
    <script src="~/js/main-best-sellers.js" asp-append-version="true"></script>
    <script src="~/js/sticky-nav.js" asp-append-version="true"></script>
    <script src="~/js/sticky-nav-active.js" asp-append-version="true"></script>
    <script src="~/js/compare-actions.js" asp-append-version="true"></script>


    <script>
        // كود مبسط للتطبيق - تم نقل جميع دوال المفضلة والسلة إلى ملفات منفصلة
        $(document).ready(function () {
            console.log('تم تحميل التطبيق بنجاح');

            // تحديث فوري لجميع العدادات من الـ cookies
            setTimeout(function() {
                if (window.updateCompareCountFromCookies) {
                    console.log('تحديث فوري لعداد المقارنة من الـ cookies...');
                    window.updateCompareCountFromCookies();
                }

                if (window.updateWishlistCountFromCookies) {
                    console.log('تحديث فوري لعداد المفضلة من الـ cookies...');
                    window.updateWishlistCountFromCookies();
                }

                if (window.updateCartCountFromCookies) {
                    console.log('تحديث فوري لعداد السلة من الـ cookies...');
                    window.updateCartCountFromCookies();
                }
            }, 100);

            // تحديث جميع العدادات من الخادم
            setTimeout(function() {
                if (window.updateCompareCount) {
                    console.log('تحديث عداد المقارنة من الخادم...');
                    window.updateCompareCount();
                }

                if (window.updateWishlistCount) {
                    console.log('تحديث عداد المفضلة من الخادم...');
                    window.updateWishlistCount();
                }

                if (window.updateCartCount) {
                    console.log('تحديث عداد السلة من الخادم...');
                    window.updateCartCount();
                }
            }, 500);

            // تحديث دوري للعدادات كل 30 ثانية للتأكد من الدقة
            setInterval(function() {
                if (window.updateCompareCountFromCookies) {
                    window.updateCompareCountFromCookies();
                }
                if (window.updateWishlistCountFromCookies) {
                    window.updateWishlistCountFromCookies();
                }
                if (window.updateCartCountFromCookies) {
                    window.updateCartCountFromCookies();
                }
            }, 30000);
        });

        // ملاحظة: تم نقل جميع دوال المفضلة والسلة إلى ملفات منفصلة

        // وظائف التنقل البسيطة
        function goToWishlist(event) {
            if (event) event.preventDefault();
            window.location.href = '@Url.Action("Index", "Wishlist")';
        }

        function goToCompare(event) {
            if (event) event.preventDefault();
            window.location.href = '@Url.Action("Index", "Compare")';
        }

        function goToCart(event) {
            if (event) event.preventDefault();
            window.location.href = '@Url.Action("Index", "Cart")';
        }

        // ملاحظة: تم نقل جميع دوال تحديث العدادات إلى ملفات منفصلة
    </script>

    @await RenderSectionAsync("Scripts", required: false)
    @await RenderSectionAsync("Styles", required: false)
</body>
</html>