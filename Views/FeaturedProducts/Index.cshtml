@model IEnumerable<Abayat.Models.FeaturedProduct>

@{
    ViewData["Title"] = "إدارة المنتجات المميزة";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="admin-section-title">
                    <i class="bi bi-star-fill text-warning"></i>
                    إدارة المنتجات المميزة
                </h2>
                <div>
                    <a href="@Url.Action("Create")" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> إضافة منتج مميز
                    </a>
                    <form method="post" action="@Url.Action("AutoPopulate")" style="display: inline;">
                        @Html.AntiForgeryToken()
                        <button type="submit" class="btn btn-success" onclick="return confirm('هل تريد إضافة 6 منتجات مميزة تلقائياً؟ سيتم حذف المنتجات المميزة الحالية.')">
                            <i class="bi bi-magic"></i> إضافة تلقائية
                        </button>
                    </form>
                </div>
            </div>

            @if (TempData["Success"] != null)
            {
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="bi bi-check-circle"></i> @TempData["Success"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            @if (TempData["Error"] != null)
            {
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="bi bi-exclamation-triangle"></i> @TempData["Error"]
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            }

            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-list-ol"></i>
                        المنتجات المميزة الحالية (@Model.Count())
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>الترتيب</th>
                                        <th>الصورة</th>
                                        <th>اسم المنتج</th>
                                        <th>السعر</th>
                                        <th>تاريخ الإضافة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var item in Model)
                                    {
                                        <tr>
                                            <td>
                                                <span class="badge bg-primary fs-6">
                                                    @if (item.DisplayOrder == 1) { <text>🥇</text> }
                                                    else if (item.DisplayOrder == 2) { <text>🥈</text> }
                                                    else if (item.DisplayOrder == 3) { <text>🥉</text> }
                                                    else { <text>#@item.DisplayOrder</text> }
                                                </span>
                                            </td>
                                            <td>
                                                <img src="@(item.Product.ImageUrl ?? "/images/placeholder.jpg")" 
                                                     alt="@item.Product.Name" 
                                                     class="img-thumbnail" 
                                                     style="width: 60px; height: 60px; object-fit: cover;">
                                            </td>
                                            <td>
                                                <strong>@item.Product.Name</strong>
                                                <br>
                                                <small class="text-muted">ID: @item.Product.Id</small>
                                            </td>
                                            <td>
                                                <span class="fw-bold text-primary">@item.Product.Price ر.ع</span>
                                            </td>
                                            <td>
                                                <small>@item.CreatedAt.ToString("dd/MM/yyyy HH:mm")</small>
                                            </td>
                                            <td>
                                                <form method="post" action="@Url.Action("Delete", new { id = item.Id })" style="display: inline;">
                                                    @Html.AntiForgeryToken()
                                                    <button type="submit" class="btn btn-sm btn-outline-danger" 
                                                            onclick="return confirm('هل تريد حذف هذا المنتج من المنتجات المميزة؟')"
                                                            title="حذف من المميزة">
                                                        <i class="bi bi-trash"></i>
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="bi bi-star display-1 text-muted"></i>
                            <h4 class="text-muted mt-3">لا توجد منتجات مميزة</h4>
                            <p class="text-muted">قم بإضافة منتجات مميزة لعرضها في قسم "الأكثر مبيعاً"</p>
                            <a href="@Url.Action("Create")" class="btn btn-primary">
                                <i class="bi bi-plus-circle"></i> إضافة أول منتج مميز
                            </a>
                        </div>
                    }
                </div>
            </div>

            <div class="mt-4">
                <div class="alert alert-info">
                    <h6><i class="bi bi-info-circle"></i> معلومات مهمة:</h6>
                    <ul class="mb-0">
                        <li>المنتجات المميزة تظهر في قسم "الأكثر مبيعاً" في صفحة المنتجات</li>
                        <li>يمكن عرض حتى 6 منتجات مميزة كحد أقصى</li>
                        <li>الترتيب يحدد أولوية العرض (1 = الأول، 2 = الثاني، إلخ)</li>
                        <li>المراكز الثلاثة الأولى تحصل على ميداليات خاصة (🥇🥈🥉)</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.admin-section-title {
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: 0;
}

.table th {
    border-top: none;
    font-weight: 600;
}

.img-thumbnail {
    border: 2px solid #dee2e6;
    transition: all 0.3s ease;
}

.img-thumbnail:hover {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.badge {
    font-size: 0.9rem !important;
}
</style>
