@{
    ViewData["Title"] = "إضافة منتج مميز";
    var availableProducts = ViewBag.AvailableProducts as List<Abayat.Models.Product>;
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="admin-section-title">
                    <i class="bi bi-plus-circle text-success"></i>
                    إضافة منتج مميز
                </h2>
                <a href="@Url.Action("Index")" class="btn btn-secondary">
                    <i class="bi bi-arrow-right"></i> رجوع للقائمة
                </a>
            </div>

            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-star"></i>
                        اختيار منتج لإضافته للمنتجات المميزة
                    </h5>
                </div>
                <div class="card-body">
                    @if (availableProducts != null && availableProducts.Any())
                    {
                        <form method="post" action="@Url.Action("Create")">
                            @Html.AntiForgeryToken()
                            
                            <div class="mb-4">
                                <label class="form-label fw-bold">اختر المنتج:</label>
                                <div class="row">
                                    @foreach (var product in availableProducts)
                                    {
                                        <div class="col-md-6 col-lg-4 mb-3">
                                            <div class="card product-selection-card">
                                                <div class="card-body text-center">
                                                    <img src="@(product.ImageUrl ?? "/images/placeholder.jpg")" 
                                                         alt="@product.Name" 
                                                         class="img-fluid mb-3" 
                                                         style="width: 100px; height: 100px; object-fit: cover; border-radius: 8px;">
                                                    
                                                    <h6 class="card-title">@product.Name</h6>
                                                    <p class="text-primary fw-bold">@product.Price ر.ع</p>
                                                    
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="productId" value="@product.Id" id="<EMAIL>">
                                                        <label class="form-check-label" for="<EMAIL>">
                                                            اختيار هذا المنتج
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                </div>
                            </div>

                            <div class="text-center">
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="bi bi-star-fill"></i>
                                    إضافة للمنتجات المميزة
                                </button>
                                <a href="@Url.Action("Index")" class="btn btn-secondary btn-lg ms-2">
                                    <i class="bi bi-x-circle"></i>
                                    إلغاء
                                </a>
                            </div>
                        </form>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="bi bi-exclamation-triangle display-1 text-warning"></i>
                            <h4 class="text-muted mt-3">لا توجد منتجات متاحة</h4>
                            <p class="text-muted">جميع المنتجات المتاحة مميزة بالفعل أو لا توجد منتجات في النظام</p>
                            <a href="@Url.Action("Index")" class="btn btn-primary">
                                <i class="bi bi-arrow-right"></i> رجوع للقائمة
                            </a>
                        </div>
                    }
                </div>
            </div>

            <div class="mt-4">
                <div class="alert alert-info">
                    <h6><i class="bi bi-lightbulb"></i> نصائح:</h6>
                    <ul class="mb-0">
                        <li>اختر المنتجات الأكثر شعبية أو التي تريد الترويج لها</li>
                        <li>سيتم إضافة المنتج في آخر ترتيب عرض</li>
                        <li>يمكن إضافة حتى 6 منتجات مميزة كحد أقصى</li>
                        <li>المنتجات المميزة تظهر في قسم "الأكثر مبيعاً" في صفحة المنتجات</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.admin-section-title {
    color: var(--primary-color);
    font-weight: 700;
    margin-bottom: 0;
}

.product-selection-card {
    transition: all 0.3s ease;
    border: 2px solid transparent;
    cursor: pointer;
}

.product-selection-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 4px 15px rgba(106, 13, 173, 0.15);
    transform: translateY(-2px);
}

.form-check-input:checked ~ .form-check-label {
    color: var(--primary-color);
    font-weight: bold;
}

.product-selection-card:has(.form-check-input:checked) {
    border-color: var(--primary-color);
    background-color: rgba(106, 13, 173, 0.05);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // جعل النقر على الكارد يختار المنتج
    document.querySelectorAll('.product-selection-card').forEach(card => {
        card.addEventListener('click', function() {
            const radio = this.querySelector('input[type="radio"]');
            if (radio) {
                radio.checked = true;
                
                // إزالة التحديد من الكروت الأخرى
                document.querySelectorAll('.product-selection-card').forEach(otherCard => {
                    otherCard.classList.remove('selected');
                });
                
                // إضافة التحديد للكارد الحالي
                this.classList.add('selected');
            }
        });
    });
});
</script>
