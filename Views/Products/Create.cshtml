@model Abayat.Models.Product

@{
    ViewData["Title"] = "إضافة منتج جديد";
    var categories = ViewData["Categories"] as SelectList;
}

<div class="products-header text-center">
    <div class="container">
        <h1 class="fade-in">@ViewData["Title"]</h1>
        <p class="lead mb-4">قم بإضافة منتج جديد لعرضه في المتجر</p>
    </div>
</div>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="form-card zoom-in">
                <h2 class="form-title">بيانات المنتج</h2>

                <form asp-action="Create" enctype="multipart/form-data">
                    <div asp-validation-summary="ModelOnly" class="validation-summary-errors"></div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="Name" class="form-label">اسم المنتج</label>
                                <input asp-for="Name" class="form-control" placeholder="أدخل اسم المنتج" />
                                <span asp-validation-for="Name" class="field-validation-error"></span>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="Price" class="form-label">السعر</label>
                                <div class="input-group">
                                    <span class="input-group-text">ر.ع</span>
                                    <input asp-for="Price" class="form-control" placeholder="0.00" id="priceInput" />
                                </div>
                                <span asp-validation-for="Price" class="field-validation-error"></span>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="DiscountPercentage" class="form-label">نسبة الخصم</label>
                                <div class="input-group">
                                    <input asp-for="DiscountPercentage" class="form-control" placeholder="14" id="discountInput" min="0" max="100" />
                                    <span class="input-group-text">%</span>
                                </div>
                                <span asp-validation-for="DiscountPercentage" class="field-validation-error"></span>
                            </div>
                        </div>
                    </div>

                    <!-- خانة عرض قيمة الخصم والسعر بعد التخفيض -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="discount-info-card">
                                <h6 class="discount-info-title">
                                    <i class="bi bi-percent text-success"></i>
                                    معلومات الخصم والتسعير
                                </h6>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="info-item">
                                            <label class="info-label">نسبة الخصم:</label>
                                            <div class="info-value discount-percentage" id="discountPercentageDisplay">@(Model?.DiscountPercentage ?? 14)%</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="info-item">
                                            <label class="info-label">قيمة الخصم:</label>
                                            <div class="info-value discount-amount" id="discountAmount">0.000 ر.ع</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="info-item">
                                            <label class="info-label">السعر بعد الخصم:</label>
                                            <div class="info-value final-price" id="finalPrice">0.000 ر.ع</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label asp-for="Description" class="form-label">وصف المنتج</label>
                        <textarea asp-for="Description" class="form-control" rows="4" placeholder="أدخل وصف المنتج بالتفصيل"></textarea>
                        <span asp-validation-for="Description" class="field-validation-error"></span>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="CategoryId" class="form-label">الفئة</label>
                                <select asp-for="CategoryId" asp-items="categories" class="form-select">
                                    <option value="">-- اختر الفئة --</option>
                                </select>
                                <span asp-validation-for="CategoryId" class="field-validation-error"></span>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="imageFile" class="form-label">صورة المنتج</label>
                                <input type="file" name="imageFile" id="imageFile" class="form-control" accept="image/*" />
                                <small class="text-muted">يفضل صورة بحجم 800x600 بكسل</small>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="ProductType" class="form-label">نوع المنتج</label>
                                <input asp-for="ProductType" class="form-control" placeholder="مثل: كاجوال، رسمي، مطرز" />
                                <span asp-validation-for="ProductType" class="field-validation-error"></span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group mt-3">
                                <div class="form-check">
                                    <input asp-for="IsAvailable" class="form-check-input" />
                                    <label asp-for="IsAvailable" class="form-check-label">متوفر للبيع</label>
                                </div>
                                <div class="form-check mt-2">
                                    <input asp-for="ShowInCarousel" class="form-check-input" />
                                    <label asp-for="ShowInCarousel" class="form-check-label">عرض في الشريط المتحرك</label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <a asp-action="Index" class="btn btn-outline-secondary form-btn">
                            <i class="bi bi-arrow-right"></i> العودة إلى القائمة
                        </a>
                        <button type="submit" class="btn btn-primary form-btn">
                            <i class="bi bi-check-circle"></i> إضافة المنتج
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />

    <style>
        .discount-info-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 2px solid #dee2e6;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .discount-info-title {
            color: #495057;
            font-weight: 600;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .info-item {
            text-align: center;
            padding: 10px;
        }

        .info-label {
            font-size: 0.9rem;
            color: #6c757d;
            font-weight: 500;
            margin-bottom: 5px;
            display: block;
        }

        .info-value {
            font-size: 1.1rem;
            font-weight: 700;
            padding: 8px 12px;
            border-radius: 8px;
            display: inline-block;
            min-width: 120px;
        }

        .discount-percentage {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .discount-amount {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .final-price {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        /* تحديث القيم عند تغيير السعر */
        .info-value.updating {
            transform: scale(1.05);
            transition: transform 0.3s ease;
        }
    </style>

    <script>
        $(document).ready(function() {
            // دالة لتحديث قيم الخصم
            function updateDiscountValues() {
                const price = parseFloat($('#priceInput').val()) || 0;
                const discountPercentage = parseFloat($('#discountInput').val()) || 0;
                const discountAmount = price * discountPercentage / 100;
                const finalPrice = price - discountAmount;

                // تحديث نسبة الخصم المعروضة
                $('#discountPercentageDisplay').addClass('updating').text(discountPercentage + '%');

                // تحديث قيمة الخصم
                $('#discountAmount').addClass('updating').text(discountAmount.toLocaleString('en-UK', {
                    minimumFractionDigits: 3,
                    maximumFractionDigits: 3
                }) + ' ر.ع');

                // تحديث السعر النهائي
                $('#finalPrice').addClass('updating').text(finalPrice.toLocaleString('en-UK', {
                    minimumFractionDigits: 3,
                    maximumFractionDigits: 3
                }) + ' ر.ع');

                // إزالة تأثير التحديث بعد فترة
                setTimeout(function() {
                    $('.info-value').removeClass('updating');
                }, 500);
            }

            // تحديث قيم الخصم عند تغيير السعر أو نسبة الخصم
            $('#priceInput, #discountInput').on('input', updateDiscountValues);

            // تحديث القيم عند تحميل الصفحة
            updateDiscountValues();
        });
    </script>
}
