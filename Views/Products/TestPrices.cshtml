@model List<Abayat.Models.Product>

@{
    ViewData["Title"] = "اختبار الأسعار";
}

<div class="container mt-4">
    <h2>اختبار حساب الأسعار</h2>
    
    <div class="table-responsive">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>اسم المنتج</th>
                    <th>السعر الأصلي</th>
                    <th>نسبة الخصم</th>
                    <th>قيمة الخصم المحسوبة</th>
                    <th>السعر بعد الخصم (النموذج)</th>
                    <th>السعر بعد الخصم (محسوب يدوياً)</th>
                    <th>الفرق</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var product in Model)
                {
                    var calculatedDiscountAmount = product.Price * product.DiscountPercentage / 100m;
                    var calculatedDiscountedPrice = product.Price - calculatedDiscountAmount;
                    var difference = product.DiscountedPrice - calculatedDiscountedPrice;
                    
                    <tr class="@(Math.Abs(difference) > 0.01m ? "table-warning" : "")">
                        <td>@product.Name</td>
                        <td>@product.Price.ToString("N2") ر.ع</td>
                        <td>@product.DiscountPercentage%</td>
                        <td>@calculatedDiscountAmount.ToString("N2") ر.ع</td>
                        <td>@product.DiscountedPrice.ToString("N2") ر.ع</td>
                        <td>@calculatedDiscountedPrice.ToString("N2") ر.ع</td>
                        <td class="@(Math.Abs(difference) > 0.01m ? "text-danger fw-bold" : "text-success")">
                            @difference.ToString("N2") ر.ع
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>
    
    <div class="alert alert-info">
        <strong>ملاحظة:</strong> الصفوف المميزة باللون الأصفر تشير إلى وجود فرق في الحساب.
    </div>
</div>
