@model Abayat.Models.PaginatedList<Abayat.Models.Product>

@{
    var pageSize = ViewData["PageSize"] as int? ?? 16;
    var selectedCategory = ViewData["SelectedCategory"] as string;
    var categories = ViewData["Categories"] as List<Abayat.Models.Category>;

    if (!string.IsNullOrEmpty(selectedCategory) && categories != null)
    {
        var category = categories.FirstOrDefault(c => c.Slug == selectedCategory);
        if (category != null)
        {
            ViewData["Title"] = "منتجات " + category.Name;
        }
        else
        {
            ViewData["Title"] = "جميع المنتجات";
        }
    }
    else
    {
        ViewData["Title"] = "جميع المنتجات";
    }
}

@* Token antifalsificación para solicitudes AJAX *@
@Html.AntiForgeryToken()

@section Styles {
    <link rel="stylesheet" href="~/css/products-page.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/product-display.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/mobile-sidebar.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/side-filter.css" asp-append-version="true" />
    <style>
        /* CSS مطابق لنافذة التسجيل */
        .mobile-sidebar-overlay {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background-color: rgba(0, 0, 0, 0.5) !important;
            z-index: 1000 !important;
            display: none !important;
        }

        .mobile-sidebar {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 400px !important;
            height: 100% !important;
            background-color: white !important;
            z-index: 1001 !important;
            overflow-y: auto !important;
            transform: translateX(-100%) !important;
            transition: transform 0.3s ease-in-out !important;
            box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1) !important;
        }

        .mobile-sidebar.active {
            transform: translateX(0) !important;
        }

        .mobile-sidebar-header {
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
            padding: 20px !important;
            border-bottom: 1px solid #eee !important;
            flex-direction: row-reverse !important;
        }

        .mobile-sidebar-title {
            margin: 0 !important;
            font-size: 1.5rem !important;
            font-weight: 600 !important;
            color: #333 !important;
            text-align: right !important;
        }

        .mobile-sidebar-close {
            background: none !important;
            border: none !important;
            font-size: 1.5rem !important;
            cursor: pointer !important;
            color: #666 !important;
            padding: 0 !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }

        .mobile-sidebar-content {
            padding: 20px !important;
        }

        .mobile-sidebar-section {
            margin-bottom: 25px !important;
            padding: 15px !important;
            background: #f8f9fa !important;
            border: 1px solid #ddd !important;
            border-radius: 8px !important;
        }

        .mobile-sidebar-section-title {
            font-size: 16px !important;
            font-weight: 600 !important;
            color: #333 !important;
            margin-bottom: 15px !important;
            display: flex !important;
            align-items: center !important;
        }

        .mobile-grid-controls {
            display: flex !important;
            gap: 10px !important;
            justify-content: space-around !important;
            flex-wrap: wrap !important;
        }

        .mobile-grid-option {
            display: flex !important;
            flex-direction: column !important;
            align-items: center !important;
            padding: 10px !important;
            background: white !important;
            border: 0px solid #ddd !important;
            border-radius: 8px !important;
            cursor: pointer !important;
            min-width: 6px !important;
            text-align: center !important;
        }

        .mobile-grid-option.active {
            background: #007bff !important;
            color: white !important;
        }

        /* للشاشات الصغيرة */
        @@media (max-width: 576px) {
            .mobile-sidebar {
                width: 100% !important;
            }
        }
    </style>
}

<div class="products-header">
    <div class="container">
        <div class="products-title-container">
            <div class="breadcrumb-container">
                <a asp-controller="Home" asp-action="Index" class="breadcrumb-item">الرئيسية</a>
                <span class="breadcrumb-separator">/</span>
                <span class="breadcrumb-item">@ViewData["Title"]</span>
            </div>
            <h1>@ViewData["Title"]</h1>
        </div>
    </div>
</div>

<div class="container">
    <div class="products-container">
        <div class="products-sidebar">
            <div class="price-filter-container">
                <h3 class="price-filter-title">تصفية حسب السعر</h3>
                <input type="range" class="price-range-slider" min="0" max="1000" value="1000" id="priceRangeSlider">
                <div class="price-inputs">
                    <input type="text" class="price-input" value="0 ر.ع" id="minPrice" readonly>
                    <input type="text" class="price-input" value="1000 ر.ع" id="maxPrice" readonly>
                </div>
                <button class="price-filter-button" id="applyPriceFilter">تطبيق التصفية</button>
            </div>

            <!-- قسم المنتجات الأكثر مبيعاً -->
            <div class="best-sellers-section">
                <h3 class="best-sellers-title collapsible-header" onclick="toggleBestSellersSection()">
                    <span>
                        <i class="bi bi-star-fill"></i>
                        الأكثر مبيعاً
                        <span class="best-sellers-count" id="bestSellersCount">(0)</span>
                    </span>
                    <i class="bi bi-chevron-down toggle-icon" id="bestSellersToggle"></i>
                </h3>
                <div id="bestSellersContainer" class="best-sellers-container">
                    <div class="loading-spinner">جاري التحميل...</div>
                </div>
            </div>

        </div>

        <div class="products-main">
            <div class="view-options">
                <!-- زر تحديد العرض للهواتف -->
              

                <!-- أيقونات العرض للشاشات الكبيرة -->

             
                    <div class="view-mode d-flex">
                    <div class="flex-container d-flex p-1">
                         <div class="view-mode-option" data-view="grid-6" title="عرض 6 منتجات في الصف">
                            <i class="bi bi-grid-3x3-gap-fill"></i>
                        </div>
                        <div class="view-mode-option" data-view="grid-4" title="عرض 4 منتجات في الصف">
                            <i class="bi bi-grid-fill"></i>
                        </div>
                        <div class="view-mode-option active" data-view="grid-2" title="عرض منتجين في الصف">
                            <i class="bi bi-layout-split"></i>
                        </div>
                    </div> 
                    </div>
                


             
 <!-- أيقونات العرض للشاشات الصغيرة -->
                <div class="mobile-view-controls d-block d-lg-none">
                    <div class="mobile-view-options">
                        <div class="view-mode-option mobile-view" data-view="grid-2" title="عرض منتجين في الصف">
                            <i class="bi bi-layout-split"></i>
                        </div>
                        <div class="view-mode-option mobile-view" data-view="grid-3" title="عرض 3 منتجات في الصف">
                            <i class="bi bi-grid-3x2-gap"></i>
                        </div>
                        <div class="view-mode-option mobile-view active" data-view="grid-4" title="عرض 4 منتجات في الصف">
                            <i class="bi bi-grid-fill"></i>
                        </div>
                    </div>
                </div>


                <div class="sort-options">
                    <span class="sort-label">الترتيب:</span>
                    <select class="sort-select" id="productSort">
                         <option value="default">الترتيب الافتراضي</option>
                        <option value="newest">الأحدث</option>
                        <option value="price-asc">السعر: من الأقل إلى الأعلى</option>
                        <option value="price-desc">السعر: من الأعلى إلى الأقل</option>
                    </select>
                </div>
                <div class="col-md-6 d-flex justify-content-between align-items-center">
                   <div>
                        <span class="">عدد المنتجات:</span>
                        <select class="items-per-page-select" id="itemsPerPage">
                            <option value="6">6</option>
                            <option value="12" selected>12</option>
                            <option value="16">16</option>
                            <option value="24">24</option>
                            <option value="36">36</option>
                      </select>
                    </div>
                    <div class="products-count d-none d-sm-block">
                        تظهر: <span>@Model.Count</span> من أصل <span>@Model.TotalItems</span> منتج
                    </div>
                </div>

                <!-- أزرار تغيير العرض والتصفية للهواتف -->
                
                <div class="mobile-controls-top d-block d-lg-none">
                    <div class="mobile-controls-row">
                        <!-- أزرار تغيير العرض -->
                        <div class="mobile-grid-controls-horizontal">
                            <div class="mobile-grid-option active" data-mobile-view="mobile-grid-1" title="منتج واحد">
                                <i class="bi bi-square-fill"></i>
                                
                            </div>
                            <div class="mobile-grid-option" data-mobile-view="mobile-grid-2" title="منتجين جنب بعض">
                                <i class="bi bi-grid-fill"></i>
                                 
                            </div>
                            <div class="mobile-grid-option" data-mobile-view="mobile-grid-3" title="ثلاث منتجات">
                                <i class="bi bi-grid-3x3-gap-fill"></i>
                        
                            </div>
                        </div>


                    </div>
                </div>

                
            </div>

            <!-- قسم المنتجات الأكثر مبيعاً للهواتف -->
            <div class="mobile-best-sellers d-block d-lg-none mb-4" style="background: white; border-radius: 12px; padding: 0px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1); border: 1px solid #f0f0f0;">
                <div class="mobile-best-sellers-header" style="margin-bottom: 20px;">
                    <h4 class="mobile-best-sellers-title" style="font-size: 1.2rem; font-weight: 700; margin: 0; color: #333; display: flex; align-items: center; gap: 10px;">
                        <i class="bi bi-star-fill text-warning"></i>
                        الأكثر مبيعاً
                        <span class="mobile-best-sellers-count" id="mobileBestSellersCount" style="font-size: 0.9rem; color: #6a0dad; font-weight: 500; background: rgba(106, 13, 173, 0.1); padding: 4px 10px; border-radius: 15px; margin-right: 10px;">(0)</span>
                    </h4>
                </div>
                <div id="mobileBestSellersContainer" class="mobile-best-sellers-container" style="display: flex; overflow-x: auto; gap: 15px; padding-bottom: 0px; scroll-behavior: smooth;">
                    <div class="loading-spinner" style="text-align: center; padding: 30px 20px; color: #6a0dad; font-size: 0.9rem;">جاري التحميل...</div>
                </div>
            </div>

            <div class="product-grid">
                @foreach (var item in Model)
                {
                    <div class="product-item animate__animated animate__fadeInUp">
                        <div class="product-item-img-container" data-product-id="@item.Id" style="cursor: pointer;" onclick="if (!event.target.closest('.product-item-actions') && !event.target.closest('.product-action-btn')) { window.location.href='/Products/Details/@item.Id'; }">
                            @if (!string.IsNullOrEmpty(item.ImageUrl))
                            {
                                <img src="@item.ImageUrl" class="product-item-img" alt="@item.Name">
                            }
                            else
                            {
                                <img src="https://placehold.co/600x400/6a0dad/ffffff?text=@item.Name" class="product-item-img" alt="@item.Name">
                            }

                            <div class="product-item-discount">-@item.DiscountPercentage%</div>

                            <div class="product-item-availability @(item.IsAvailable ? "available" : "not-available")">
                                @(item.IsAvailable ? "متوفر" : "غير متوفر")
                            </div>

                            <!-- زر المفضلة في الزاوية العلوية -->
                           

                            <div class="product-item-actions">
                                <button class="product-item-action-btn add-to-wishlist-btn" data-product-id="@item.Id" title="إضافة للمفضلة">
                                    <i class="bi bi-heart"></i>
                                    <i class="bi bi-heart-fill" style="display: none;"></i>
                                </button>
                                <button class="product-item-action-btn add-to-cart-btn" data-product-id="@item.Id" title="إضافة للسلة">
                                    <i class="bi bi-cart-plus"></i>
                                </button>
                                <button class="product-item-action-btn add-to-compare-btn" data-product-id="@item.Id" title="إضافة للمقارنة">
                                    <i class="bi bi-arrow-left-right"></i>
                                </button>
                                @if (User.IsInRole("Admin"))
                                {
                                    <button class="product-item-action-btn edit-product-btn"
                                            data-product-id="@item.Id"
                                            title="تعديل المنتج"
                                            onclick="console.log('تم النقر على زر التعديل للمنتج: @item.Id'); window.location.href='/Products/Edit/@item.Id';">
                                        <i class="bi bi-pencil-square"></i>
                                    </button>
                                }
                            </div>

                            @if (User.IsInRole("Admin"))
                            {
                                <div class="product-actions-menu">
                                    <button class="product-action-btn toggle-carousel-btn @(item.ShowInCarousel ? "active" : "")"
                                            data-product-id="@item.Id"
                                            title="@(item.ShowInCarousel ? "إزالة من الشريط المتحرك" : "إضافة إلى الشريط المتحرك")">
                                        <i class="bi bi-collection-play"></i>
                                    </button>

                                    <button class="product-action-btn toggle-availability-btn @(item.IsAvailable ? "available" : "not-available")"
                                            data-product-id="@item.Id"
                                            title="@(item.IsAvailable ? "تعيين كغير متوفر" : "تعيين كمتوفر")">
                                        <i class="bi bi-@(item.IsAvailable ? "bag-check" : "bag-x")"></i>
                                    </button>
                                </div>
                            }
                        </div>
                        <div class="product-item-info">
                            <h3 class="product-item-title">@item.Name</h3>
                            <div class="product-item-price">
                                <div class="product-item-price-current">@item.DiscountedPrice.ToString("N0") ر.ع</div>
                                <div class="product-item-price-original">@item.Price.ToString("N0") ر.ع</div>
                            </div>
                        </div>
                        <a href="/Products/Details/@item.Id" class="product-link"></a>
                    </div>
                }
            </div>

            <!-- Pagination -->
            <div class="pagination-container mt-4">
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        @{
                            var prevDisabled = !Model.HasPreviousPage ? "disabled" : "";
                            var nextDisabled = !Model.HasNextPage ? "disabled" : "";
                        }

                        <li class="page-item @prevDisabled">
                            <a class="page-link" asp-action="Index" asp-route-pageNumber="1" asp-route-pageSize="@pageSize" asp-route-category="@selectedCategory" aria-label="First">
                                <i class="bi bi-chevron-double-right"></i>
                            </a>
                        </li>
                        <li class="page-item @prevDisabled">
                            <a class="page-link" asp-action="Index" asp-route-pageNumber="@(Model.PageIndex - 1)" asp-route-pageSize="@pageSize" asp-route-category="@selectedCategory" aria-label="Previous">
                                <i class="bi bi-chevron-right"></i>
                            </a>
                        </li>

                        @{
                            int startPage = Math.Max(1, Model.PageIndex - 2);
                            int endPage = Math.Min(Model.TotalPages, startPage + 4);

                            if (endPage - startPage < 4 && Model.TotalPages > 4)
                            {
                                startPage = Math.Max(1, endPage - 4);
                            }
                        }

                        @for (int i = startPage; i <= endPage; i++)
                        {
                            <li class="page-item @(i == Model.PageIndex ? "active" : "")">
                                <a class="page-link" asp-action="Index" asp-route-pageNumber="@i" asp-route-pageSize="@pageSize" asp-route-category="@selectedCategory">@i</a>
                            </li>
                        }

                        <li class="page-item @nextDisabled">
                            <a class="page-link" asp-action="Index" asp-route-pageNumber="@(Model.PageIndex + 1)" asp-route-pageSize="@pageSize" asp-route-category="@selectedCategory" aria-label="Next">
                                <i class="bi bi-chevron-left"></i>
                            </a>
                        </li>
                        <li class="page-item @nextDisabled">
                            <a class="page-link" asp-action="Index" asp-route-pageNumber="@Model.TotalPages" asp-route-pageSize="@pageSize" asp-route-category="@selectedCategory" aria-label="Last">
                                <i class="bi bi-chevron-double-left"></i>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>

            @if (User.IsInRole("Admin"))
            {
                <div class="text-center mt-4">
                    <a asp-action="Create" class="btn btn-light">
                        <i class="bi bi-plus-circle me-2"></i> إضافة منتج جديد
                    </a>
                </div>
            }
        </div>
    </div>
</div>

<!-- نافذة تحديد العرض للهواتف -->


<!-- النافذة الجانبية للهواتف -->
<div class="mobile-sidebar-overlay" id="mobileSidebarOverlay">
    <div class="mobile-sidebar" id="mobileSidebar">
        <div class="mobile-sidebar-header">
            <h5 class="mobile-sidebar-title">
                <i class="bi bi-funnel me-2"></i>
                تصفية وعرض المنتجات
            </h5>
            <button class="mobile-sidebar-close" id="mobileSidebarClose">
                <i class="bi bi-x-lg"></i>
            </button>
        </div>

        <div class="mobile-sidebar-content">
            <!-- أيقونات شبكة العرض -->
            <div class="mobile-sidebar-section">
                <h6 class="mobile-sidebar-section-title">
                    <i class="bi bi-grid me-2"></i>
                    طريقة العرض
                </h6>
                <div class="mobile-grid-controls">
                    <div class="mobile-grid-option" data-mobile-view="mobile-grid-1" title="منتج واحد">
                        <i class="bi bi-square"></i>
                        <span>منتج واحد</span>
                    </div>
                    <div class="mobile-grid-option active" data-mobile-view="mobile-grid-2" title="منتجين جنب بعض">
                        <i class="bi bi-grid-1x2"></i>
                        <span>منتجين</span>
                    </div>
                    <div class="mobile-grid-option" data-mobile-view="mobile-grid-3" title="ثلاث منتجات">
                        <i class="bi bi-grid-3x2"></i>
                        <span>ثلاث منتجات</span>
                    </div>
                </div>
            </div>

            <!-- الترتيب -->
            <div class="mobile-sidebar-section">
                <h6 class="mobile-sidebar-section-title">
                    <i class="bi bi-sort-down me-2"></i>
                    الترتيب
                </h6>
                <select class="mobile-sort-select" id="mobileSortSelect">
                    <option value="default">الترتيب الافتراضي</option>
                    <option value="newest">الأحدث</option>
                    <option value="price-asc">السعر: من الأقل إلى الأعلى</option>
                    <option value="price-desc">السعر: من الأعلى إلى الأقل</option>
                </select>
            </div>

            <!-- عدد المنتجات -->
            <div class="mobile-sidebar-section">
                <h6 class="mobile-sidebar-section-title">
                    <i class="bi bi-list-ol me-2"></i>
                    عدد المنتجات
                </h6>
                <select class="mobile-items-select" id="mobileItemsSelect">
                    <option value="6">6 منتجات</option>
                    <option value="12">12 منتج</option>
                    <option value="16">16 منتج</option>
                    <option value="24">24 منتج</option>
                    <option value="36">36 منتج</option>
                </select>
            </div>

            <!-- تصفية السعر -->
            <div class="mobile-sidebar-section">
                <h6 class="mobile-sidebar-section-title">
                    <i class="bi bi-currency-exchange me-2"></i>
                    تصفية حسب السعر
                </h6>
                <div class="mobile-price-filter">
                    <input type="range" class="mobile-price-slider" min="0" max="1000" value="1000" id="mobilePriceSlider">
                    <div class="mobile-price-inputs">
                        <input type="text" class="mobile-price-input" value="0 ر.ع" id="mobileMinPrice" readonly>
                        <input type="text" class="mobile-price-input" value="1000 ر.ع" id="mobileMaxPrice" readonly>
                    </div>
                    <button class="mobile-price-apply-btn" id="mobileApplyPriceFilter">تطبيق التصفية</button>
                </div>
            </div>
        </div>

        <div class="mobile-sidebar-footer">
            <button class="mobile-sidebar-apply-btn" id="mobileSidebarApply">
                <i class="bi bi-check-lg me-2"></i>
                تطبيق التغييرات
            </button>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/display-options.js" asp-append-version="true"></script>
    <script src="~/js/mobile-filter.js" asp-append-version="true"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            console.log('🚀 بدء تحميل صفحة المنتجات');
            console.log('👤 المستخدم مدير؟', @(User.IsInRole("Admin") ? "true" : "false"));

            // Inicializar controles de visualización
            console.log('🎛️ تهيئة أزرار العرض...');
            // استخدام دالة التهيئة من display-options.js
            if (typeof setupViewModeButtons === 'function') {
                setupViewModeButtons();
            } else {
                console.error('❌ دالة setupViewModeButtons غير موجودة');
            }

            console.log('📱 تهيئة عناصر التحكم للهواتف...');
            initMobileGridControls();
            initMobileSidebar();
            initPriceSlider();
            initSortControls();

            console.log('💜 تهيئة أزرار المفضلة والمقارنة...');
            initWishlistButtons();
            initCompareButtons();
            initItemsPerPage();
            updateCompareCount();

            // تحديث عداد السلة والمبلغ الإجمالي عند تحميل الصفحة
            if (typeof updateCartCount === 'function') {
                updateCartCount();
            }
            if (typeof updateCartTotal === 'function') {
                updateCartTotal();
            }

            // ملاحظة: تهيئة أزرار السلة تتم تلقائياً في product-actions.js

            // تهيئة أزرار التعديل للمدير
            console.log('✏️ تهيئة أزرار التعديل للمدير...');
            initEditProductButtons();

            // إضافة event delegation كطريقة بديلة
            document.addEventListener('click', function(e) {
                if (e.target.closest('.edit-product-btn')) {
                    console.log('🎯 تم النقر على زر التعديل عبر event delegation');
                    const button = e.target.closest('.edit-product-btn');
                    const productId = button.getAttribute('data-product-id');

                    if (productId) {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('الانتقال إلى صفحة التعديل للمنتج:', productId);
                        window.location.href = `/Products/Edit/${productId}`;
                    }
                }
            });

            // تهيئة أزرار إضافة المنتج إلى الشريط المتحرك وتبديل حالة التوفر (للمدير فقط)
            if (document.querySelector('.toggle-carousel-btn')) {
                initCarouselButtons();
                initAvailabilityButtons();
            }

            // تشخيص أزرار العرض بعد التهيئة
            setTimeout(() => {
                debugViewControls();
            }, 1000);

            console.log('✅ تم تحميل صفحة المنتجات بنجاح');
        });

        // دالة تشخيص أزرار العرض
        function debugViewControls() {
            console.log('🔍 تشخيص أزرار العرض:');

            const productGrid = document.querySelector('.product-grid');
            const viewModeOptions = document.querySelectorAll('.view-mode-option');

            console.log('- عنصر الشبكة:', productGrid);
            console.log('- عدد أزرار العرض:', viewModeOptions.length);
            console.log('- فئات الشبكة الحالية:', productGrid ? productGrid.className : 'غير موجود');

            viewModeOptions.forEach((option, index) => {
                const view = option.getAttribute('data-view');
                const isActive = option.classList.contains('active');
                console.log(`- الزر ${index + 1}: ${view}, نشط: ${isActive}`);
            });

            const savedView = localStorage.getItem('productViewMode');
            console.log('- العرض المحفوظ:', savedView);
        }

        // إضافة دالة التشخيص للنافذة العامة
        window.debugViewControls = debugViewControls;
        }

        // دالة تهيئة أيقونات شبكة العرض للهواتف
        function initMobileGridControls() {
            const productGrid = document.querySelector('.product-grid');
            const mobileGridOptions = document.querySelectorAll('.mobile-grid-option');

            if (!productGrid || mobileGridOptions.length === 0) {
                return; // لا توجد عناصر للتحكم في الهواتف
            }

            // إضافة مستمعي الأحداث لأيقونات الشبكة
            mobileGridOptions.forEach(option => {
                option.addEventListener('click', function() {
                    const mobileView = this.getAttribute('data-mobile-view');
                    console.log('Changing mobile view to:', mobileView);

                    // إزالة الفئات النشطة من جميع الأيقونات
                    mobileGridOptions.forEach(o => o.classList.remove('active'));

                    // تفعيل الأيقونة المحددة
                    this.classList.add('active');

                    // إزالة جميع فئات الشبكة المحمولة السابقة
                    productGrid.classList.remove('mobile-grid-1', 'mobile-grid-2', 'mobile-grid-3');

                    // إضافة فئة الشبكة الجديدة
                    productGrid.classList.add(mobileView);

                    // عرض رسالة تأكيد
                    let message = '';
                    switch(mobileView) {
                        case 'mobile-grid-1':
                            message = 'تم تغيير العرض إلى منتج واحد';
                            break;
                        case 'mobile-grid-2':
                            message = 'تم تغيير العرض إلى منتجين';
                            break;
                        case 'mobile-grid-3':
                            message = 'تم تغيير العرض إلى ثلاث منتجات';
                            break;
                    }
                    showToast(message);

                    // حفظ التفضيل
                    localStorage.setItem('mobileGridMode', mobileView);
                });
            });

            // تحميل التفضيل المحفوظ
            const savedMobileView = localStorage.getItem('mobileGridMode') || 'mobile-grid-1';
            const savedMobileOption = document.querySelector(`.mobile-grid-option[data-mobile-view="${savedMobileView}"]`);

            if (savedMobileOption) {
                savedMobileOption.click();
            }
        }

        // دالة تهيئة النافذة الجانبية للهواتف
        function initMobileSidebar() {
            const mobileDisplayToggle = document.getElementById('mobileDisplayToggle');
            const mobileSidebarOverlay = document.getElementById('mobileSidebarOverlay');
            const mobileSidebar = document.getElementById('mobileSidebar');
            const mobileSidebarClose = document.getElementById('mobileSidebarClose');
            const mobileSidebarApply = document.getElementById('mobileSidebarApply');

            console.log('initMobileSidebar called');
            console.log('mobileDisplayToggle:', mobileDisplayToggle);
            console.log('mobileSidebarOverlay:', mobileSidebarOverlay);
            console.log('mobileSidebar:', mobileSidebar);
            console.log('mobileSidebarClose:', mobileSidebarClose);

            if (!mobileDisplayToggle || !mobileSidebarOverlay) {
                console.log('Elements not found, returning');
                return; // العناصر غير موجودة
            }

            // فتح النافذة الجانبية
            mobileDisplayToggle.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('Mobile display toggle clicked - opening sidebar');
                console.log('mobileSidebarOverlay found:', !!mobileSidebarOverlay);
                console.log('mobileSidebar found:', !!mobileSidebar);

                // البحث مرة أخرى عن العناصر
                const overlay = document.getElementById('mobileSidebarOverlay');
                const sidebar = document.getElementById('mobileSidebar');
                console.log('Re-searched overlay:', !!overlay);
                console.log('Re-searched sidebar:', !!sidebar);

                // فتح النافذة الجانبية بنفس طريقة نافذة التسجيل
                if (overlay && sidebar) {
                    console.log('Opening sidebar...');
                    overlay.style.display = 'block';
                    setTimeout(function() {
                        sidebar.classList.add('active');
                        console.log('Added active class to sidebar');

                        // فحص الـ styles المطبقة
                        const overlayStyles = window.getComputedStyle(overlay);
                        const sidebarStyles = window.getComputedStyle(sidebar);
                        console.log('Overlay display:', overlayStyles.display);
                        console.log('Overlay z-index:', overlayStyles.zIndex);
                        console.log('Sidebar transform:', sidebarStyles.transform);
                        console.log('Sidebar left:', sidebarStyles.left);
                        console.log('Sidebar width:', sidebarStyles.width);
                    }, 100);
                    document.body.style.overflow = 'hidden';
                } else {
                    console.error('Sidebar elements not found!');
                    console.log('All elements with mobile-sidebar class:', document.querySelectorAll('.mobile-sidebar'));
                    console.log('All elements with mobile-sidebar-overlay class:', document.querySelectorAll('.mobile-sidebar-overlay'));
                }
            });

            // إغلاق النافذة الجانبية بنفس طريقة نافذة التسجيل
            function closeMobileSidebar() {
                if (mobileSidebar) {
                    mobileSidebar.classList.remove('active');
                    setTimeout(function() {
                        if (mobileSidebarOverlay) {
                            mobileSidebarOverlay.style.display = 'none';
                        }
                    }, 300);
                    document.body.style.overflow = '';
                }
            }

            // إغلاق عند النقر على زر الإغلاق
            if (mobileSidebarClose) {
                mobileSidebarClose.addEventListener('click', closeMobileSidebar);
            }

            // إغلاق عند النقر على الخلفية
            mobileSidebarOverlay.addEventListener('click', function(e) {
                if (e.target === mobileSidebarOverlay) {
                    closeMobileSidebar();
                }
            });

            // تطبيق التغييرات وإغلاق النافذة
            if (mobileSidebarApply) {
                mobileSidebarApply.addEventListener('click', function() {
                    showToast('تم تطبيق التغييرات بنجاح');
                    closeMobileSidebar();
                });
            }

            // تهيئة عناصر التحكم داخل النافذة الجانبية
            initMobileSidebarControls();

            // تفعيل وظائف النافذة الجانبية

            // إغلاق النافذة عند النقر على الخلفية
            mobileSidebarOverlay.addEventListener('click', function(e) {
                if (e.target === mobileSidebarOverlay) {
                    mobileSidebarOverlay.classList.remove('active');
                }
            });

            // إغلاق النافذة عند النقر على زر الإغلاق
            if (mobileSidebarClose) {
                mobileSidebarClose.addEventListener('click', function() {
                    mobileSidebarOverlay.classList.remove('active');
                });
            }

            // تطبيق التغييرات
            if (mobileSidebarApply) {
                mobileSidebarApply.addEventListener('click', function() {
                    // تطبيق إعدادات العرض المحددة
                    const activeGridOption = document.querySelector('.mobile-grid-option.active');
                    if (activeGridOption) {
                        const viewType = activeGridOption.getAttribute('data-mobile-view');
                        applyMobileGridView(viewType);
                    }

                    // إغلاق النافذة
                    mobileSidebarOverlay.classList.remove('active');
                });
            }

            // تفعيل خيارات العرض
            const gridOptions = document.querySelectorAll('.mobile-grid-option');
            gridOptions.forEach(option => {
                option.addEventListener('click', function() {
                    // إزالة الفئة النشطة من جميع الخيارات
                    gridOptions.forEach(opt => opt.classList.remove('active'));
                    // إضافة الفئة النشطة للخيار المحدد
                    this.classList.add('active');
                });
            });

            // تفعيل شريط تمرير السعر
            const priceSlider = document.getElementById('mobilePriceSlider');
            const priceDisplay = document.getElementById('mobilePriceDisplay');
            if (priceSlider && priceDisplay) {
                priceSlider.addEventListener('input', function() {
                    priceDisplay.textContent = `حتى ${this.value} ريال`;
                });
            }

            // تطبيق فلتر السعر
            const applyPriceFilter = document.getElementById('mobileApplyPriceFilter');
            if (applyPriceFilter) {
                applyPriceFilter.addEventListener('click', function() {
                    const maxPrice = priceSlider.value;
                    // هنا يمكن إضافة منطق تطبيق فلتر السعر
                    console.log('تطبيق فلتر السعر:', maxPrice);
                });
            }
        }

        // دالة تطبيق عرض الشبكة للهواتف
        function applyMobileGridView(viewType) {
            const productGrid = document.querySelector('.products-grid');
            if (!productGrid) return;

            // إزالة جميع فئات العرض السابقة
            productGrid.classList.remove('mobile-grid-1', 'mobile-grid-2', 'mobile-grid-3');

            // إضافة فئة العرض الجديدة
            if (viewType) {
                productGrid.classList.add(viewType);
            }

            console.log('تم تطبيق عرض الشبكة:', viewType);
        }

        // دالة تهيئة عناصر التحكم داخل النافذة الجانبية
        function initMobileSidebarControls() {
            // تهيئة شريط تمرير السعر
            const mobilePriceSlider = document.getElementById('mobilePriceSlider');
            const mobileMinPrice = document.getElementById('mobileMinPrice');
            const mobileMaxPrice = document.getElementById('mobileMaxPrice');
            const mobileApplyPriceFilter = document.getElementById('mobileApplyPriceFilter');

            if (mobilePriceSlider && mobileMaxPrice) {
                mobilePriceSlider.addEventListener('input', function() {
                    mobileMaxPrice.value = this.value + ' ر.ع';
                });

                if (mobileApplyPriceFilter) {
                    mobileApplyPriceFilter.addEventListener('click', function() {
                        filterProductsByPrice();
                        showToast('تم تطبيق تصفية السعر');
                    });
                }
            }

            // تهيئة قائمة الترتيب
            const mobileSortSelect = document.getElementById('mobileSortSelect');
            if (mobileSortSelect) {
                mobileSortSelect.addEventListener('change', function() {
                    const sortValue = this.value;
                    sortProducts(sortValue);

                    // مزامنة مع قائمة الترتيب الرئيسية
                    const mainSortSelect = document.getElementById('productSort');
                    if (mainSortSelect) {
                        mainSortSelect.value = sortValue;
                    }
                });
            }

            // تهيئة قائمة عدد المنتجات
            const mobileItemsSelect = document.getElementById('mobileItemsSelect');
            if (mobileItemsSelect) {
                mobileItemsSelect.addEventListener('change', function() {
                    const itemsValue = this.value;
                    changePageSize(itemsValue);

                    // مزامنة مع قائمة عدد المنتجات الرئيسية
                    const mainItemsSelect = document.getElementById('itemsPerPage');
                    if (mainItemsSelect) {
                        mainItemsSelect.value = itemsValue;
                    }
                });
            }

            // مزامنة القيم الحالية
            syncMobileSidebarValues();
        }

        // دالة مزامنة القيم بين النافذة الجانبية والعناصر الرئيسية
        function syncMobileSidebarValues() {
            // مزامنة قائمة الترتيب
            const mainSortSelect = document.getElementById('productSort');
            const mobileSortSelect = document.getElementById('mobileSortSelect');
            if (mainSortSelect && mobileSortSelect) {
                mobileSortSelect.value = mainSortSelect.value;
            }

            // مزامنة قائمة عدد المنتجات
            const mainItemsSelect = document.getElementById('itemsPerPage');
            const mobileItemsSelect = document.getElementById('mobileItemsSelect');
            if (mainItemsSelect && mobileItemsSelect) {
                mobileItemsSelect.value = mainItemsSelect.value;
            }

            // مزامنة شريط تمرير السعر
            const mainPriceSlider = document.getElementById('priceRangeSlider');
            const mobilePriceSlider = document.getElementById('mobilePriceSlider');
            const mobileMaxPrice = document.getElementById('mobileMaxPrice');
            if (mainPriceSlider && mobilePriceSlider && mobileMaxPrice) {
                mobilePriceSlider.value = mainPriceSlider.value;
                mobileMaxPrice.value = mainPriceSlider.value + ' ر.ع';
            }
        }

        function initPriceSlider() {
            const slider = document.getElementById('priceRangeSlider');
            const minPrice = document.getElementById('minPrice');
            const maxPrice = document.getElementById('maxPrice');
            const applyButton = document.getElementById('applyPriceFilter');

            slider.addEventListener('input', function() {
                maxPrice.value = this.value + ' ر.ع';
            });

            applyButton.addEventListener('click', function() {
                filterProductsByPrice();
            });
        }

        function filterProductsByPrice() {
            const maxPriceValue = parseInt(document.getElementById('priceRangeSlider').value);
            const productItems = document.querySelectorAll('.product-item');
            let visibleCount = 0;
            let totalCount = productItems.length;

            productItems.forEach(item => {
                const priceElement = item.querySelector('.product-item-price-current');
                if (priceElement) {
                    // استخراج قيمة السعر من النص
                    const priceText = priceElement.textContent;
                    const priceValue = parseFloat(priceText.replace(/[^\d.]/g, ''));

                    if (priceValue <= maxPriceValue) {
                        item.style.display = '';
                        visibleCount++;
                    } else {
                        item.style.display = 'none';
                    }
                }
            });

            // تحديث عداد المنتجات المعروضة
            updateProductCount(visibleCount, totalCount);

            // عرض رسالة للمستخدم
            showToast(`تم تطبيق التصفية: ${visibleCount} من أصل ${totalCount} منتج`);
        }

        function updateProductCount(visibleCount, totalCount) {
            const countElement = document.querySelector('.products-count span:first-child');
            if (countElement) {
                countElement.textContent = visibleCount;
            }
        }

        function initSortControls() {
            // Esta función ahora solo inicializa los controles
            // El evento de cambio se maneja en el DOMContentLoaded
        }

        // وظيفة فرز المنتجات
        function sortProducts(sortValue) {
            const productGrid = document.querySelector('.product-grid');
            if (!productGrid) {
                console.error('Product grid not found');
                return;
            }

            const products = Array.from(document.querySelectorAll('.product-item'));
            console.log('Found', products.length, 'products to sort');

            if (products.length === 0) {
                console.error('No product items found');
                return;
            }

            products.sort((a, b) => {
                if (sortValue === 'price-asc') {
                    const priceA = getPriceFromItem(a);
                    const priceB = getPriceFromItem(b);
                    console.log('Comparing prices:', priceA, priceB);
                    return priceA - priceB;
                } else if (sortValue === 'price-desc') {
                    const priceA = getPriceFromItem(a);
                    const priceB = getPriceFromItem(b);
                    console.log('Comparing prices (desc):', priceA, priceB);
                    return priceB - priceA;
                } else {
                    // Por defecto, mantener el orden original
                    return 0;
                }
            });

            // Reordenar los productos en el DOM
            products.forEach(product => {
                productGrid.appendChild(product);
            });

            // إظهار رسالة توست للمستخدم
            showToast('تم ترتيب المنتجات');
        }

        function getPriceFromItem(item) {
            const priceElement = item.querySelector('.product-item-price-current');
            if (priceElement) {
                const priceText = priceElement.textContent;
                // استخراج الأرقام فقط من النص
                const priceValue = parseInt(priceText.replace(/[^\d]/g, ''));
                console.log('Extracted price:', priceValue, 'from', priceText);
                return priceValue;
            }
            console.warn('Price element not found in item');
            return 0;
        }

        // تهيئة أزرار المفضلة
        function initWishlistButtons() {
            const wishlistButtons = document.querySelectorAll('.add-to-wishlist-btn');

            // تحديث حالة الأزرار بناءً على المفضلة الحالية
            updateWishlistButtonStates();

            wishlistButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const productId = this.getAttribute('data-product-id');

                    // إضافة تأثير حركي للزر
                    this.classList.add('adding');

                    // إرسال طلب AJAX لإضافة/إزالة المنتج من المفضلة
                    $.ajax({
                        url: '/Wishlist/AddToWishlist',
                        type: 'POST',
                        data: {
                            productId: productId,
                            __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                        },
                        success: (response) => {
                            if (response.success) {
                                // تحديث حالة الزر
                                if (response.isInWishlist) {
                                    this.classList.add('active');
                                    this.querySelector('.bi-heart').style.display = 'none';
                                    this.querySelector('.bi-heart-fill').style.display = 'inline-block';

                                    // إضافة تأثير نبضة للقلب
                                    this.classList.add('pulse');
                                    setTimeout(() => {
                                        this.classList.remove('pulse');
                                    }, 600);

                                    showToast('❤️ تمت إضافة المنتج إلى المفضلة');
                                } else {
                                    this.classList.remove('active');
                                    this.querySelector('.bi-heart-fill').style.display = 'none';
                                    this.querySelector('.bi-heart').style.display = 'inline-block';
                                    showToast('💔 تمت إزالة المنتج من المفضلة');
                                }

                                // تحديث عداد المفضلة مباشرة
                                const wishlistBadge = document.querySelector('.wishlist-count');
                                if (wishlistBadge) {
                                    wishlistBadge.textContent = response.count;

                                    // إضافة تأثير حركي للعداد
                                    wishlistBadge.classList.add('pulse');
                                    wishlistBadge.style.backgroundColor = '#6a0dad';

                                    // إزالة التأثير بعد فترة
                                    setTimeout(() => {
                                        wishlistBadge.classList.remove('pulse');
                                        wishlistBadge.style.backgroundColor = '';
                                    }, 1500);
                                }

                                // لا نطلق حدث تحديث المفضلة لتجنب التحديثات المتكررة
                                // العداد تم تحديثه مباشرة أعلاه
                            } else {
                                showToast('حدث خطأ أثناء تحديث المفضلة');
                            }

                            // إزالة تأثير الإضافة
                            this.classList.remove('adding');
                        },
                        error: () => {
                            showToast('حدث خطأ أثناء تحديث المفضلة');
                            this.classList.remove('adding');
                        }
                    });
                });
            });

            // لا نحدث حالة الأزرار عند التحميل لتجنب طلبات غير ضرورية
            // الأزرار ستحدث حالتها عند الحاجة فقط
        }

        // تحديث حالة أزرار المفضلة
        function updateWishlistButtonsState() {
            $.ajax({
                url: '/Wishlist/GetWishlistItems',
                type: 'GET',
                success: function(data) {
                    if (data && data.items) {
                        $('.add-to-wishlist-btn').each(function() {
                            const productId = parseInt($(this).data('product-id'));
                            const button = $(this);

                            if (data.items.includes(productId)) {
                                button.addClass('active');
                                button.find('i').removeClass('bi-heart').addClass('bi-heart-fill');
                            } else {
                                button.removeClass('active');
                                button.find('i').removeClass('bi-heart-fill').addClass('bi-heart');
                            }
                        });
                    }
                }
            });
        }

        // ملاحظة: تم إزالة الدوال المحلية لتحديث عداد المفضلة
        // لتجنب التداخل مع الدوال العامة في product-actions.js
        // الآن يتم استخدام الدوال العامة مباشرة

        // Inicializar botones de comparación
        function initCompareButtons() {
            const compareButtons = document.querySelectorAll('.add-to-compare-btn');

            // Cargar productos en comparación
            let compareList = JSON.parse(localStorage.getItem('compareList')) || [];

            // Marcar botones de productos que ya están en comparación
            compareButtons.forEach(button => {
                const productId = button.getAttribute('data-product-id');
                const productCard = button.closest('.product-card');

                if (compareList.includes(productId)) {
                    button.classList.add('active');
                    if (productCard) {
                        productCard.classList.add('in-compare');
                    }
                }
            });

            // Añadir evento de clic a los botones
            compareButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const productId = this.getAttribute('data-product-id');
                    const productCard = this.closest('.product-card');

                    // Alternar estado activo
                    this.classList.toggle('active');
                    if (productCard) {
                        productCard.classList.toggle('in-compare');
                    }

                    // Actualizar lista de comparación
                    if (this.classList.contains('active')) {
                        // Limitar a 4 productos en comparación
                        if (compareList.length >= 4) {
                            // Eliminar el primer producto de la lista
                            const removedId = compareList.shift();
                            // Actualizar UI para el producto eliminado
                            const removedButton = document.querySelector(`.add-to-compare-btn[data-product-id="${removedId}"]`);
                            if (removedButton) {
                                removedButton.classList.remove('active');
                                const removedCard = removedButton.closest('.product-card');
                                if (removedCard) {
                                    removedCard.classList.remove('in-compare');
                                }
                            }
                        }

                        // Añadir a comparación si no existe
                        if (!compareList.includes(productId)) {
                            compareList.push(productId);
                            showToast('تمت إضافة المنتج إلى المقارنة');
                        }
                    } else {
                        // Eliminar de comparación
                        compareList = compareList.filter(id => id !== productId);
                        showToast('تمت إزالة المنتج من المقارنة');
                    }

                    // Guardar en localStorage
                    localStorage.setItem('compareList', JSON.stringify(compareList));

                    // تحديث عدد المنتجات في المقارنة
                    updateCompareCount();

                    // إطلاق حدث تحديث المقارنة
                    $(document).trigger('compare:updated');
                });
            });
        }

        // تحديث عدد المنتجات في المقارنة
        function updateCompareCount() {
            const compareList = JSON.parse(localStorage.getItem('compareList')) || [];
            const compareCountBadges = document.querySelectorAll('.compare-count');
            const compareButtonCount = document.querySelector('.compare-button-count');

            // تحديث جميع شارات العد في الصفحة
            compareCountBadges.forEach(badge => {
                badge.textContent = compareList.length;

                if (compareList.length > 0) {
                    badge.classList.remove('d-none');
                } else {
                    badge.classList.add('d-none');
                }
            });

            // تحديث عداد زر عرض المقارنة
            if (compareButtonCount) {
                compareButtonCount.textContent = compareList.length;

                if (compareList.length > 0) {
                    compareButtonCount.classList.remove('d-none');
                } else {
                    compareButtonCount.classList.add('d-none');
                }
            }
        }

        // ملاحظة: تم نقل دوال السلة إلى ملف product-actions.js لتجنب التكرار

        // Crear página de comparación
        function createComparePage() {
            const compareList = JSON.parse(localStorage.getItem('compareList')) || [];

            if (compareList.length === 0) {
                showToast('لا توجد منتجات للمقارنة');
                return;
            }

            // Crear URL con parámetros de consulta
            const url = '/Products/Compare?ids=' + compareList.join(',');
            window.location.href = url;
        }

        // تغيير حجم الصفحة
        function changePageSize(pageSize) {
            // حفظ التفضيل
            localStorage.setItem('itemsPerPage', pageSize);

            // إعادة تحميل الصفحة مع حجم الصفحة الجديد
            const currentUrl = new URL(window.location.href);
            currentUrl.searchParams.set('pageSize', pageSize);
            currentUrl.searchParams.set('pageNumber', '1'); // العودة إلى الصفحة الأولى عند تغيير حجم الصفحة
            window.location.href = currentUrl.toString();
        }

        // إضافة مستمعي الأحداث للأزرار
        document.addEventListener('DOMContentLoaded', function() {
            // زر المقارنة
            const compareButton = document.getElementById('compareButton');
            if (compareButton) {
                compareButton.addEventListener('click', createComparePage);
            }

            // قائمة عدد المنتجات
            const itemsPerPageSelect = document.getElementById('itemsPerPage');
            if (itemsPerPageSelect) {
                itemsPerPageSelect.addEventListener('change', function() {
                    changePageSize(this.value);
                });
            }

            // قائمة الترتيب
            const sortSelect = document.getElementById('productSort');
            if (sortSelect) {
                sortSelect.addEventListener('change', function() {
                    const sortValue = this.value;
                    sortProducts(sortValue);
                });
            }
        });

        // Inicializar selector de número de productos por página
        function initItemsPerPage() {
            const itemsPerPageSelect = document.getElementById('itemsPerPage');

            // Cargar preferencia guardada (لكن لا نطبقها هنا لأننا نستخدم التقسيم من الخادم)
            const savedItemsPerPage = localStorage.getItem('itemsPerPage') || '@pageSize';

            // تعيين القيمة المحددة في القائمة المنسدلة
            if (itemsPerPageSelect) {
                itemsPerPageSelect.value = savedItemsPerPage;
            }
        }

        // تهيئة أزرار إضافة المنتج إلى الشريط المتحرك (للمدير فقط)
        function initCarouselButtons() {
            const carouselButtons = document.querySelectorAll('.toggle-carousel-btn');

            carouselButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const productId = parseInt(this.getAttribute('data-product-id'));
                    const button = this;

                    // إرسال طلب AJAX لتبديل حالة عرض المنتج في الشريط المتحرك
                    $.ajax({
                        url: '/Products/ToggleCarousel/' + productId,
                        type: 'POST',
                        data: {
                            __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                        },
                        success: function(response) {
                            if (response.success) {
                                // تحديث حالة الزر
                                if (response.showInCarousel) {
                                    button.classList.add('active');
                                    button.setAttribute('title', 'إزالة من الشريط المتحرك');
                                } else {
                                    button.classList.remove('active');
                                    button.setAttribute('title', 'إضافة إلى الشريط المتحرك');
                                }

                                // عرض رسالة نجاح
                                showToast(response.message);
                            } else {
                                // عرض رسالة خطأ
                                showToast('حدث خطأ أثناء تحديث المنتج');
                            }
                        },
                        error: function() {
                            showToast('حدث خطأ أثناء تحديث المنتج');
                        }
                    });
                });
            });
        }

        // تهيئة أزرار تبديل حالة توفر المنتج (للمدير فقط)
        function initAvailabilityButtons() {
            const availabilityButtons = document.querySelectorAll('.toggle-availability-btn');

            availabilityButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const productId = this.getAttribute('data-product-id');
                    const button = this;
                    const productCard = button.closest('.product-card');

                    // إرسال طلب AJAX لتبديل حالة توفر المنتج
                    $.ajax({
                        url: '/Products/ToggleAvailability/' + productId,
                        type: 'POST',
                        data: {
                            __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                        },
                        success: function(response) {
                            if (response.success) {
                                // تحديث حالة الزر
                                if (response.isAvailable) {
                                    button.classList.remove('not-available');
                                    button.classList.add('available');
                                    button.setAttribute('title', 'تعيين كغير متوفر');
                                    button.querySelector('i').classList.remove('bi-bag-x');
                                    button.querySelector('i').classList.add('bi-bag-check');

                                    // تحديث شارة التوفر
                                    let availabilityBadge = productCard.querySelector('.availability-badge');
                                    if (availabilityBadge) {
                                        availabilityBadge.classList.remove('not-available');
                                        availabilityBadge.classList.add('available');
                                        availabilityBadge.textContent = 'متوفر';
                                    }
                                } else {
                                    button.classList.remove('available');
                                    button.classList.add('not-available');
                                    button.setAttribute('title', 'تعيين كمتوفر');
                                    button.querySelector('i').classList.remove('bi-bag-check');
                                    button.querySelector('i').classList.add('bi-bag-x');

                                    // تحديث شارة التوفر
                                    let availabilityBadge = productCard.querySelector('.availability-badge');
                                    if (availabilityBadge) {
                                        availabilityBadge.classList.remove('available');
                                        availabilityBadge.classList.add('not-available');
                                        availabilityBadge.textContent = 'غير متوفر';
                                    }
                                }

                                // عرض رسالة نجاح
                                showToast(response.message);
                            } else {
                                // عرض رسالة خطأ
                                showToast('حدث خطأ أثناء تحديث المنتج');
                            }
                        },
                        error: function() {
                            showToast('حدث خطأ أثناء تحديث المنتج');
                        }
                    });
                });
            });
        }

        function showToast(message) {
            // Crear elemento toast si no existe
            let toast = document.getElementById('toast-notification');
            if (!toast) {
                toast = document.createElement('div');
                toast.id = 'toast-notification';
                toast.style.position = 'fixed';
                toast.style.bottom = '20px';
                toast.style.right = '20px';
                toast.style.backgroundColor = 'rgba(106, 13, 173, 0.9)';
                toast.style.color = 'white';
                toast.style.padding = '12px 20px';
                toast.style.borderRadius = '4px';
                toast.style.zIndex = '1000';
                toast.style.transition = 'opacity 0.5s ease-in-out';
                toast.style.opacity = '0';
                toast.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
                document.body.appendChild(toast);
            }

            // Mostrar mensaje
            toast.textContent = message;
            toast.style.opacity = '1';

            // Ocultar después de 3 segundos
            setTimeout(() => {
                toast.style.opacity = '0';
            }, 3000);
        }

        // دالة تحديث حالة أزرار المفضلة عند تحميل الصفحة
        function updateWishlistButtonStates() {
            console.log('🔄 تحديث حالة أزرار المفضلة...');

            // جلب قائمة المفضلة من الخادم
            fetch('/Wishlist/GetWishlistItems')
            .then(response => response.json())
            .then(data => {
                console.log('📦 قائمة المفضلة المستلمة:', data);

                if (data.success && data.items) {
                    const wishlistProductIds = data.items.map(item => item.id.toString());
                    console.log('🔍 معرفات المنتجات المفضلة:', wishlistProductIds);

                    // تحديث حالة كل زر
                    const wishlistButtons = document.querySelectorAll('.add-to-wishlist-btn');
                    wishlistButtons.forEach(button => {
                        const productId = button.getAttribute('data-product-id');

                        if (wishlistProductIds.includes(productId)) {
                            // المنتج في المفضلة - تفعيل الزر وتغيير الأيقونة
                            button.classList.add('active');
                            const icon = button.querySelector('i');
                            if (icon && icon.classList.contains('bi-heart')) {
                                icon.classList.remove('bi-heart');
                                icon.classList.add('bi-heart-fill');
                            }
                            console.log(`❤️ المنتج ${productId} في المفضلة`);
                        } else {
                            // المنتج ليس في المفضلة - إلغاء تفعيل الزر وإعادة الأيقونة
                            button.classList.remove('active');
                            const icon = button.querySelector('i');
                            if (icon && icon.classList.contains('bi-heart-fill')) {
                                icon.classList.remove('bi-heart-fill');
                                icon.classList.add('bi-heart');
                            }
                            console.log(`🤍 المنتج ${productId} ليس في المفضلة`);
                        }
                    });

                    console.log('✅ تم تحديث حالة أزرار المفضلة بنجاح');
                }
            })
            .catch(error => {
                console.error('❌ خطأ في جلب قائمة المفضلة:', error);
            });
        }

        // JavaScript للمنتجات الأكثر مبيعاً للهواتف
        function loadMobileBestSellers() {
            const container = document.getElementById('mobileBestSellersContainer');
            const countElement = document.getElementById('mobileBestSellersCount');

            if (!container) return;

            container.innerHTML = '<div style="text-align: center; padding: 30px 20px; color: #6a0dad; font-size: 0.9rem;">جاري التحميل...</div>';

            fetch('/Products/GetBestSellers')
                .then(response => response.json())
                .then(data => {
                    console.log('استجابة المنتجات الأكثر مبيعاً للهواتف:', data);

                    if (data.success && data.products && data.products.length > 0) {
                        console.log('تم العثور على منتجات للهواتف:', data.products);
                        displayMobileBestSellers(data.products);
                        if (countElement) {
                            countElement.textContent = `(${data.products.length})`;
                        }
                    } else {
                        console.log('لا توجد منتجات أو فشل في التحميل للهواتف');
                        container.innerHTML = '<div style="text-align: center; padding: 30px 20px; color: #666; font-size: 0.9rem;"><i class="bi bi-star" style="font-size: 2rem; margin-bottom: 10px; display: block; color: #dee2e6;"></i>لا توجد منتجات متوفرة حالياً<br><small style="color: #adb5bd; margin-top: 5px; display: block;">سيتم عرض المنتجات الأكثر مبيعاً هنا</small></div>';
                        if (countElement) {
                            countElement.textContent = '(0)';
                        }
                    }
                })
                .catch(error => {
                    console.error('خطأ في تحميل المنتجات للهواتف:', error);
                    container.innerHTML = '<div style="text-align: center; padding: 30px 20px; color: #dc3545; font-size: 0.9rem;">حدث خطأ أثناء تحميل المنتجات</div>';
                    if (countElement) {
                        countElement.textContent = '(0)';
                    }
                });
        }

        function displayMobileBestSellers(products) {
            const container = document.getElementById('mobileBestSellersContainer');
            if (!container) return;

            let html = '';
            products.forEach((product, index) => {
                const productId = product.Id || product.id || 0;
                const productName = product.Name || product.name || 'منتج غير محدد';
                const productPrice = product.Price || product.price || 0;
                const productImage = product.ImageUrl || product.imageUrl || '/images/placeholder.jpg';

                const rankIcon = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : `#${index + 1}`;

                html += `
                    <div style="flex: 0 0 280px; background: #fafafa; border: 1px solid #f0f0f0; border-radius: 12px; padding: 15px; transition: all 0.3s ease; cursor: pointer; position: relative;" onclick="window.location.href='/Products/Details/${productId}'">
                        <div style="display: flex; align-items: center; gap: 15px;">
                            <div style="flex-shrink: 0; display: flex; align-items: center; justify-content: center;">
                                <span style="font-size: 1.1rem; font-weight: bold; color: #6a0dad; min-width: 25px; text-align: center;">${rankIcon}</span>
                            </div>
                            <div style="position: relative; flex-shrink: 0;">
                                <img src="${productImage}" alt="${productName}" style="width: 70px; height: 70px; object-fit: cover; border-radius: 10px; border: 2px solid #f0f0f0;" onerror="this.src='/images/placeholder.jpg'">
                                <div style="position: absolute; top: -5px; right: -5px; background: linear-gradient(45deg, #ffd700, #ffed4e); color: #333; border-radius: 50%; width: 18px; height: 18px; display: flex; align-items: center; justify-content: center; font-size: 9px; box-shadow: 0 2px 4px rgba(0,0,0,0.2); border: 2px solid white;">
                                    <i class="bi bi-star-fill"></i>
                                </div>
                            </div>
                            <div style="flex: 1; min-width: 0;">
                                <div style="font-size: 1rem; font-weight: 600; margin: 0 0 8px 0; color: #333; line-height: 1.3; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden;">${productName}</div>
                                <div style="font-size: 1rem; font-weight: 600; color: #6a0dad; margin-bottom: 10px;">${productPrice} ر.ع</div>
                                <div style="display: flex; gap: 8px;">
                                    <button style="background: white; border: 2px solid #f0f0f0; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; cursor: pointer; transition: all 0.3s ease; color: #999;" onclick="event.stopPropagation(); addToWishlistMobile(${productId})" title="إضافة للمفضلة">
                                        <i class="bi bi-heart" style="font-size: 12px;"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        function addToWishlistMobile(productId) {
            const token = document.querySelector('input[name="__RequestVerificationToken"]')?.value;

            if (!token) {
                alert('حدث خطأ أثناء إضافة المنتج للمفضلة');
                return;
            }

            fetch('/Wishlist/AddToWishlist', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'RequestVerificationToken': token,
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `productId=${productId}&__RequestVerificationToken=${token}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.isInWishlist ? 'تمت إضافة المنتج للمفضلة ❤️' : 'تمت إزالة المنتج من المفضلة');

                    // تحديث عداد المفضلة
                    const wishlistBadges = document.querySelectorAll('.wishlist-count');
                    wishlistBadges.forEach(badge => {
                        badge.textContent = data.count;
                    });
                } else {
                    alert('حدث خطأ أثناء تحديث المفضلة');
                }
            })
            .catch(error => {
                console.error('خطأ:', error);
                alert('حدث خطأ أثناء تحديث المفضلة');
            });
        }

        // تحميل المنتجات للهواتف عند تحميل الصفحة
        if (document.getElementById('mobileBestSellersContainer')) {
            loadMobileBestSellers();
        }

        // تهيئة أزرار التعديل للمدير
        function initEditProductButtons() {
            console.log('🔧 بدء تهيئة أزرار التعديل');

            // التحقق من وجود المستخدم المدير
            const isAdmin = document.querySelector('.edit-product-btn') !== null;
            console.log('هل المستخدم مدير؟', isAdmin);

            const editButtons = document.querySelectorAll('.edit-product-btn');
            console.log('تم العثور على', editButtons.length, 'زر تعديل');

            if (editButtons.length === 0) {
                console.log('⚠️ لم يتم العثور على أزرار تعديل. تأكد من تسجيل الدخول كمدير.');
                return;
            }

            editButtons.forEach((button, index) => {
                console.log(`تهيئة زر التعديل ${index + 1}:`, button);
                console.log('معرف المنتج:', button.getAttribute('data-product-id'));

                // إزالة أي مستمعين سابقين
                button.removeEventListener('click', editButtonClickHandler);

                // إضافة مستمع جديد
                button.addEventListener('click', editButtonClickHandler);

                // إضافة تأثير بصري للتأكد من أن الزر يعمل
                button.style.cursor = 'pointer';
                button.style.opacity = '1';
            });

            console.log('✅ تم الانتهاء من تهيئة أزرار التعديل');
        }

        // معالج النقر على زر التعديل
        function editButtonClickHandler(e) {
            console.log('🖱️ تم النقر على زر التعديل!');
            console.log('Event:', e);
            console.log('Target:', e.target);
            console.log('Current Target:', e.currentTarget);

            e.preventDefault();
            e.stopPropagation();

            const productId = this.getAttribute('data-product-id');
            console.log('معرف المنتج:', productId);

            if (productId && productId !== 'null' && productId !== '') {
                console.log('🚀 الانتقال إلى صفحة التعديل...');
                const editUrl = `/Products/Edit/${productId}`;
                console.log('رابط التعديل:', editUrl);

                // إضافة تأثير بصري للزر
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'scale(1)';
                }, 150);

                // الانتقال إلى صفحة تعديل المنتج
                window.location.href = editUrl;
            } else {
                console.error('❌ لم يتم العثور على معرف المنتج صالح:', productId);
                alert('خطأ: لم يتم العثور على معرف المنتج');
            }
        }


    </script>
}

<!-- زر التصفية الثابت للهواتف -->
<button class="fixed-filter-button d-block d-lg-none" id="fixedFilterButton">
    <i class="bi bi-funnel"></i>
</button>

<!-- نافذة التصفية الجانبية للهواتف -->
<div class="side-filter-overlay" id="sideFilterOverlay">
    <div class="side-filter-panel" id="sideFilterPanel">
        <div class="side-filter-header">
            <h2>تصفية المنتجات</h2>
            <button class="side-filter-close" id="sideFilterClose">
                <i class="bi bi-x"></i>
            </button>
        </div>

        <div class="side-filter-body">
            <!-- تصفية حسب السعر -->
            <div class="filter-section">
                <h6 class="filter-section-title">
                    <i class="bi bi-currency-dollar me-2"></i>
                    تصفية حسب السعر
                </h6>
                <div class="price-filter-container">
                    <div class="price-range-slider">
                        <input type="range" id="mobilePriceRange" min="0" max="1000" value="1000" class="form-range">
                        <div class="price-range-labels">
                            <span>0 ر.ع</span>
                            <span id="mobilePriceValue">1000 ر.ع</span>
                        </div>
                    </div>
                    <div class="price-input-group">
                        <div class="price-input">
                            <label>من</label>
                            <input type="number" id="mobilePriceMin" class="form-control" placeholder="0" min="0">
                        </div>
                        <div class="price-input">
                            <label>إلى</label>
                            <input type="number" id="mobilePriceMax" class="form-control" placeholder="1000" min="0">
                        </div>
                    </div>
                    <button class="btn btn-primary apply-price-filter" id="mobileApplyPriceFilter">
                        تطبيق التصفية
                    </button>
                </div>
            </div>

            <!-- تصفية حسب التوفر -->
            <div class="filter-section">
                <h6 class="filter-section-title">
                    <i class="bi bi-check-circle me-2"></i>
                    حالة التوفر
                </h6>
                <div class="availability-filters">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="mobileAvailableOnly">
                        <label class="form-check-label" for="mobileAvailableOnly">
                            متوفر فقط
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="mobileOutOfStock">
                        <label class="form-check-label" for="mobileOutOfStock">
                            غير متوفر
                        </label>
                    </div>
                </div>
            </div>

            <!-- أزرار التحكم -->
            <div class="filter-actions">
                <button class="btn btn-outline-secondary" id="mobileClearFilters">
                    <i class="bi bi-arrow-clockwise me-1"></i>
                    إعادة تعيين
                </button>
                <button class="btn btn-primary" id="mobileApplyFilters">
                    <i class="bi bi-check me-1"></i>
                    تطبيق التصفية
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// وظيفة النقر على صورة المنتج
document.addEventListener('DOMContentLoaded', function() {
    // منع تداخل النقر على الصورة مع أزرار الإجراءات
    const actionButtons = document.querySelectorAll('.product-item-action-btn, .product-action-btn');
    actionButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    });

    // إضافة وظيفة النقر على صورة المنتج
    const productImages = document.querySelectorAll('.product-item-img-container');
    productImages.forEach(container => {
        container.addEventListener('click', function(e) {
            // التأكد من عدم النقر على أزرار الإجراءات
            if (!e.target.closest('.product-item-actions') &&
                !e.target.closest('.product-actions-menu') &&
                !e.target.closest('.product-item-action-btn') &&
                !e.target.closest('.product-action-btn')) {

                // الحصول على معرف المنتج
                const productId = container.getAttribute('data-product-id');

                if (productId) {
                    console.log('النقر على المنتج رقم:', productId);

                    // إضافة تأثير بصري
                    container.style.transform = 'scale(0.95)';

                    // الانتقال لصفحة التفاصيل
                    setTimeout(() => {
                        console.log('الانتقال إلى:', '/Products/Details/' + productId);
                        window.location.href = '/Products/Details/' + productId;
                    }, 150);
                } else {
                    console.log('لم يتم العثور على معرف المنتج');
                }
            }
        });
    });

    // إضافة تأثير hover للصور
    productImages.forEach(container => {
        container.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.02)';
        });

        container.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1)';
        });
    });
});
</script>
