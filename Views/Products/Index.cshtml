@model PaginatedList<Product>

@{
    ViewData["Title"] = "المنتجات";
    var pageSize = ViewData["PageSize"]?.ToString() ?? "12";
    var categories = ViewData["Categories"] as List<Category> ?? new List<Category>();
    var selectedCategory = ViewData["SelectedCategory"]?.ToString();
}

<div class="container-fluid">
    <div class="row">
        <!-- المحتوى الرئيسي -->
        <div class="col-lg-9 col-md-8">
            <!-- شريط التحكم -->
            <div class="products-controls">
                <div class="products-controls-left">
                    <div class="products-count">
                        <span>@Model.TotalItems</span> منتج
                    </div>
                </div>

                <div class="products-controls-right">
                    <!-- قائمة الترتيب -->
                    <div class="control-group">
                        <label for="productSort">الترتيب:</label>
                        <select id="productSort" class="form-select">
                            <option value="default">الافتراضي</option>
                            <option value="newest">الأحدث</option>
                            <option value="price-asc">السعر: من الأقل إلى الأعلى</option>
                            <option value="price-desc">السعر: من الأعلى إلى الأقل</option>
                        </select>
                    </div>

                    <!-- عدد المنتجات -->
                    <div class="control-group">
                        <label for="itemsPerPage">عدد المنتجات:</label>
                        <select id="itemsPerPage" class="form-select">
                            <option value="6">6</option>
                            <option value="12" selected>12</option>
                            <option value="16">16</option>
                            <option value="24">24</option>
                            <option value="36">36</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- شبكة المنتجات -->
            <div class="product-grid">
                @foreach (var item in Model)
                {
                    <div class="product-item">
                        <div class="product-card">
                            <!-- صورة المنتج -->
                            <div class="product-item-img-container" data-product-id="@item.Id">
                                <img src="@item.ImageUrl" alt="@item.Name" class="product-item-img" onerror="this.src='/images/placeholder.jpg'">
                                
                                <!-- أزرار الإجراءات -->
                                <div class="product-item-actions">
                                    <!-- زر المفضلة -->
                                    <button class="product-item-action-btn add-to-wishlist-btn" data-product-id="@item.Id" title="إضافة للمفضلة">
                                        <i class="bi bi-heart"></i>
                                        <i class="bi bi-heart-fill" style="display: none;"></i>
                                    </button>

                                    <!-- زر السلة -->
                                    <button class="product-item-action-btn add-to-cart-btn" data-product-id="@item.Id" title="إضافة للسلة">
                                        <i class="bi bi-cart-plus"></i>
                                    </button>

                                    <!-- زر المقارنة -->
                                    <button class="product-item-action-btn add-to-compare-btn" data-product-id="@item.Id" title="إضافة للمقارنة">
                                        <i class="bi bi-arrow-left-right"></i>
                                    </button>

                                    <!-- زر التعديل (للمدير فقط) -->
                                    @if (User.IsInRole("Admin"))
                                    {
                                        <button class="product-item-action-btn edit-product-btn" 
                                                data-product-id="@item.Id" 
                                                title="تعديل المنتج"
                                                onclick="console.log('تم النقر على زر التعديل للمنتج: @item.Id'); window.location.href='/Products/Edit/@item.Id';">
                                            <i class="bi bi-pencil-square"></i>
                                        </button>
                                    }
                                </div>
                            </div>

                            <!-- معلومات المنتج -->
                            <div class="product-item-info">
                                <h3 class="product-item-title">@item.Name</h3>
                                <div class="product-item-price">
                                    <span class="product-item-price-current">@item.Price.ToString("F3") ر.ع</span>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>

            <!-- التقسيم -->
            @if (Model.TotalPages > 1)
            {
                <nav aria-label="تنقل الصفحات">
                    <ul class="pagination justify-content-center">
                        @if (Model.HasPreviousPage)
                        {
                            <li class="page-item">
                                <a class="page-link" href="?pageNumber=@(Model.PageIndex - 1)&pageSize=@pageSize@(string.IsNullOrEmpty(selectedCategory) ? "" : "&category=" + selectedCategory)">السابق</a>
                            </li>
                        }

                        @for (int i = 1; i <= Model.TotalPages; i++)
                        {
                            <li class="page-item @(i == Model.PageIndex ? "active" : "")">
                                <a class="page-link" href="?pageNumber=@i&pageSize=@pageSize@(string.IsNullOrEmpty(selectedCategory) ? "" : "&category=" + selectedCategory)">@i</a>
                            </li>
                        }

                        @if (Model.HasNextPage)
                        {
                            <li class="page-item">
                                <a class="page-link" href="?pageNumber=@(Model.PageIndex + 1)&pageSize=@pageSize@(string.IsNullOrEmpty(selectedCategory) ? "" : "&category=" + selectedCategory)">التالي</a>
                            </li>
                        }
                    </ul>
                </nav>
            }
        </div>

        <!-- الشريط الجانبي -->
        <div class="col-lg-3 col-md-4">
            <div class="sidebar">
                <!-- فئات المنتجات -->
                <div class="sidebar-section">
                    <h5>الفئات</h5>
                    <ul class="category-list">
                        <li><a href="/Products" class="@(string.IsNullOrEmpty(selectedCategory) ? "active" : "")">جميع المنتجات</a></li>
                        @foreach (var category in categories)
                        {
                            <li><a href="/Products?category=@category.Slug" class="@(selectedCategory == category.Slug ? "active" : "")">@category.Name</a></li>
                        }
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // وظيفة فرز المنتجات
        function sortProducts(sortValue) {
            const currentUrl = new URL(window.location.href);
            
            if (sortValue === 'default') {
                currentUrl.searchParams.delete('sortBy');
            } else {
                currentUrl.searchParams.set('sortBy', sortValue);
            }
            
            currentUrl.searchParams.set('pageNumber', '1');
            window.location.href = currentUrl.toString();
        }

        // تغيير حجم الصفحة
        function changePageSize(pageSize) {
            const currentUrl = new URL(window.location.href);
            currentUrl.searchParams.set('pageSize', pageSize);
            currentUrl.searchParams.set('pageNumber', '1');
            window.location.href = currentUrl.toString();
        }

        // بداية تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function () {
            // إضافة event listeners
            const sortSelect = document.getElementById('productSort');
            if (sortSelect) {
                sortSelect.addEventListener('change', function() {
                    sortProducts(this.value);
                });
            }

            const itemsSelect = document.getElementById('itemsPerPage');
            if (itemsSelect) {
                itemsSelect.addEventListener('change', function() {
                    changePageSize(this.value);
                });
            }
        });
    </script>
}
