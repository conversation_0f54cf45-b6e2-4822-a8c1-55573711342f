@model PaginatedList<Product>

@{
    ViewData["Title"] = "المنتجات";
    var pageSize = ViewData["PageSize"]?.ToString() ?? "12";
    var categories = ViewData["Categories"] as List<Category> ?? new List<Category>();
    var selectedCategory = ViewData["SelectedCategory"]?.ToString();
    var bestSellers = ViewData["BestSellers"] as List<Product> ?? new List<Product>();
}

<!-- زر فتح النافذة الجانبية للشاشات الصغيرة -->
<button class="filter-button" id="filterButton" title="الفلاتر والمنتجات الأكثر مبيعاً">
    <i class="bi bi-funnel"></i>
</button>

<!-- النافذة الجانبية للشاشات الصغيرة -->
<div class="side-filter-overlay" id="sideFilterOverlay"></div>
<div class="side-filter-panel" id="sideFilterPanel">
    <div class="side-filter-header">
        <h5>الفلاتر والمنتجات</h5>
        <button class="btn-close" id="closeSideFilter" aria-label="إغلاق"></button>
    </div>

    <div class="side-filter-content">
        <!-- أدوات التحكم في الشاشات الصغيرة -->
        <div class="controls-section">
            <div class="controls-row">
                <!-- خيارات الترتيب -->
                <div class="sort-options">
                    <label class="sort-label">الترتيب:</label>
                    <select class="sort-select" id="mobileSortSelect">
                        <option value="default">الافتراضي</option>
                        <option value="newest">الأحدث</option>
                        <option value="price-asc">السعر: من الأقل إلى الأعلى</option>
                        <option value="price-desc">السعر: من الأعلى إلى الأقل</option>
                    </select>
                </div>

                <!-- عدد المنتجات -->
                <div class="items-per-page">
                    <label class="items-per-page-label">العدد:</label>
                    <select class="items-per-page-select" id="mobileItemsSelect">
                        <option value="6">6</option>
                        <option value="12" selected>12</option>
                        <option value="16">16</option>
                        <option value="24">24</option>
                        <option value="36">36</option>
                    </select>
                </div>

                <!-- زر المقارنة -->
                <div class="compare-button-container">
                    <button class="btn btn-outline-primary btn-sm" id="mobileCompareBtn">
                        <i class="bi bi-arrow-left-right"></i>
                        مقارنة
                        <span class="badge bg-primary" id="mobileCompareCount">0</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- فئات المنتجات -->
        <div class="filter-section">
            <h6>الفئات</h6>
            <ul class="category-list">
                <li><a href="/Products" class="@(string.IsNullOrEmpty(selectedCategory) ? "active" : "")">جميع المنتجات</a></li>
                @foreach (var category in categories)
                {
                    <li><a href="/Products?category=@category.Slug" class="@(selectedCategory == category.Slug ? "active" : "")">@category.Name</a></li>
                }
            </ul>
        </div>

        <!-- المنتجات الأكثر مبيعاً -->
        <div class="best-sellers-section">
            <h6>الأكثر مبيعاً</h6>
            <div class="best-sellers-container" id="mobileBestSellersContainer">
                <!-- سيتم تحميل المنتجات هنا -->
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <div class="row">
        <!-- المحتوى الرئيسي -->
        <div class="col-xl-9 col-lg-8">
            <!-- أدوات التحكم للشاشات الكبيرة -->
            <div class="products-controls d-none d-lg-flex">
                <div class="products-controls-left">
                    <div class="products-count">
                        <span>@Model.TotalItems</span> منتج
                    </div>
                </div>

                <div class="products-controls-right">
                    <!-- قائمة الترتيب -->
                    <div class="control-group">
                        <label for="productSort">الترتيب:</label>
                        <select id="productSort" class="form-select">
                            <option value="default">الافتراضي</option>
                            <option value="newest">الأحدث</option>
                            <option value="price-asc">السعر: من الأقل إلى الأعلى</option>
                            <option value="price-desc">السعر: من الأعلى إلى الأقل</option>
                        </select>
                    </div>

                    <!-- عدد المنتجات -->
                    <div class="control-group">
                        <label for="itemsPerPage">عدد المنتجات:</label>
                        <select id="itemsPerPage" class="form-select">
                            <option value="6">6</option>
                            <option value="12" selected>12</option>
                            <option value="16">16</option>
                            <option value="24">24</option>
                            <option value="36">36</option>
                        </select>
                    </div>

                    <!-- زر المقارنة -->
                    <div class="control-group">
                        <button class="btn btn-outline-primary" id="compareBtn">
                            <i class="bi bi-arrow-left-right"></i>
                            مقارنة
                            <span class="badge bg-primary" id="compareCount">0</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- أدوات التحكم للموبايل -->
            <div class="mobile-controls-top d-lg-none">
                <div class="mobile-grid-options">
                    <button class="mobile-grid-option active" data-columns="1" title="منتج واحد">
                        <i class="bi bi-square"></i>
                    </button>
                    <button class="mobile-grid-option" data-columns="2" title="منتجان">
                        <i class="bi bi-grid"></i>
                    </button>
                    <button class="mobile-grid-option" data-columns="3" title="ثلاثة منتجات">
                        <i class="bi bi-grid-3x3"></i>
                    </button>
                </div>

                <div class="mobile-products-count">
                    <span>@Model.TotalItems</span> منتج
                </div>
            </div>

            <!-- شبكة المنتجات -->
            <div class="product-grid" id="productGrid">
                @foreach (var item in Model)
                {
                    <div class="product-item">
                        <div class="product-card">
                            <!-- صورة المنتج -->
                            <div class="product-item-img-container" data-product-id="@item.Id">
                                <img src="@item.ImageUrl" alt="@item.Name" class="product-item-img" onerror="this.src='/images/placeholder.jpg'">

                                <!-- أزرار الإجراءات -->
                                <div class="product-item-actions">
                                    <!-- زر المفضلة -->
                                    <button class="product-item-action-btn add-to-wishlist-btn" data-product-id="@item.Id" title="إضافة للمفضلة">
                                        <i class="bi bi-heart"></i>
                                        <i class="bi bi-heart-fill" style="display: none;"></i>
                                    </button>

                                    <!-- زر السلة -->
                                    <button class="product-item-action-btn add-to-cart-btn" data-product-id="@item.Id" title="إضافة للسلة">
                                        <i class="bi bi-cart-plus"></i>
                                    </button>

                                    <!-- زر المقارنة -->
                                    <button class="product-item-action-btn add-to-compare-btn" data-product-id="@item.Id" title="إضافة للمقارنة">
                                        <i class="bi bi-arrow-left-right"></i>
                                    </button>

                                    <!-- زر التعديل (للمدير فقط) -->
                                    @if (User.IsInRole("Admin"))
                                    {
                                        <button class="product-item-action-btn edit-product-btn"
                                                data-product-id="@item.Id"
                                                title="تعديل المنتج"
                                                onclick="console.log('تم النقر على زر التعديل للمنتج: @item.Id'); window.location.href='/Products/Edit/@item.Id';">
                                            <i class="bi bi-pencil-square"></i>
                                        </button>
                                    }
                                </div>
                            </div>

                            <!-- معلومات المنتج -->
                            <div class="product-item-info">
                                <h3 class="product-item-title">@item.Name</h3>
                                <div class="product-item-price">
                                    <span class="product-item-price-current">@item.Price.ToString("F3") ر.ع</span>
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>

            <!-- التقسيم -->
            @if (Model.TotalPages > 1)
            {
                <nav aria-label="تنقل الصفحات">
                    <ul class="pagination justify-content-center">
                        @if (Model.HasPreviousPage)
                        {
                            <li class="page-item">
                                <a class="page-link" href="?pageNumber=@(Model.PageIndex - 1)&pageSize=@pageSize@(string.IsNullOrEmpty(selectedCategory) ? "" : "&category=" + selectedCategory)">السابق</a>
                            </li>
                        }

                        @for (int i = 1; i <= Model.TotalPages; i++)
                        {
                            <li class="page-item @(i == Model.PageIndex ? "active" : "")">
                                <a class="page-link" href="?pageNumber=@i&pageSize=@pageSize@(string.IsNullOrEmpty(selectedCategory) ? "" : "&category=" + selectedCategory)">@i</a>
                            </li>
                        }

                        @if (Model.HasNextPage)
                        {
                            <li class="page-item">
                                <a class="page-link" href="?pageNumber=@(Model.PageIndex + 1)&pageSize=@pageSize@(string.IsNullOrEmpty(selectedCategory) ? "" : "&category=" + selectedCategory)">التالي</a>
                            </li>
                        }
                    </ul>
                </nav>
            }
        </div>

        <!-- الشريط الجانبي -->
        <div class="col-xl-3 col-lg-4 d-none d-lg-block">
            <div class="sidebar">
                <!-- فئات المنتجات -->
                <div class="sidebar-section">
                    <h5>الفئات</h5>
                    <ul class="category-list">
                        <li><a href="/Products" class="@(string.IsNullOrEmpty(selectedCategory) ? "active" : "")">جميع المنتجات</a></li>
                        @foreach (var category in categories)
                        {
                            <li><a href="/Products?category=@category.Slug" class="@(selectedCategory == category.Slug ? "active" : "")">@category.Name</a></li>
                        }
                    </ul>
                </div>

                <!-- المنتجات الأكثر مبيعاً -->
                <div class="sidebar-section">
                    <h5>الأكثر مبيعاً</h5>
                    <div class="best-sellers-container" id="bestSellersContainer">
                        <!-- سيتم تحميل المنتجات هنا -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // وظيفة فرز المنتجات
        function sortProducts(sortValue) {
            const currentUrl = new URL(window.location.href);
            
            if (sortValue === 'default') {
                currentUrl.searchParams.delete('sortBy');
            } else {
                currentUrl.searchParams.set('sortBy', sortValue);
            }
            
            currentUrl.searchParams.set('pageNumber', '1');
            window.location.href = currentUrl.toString();
        }

        // تغيير حجم الصفحة
        function changePageSize(pageSize) {
            const currentUrl = new URL(window.location.href);
            currentUrl.searchParams.set('pageSize', pageSize);
            currentUrl.searchParams.set('pageNumber', '1');
            window.location.href = currentUrl.toString();
        }

        // بداية تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function () {
            // إضافة event listeners
            const sortSelect = document.getElementById('productSort');
            if (sortSelect) {
                sortSelect.addEventListener('change', function() {
                    sortProducts(this.value);
                });
            }

            const itemsSelect = document.getElementById('itemsPerPage');
            if (itemsSelect) {
                itemsSelect.addEventListener('change', function() {
                    changePageSize(this.value);
                });
            }

            // تحميل المنتجات الأكثر مبيعاً
            loadBestSellers();

            // إعداد النافذة الجانبية
            setupSideFilter();

            // إعداد خيارات العرض للموبايل
            setupMobileGridOptions();
        });

        // تحميل المنتجات الأكثر مبيعاً
        function loadBestSellers() {
            const container = document.getElementById('bestSellersContainer');
            const mobileContainer = document.getElementById('mobileBestSellersContainer');

            if (!container && !mobileContainer) return;

            // عرض رسالة التحميل
            const loadingHtml = '<div class="loading-spinner">جاري التحميل...</div>';
            if (container) container.innerHTML = loadingHtml;
            if (mobileContainer) mobileContainer.innerHTML = loadingHtml;

            fetch('/Products/GetBestSellers')
                .then(response => response.json())
                .then(data => {
                    console.log('استجابة المنتجات الأكثر مبيعاً:', data);

                    if (data.success && data.products && data.products.length > 0) {
                        const html = renderBestSellers(data.products);
                        if (container) container.innerHTML = html;
                        if (mobileContainer) mobileContainer.innerHTML = html;
                    } else {
                        const noDataHtml = '<div class="no-best-sellers">لا توجد منتجات مميزة</div>';
                        if (container) container.innerHTML = noDataHtml;
                        if (mobileContainer) mobileContainer.innerHTML = noDataHtml;
                    }
                })
                .catch(error => {
                    console.error('خطأ في تحميل المنتجات الأكثر مبيعاً:', error);
                    const errorHtml = '<div class="error-message">خطأ في التحميل</div>';
                    if (container) container.innerHTML = errorHtml;
                    if (mobileContainer) mobileContainer.innerHTML = errorHtml;
                });
        }

        // عرض المنتجات الأكثر مبيعاً
        function renderBestSellers(products) {
            let html = '';
            products.forEach((product, index) => {
                const productId = product.Id || product.id || 0;
                const productName = product.Name || product.name || 'منتج غير محدد';
                const productPrice = product.DiscountedPrice || product.discountedPrice || product.Price || product.price || 0;
                const productImage = product.ImageUrl || product.imageUrl || '/images/placeholder.jpg';

                const rankIcon = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '#' + (index + 1);

                html += `
                    <div class="best-seller-item" data-product-id="${productId}">
                        <div class="best-seller-rank">${rankIcon}</div>
                        <div class="best-seller-image">
                            <img src="${productImage}" alt="${productName}" onerror="this.src='/images/placeholder.jpg'">
                        </div>
                        <div class="best-seller-info">
                            <h6 class="best-seller-name">${productName}</h6>
                            <div class="best-seller-price">${parseFloat(productPrice).toFixed(3)} ر.ع</div>
                        </div>
                    </div>
                `;
            });
            return html;
        }

        // إعداد النافذة الجانبية
        function setupSideFilter() {
            const filterButton = document.getElementById('filterButton');
            const sideFilterOverlay = document.getElementById('sideFilterOverlay');
            const sideFilterPanel = document.getElementById('sideFilterPanel');
            const closeSideFilter = document.getElementById('closeSideFilter');

            if (!filterButton || !sideFilterOverlay || !sideFilterPanel || !closeSideFilter) return;

            // فتح النافذة الجانبية
            filterButton.addEventListener('click', function() {
                sideFilterOverlay.style.display = 'block';
                setTimeout(() => {
                    sideFilterPanel.classList.add('active');
                }, 10);
            });

            // إغلاق النافذة الجانبية
            function closeSideFilterPanel() {
                sideFilterPanel.classList.remove('active');
                setTimeout(() => {
                    sideFilterOverlay.style.display = 'none';
                }, 300);
            }

            closeSideFilter.addEventListener('click', closeSideFilterPanel);
            sideFilterOverlay.addEventListener('click', closeSideFilterPanel);

            // إعداد أدوات التحكم في النافذة الجانبية
            const mobileSortSelect = document.getElementById('mobileSortSelect');
            const mobileItemsSelect = document.getElementById('mobileItemsSelect');

            if (mobileSortSelect) {
                mobileSortSelect.addEventListener('change', function() {
                    sortProducts(this.value);
                });
            }

            if (mobileItemsSelect) {
                mobileItemsSelect.addEventListener('change', function() {
                    changePageSize(this.value);
                });
            }
        }

        // إعداد خيارات العرض للموبايل
        function setupMobileGridOptions() {
            const gridOptions = document.querySelectorAll('.mobile-grid-option');
            const productGrid = document.getElementById('productGrid');

            if (!productGrid || gridOptions.length === 0) return;

            gridOptions.forEach(option => {
                option.addEventListener('click', function() {
                    // إزالة الفئة النشطة من جميع الخيارات
                    gridOptions.forEach(opt => opt.classList.remove('active'));

                    // إضافة الفئة النشطة للخيار المحدد
                    this.classList.add('active');

                    // الحصول على عدد الأعمدة
                    const columns = this.getAttribute('data-columns');

                    // تطبيق التخطيط
                    productGrid.className = `product-grid mobile-grid-${columns}`;
                });
            });
        }
    </script>
}
