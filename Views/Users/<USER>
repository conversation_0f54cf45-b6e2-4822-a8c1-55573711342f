@model Abayat.ViewModels.EditUserViewModel

@{
    ViewData["Title"] = "تعديل المستخدم";
}

<div class="products-header text-center">
    <div class="container">
        <h1 class="fade-in">@ViewData["Title"]</h1>
        <p class="lead mb-4">تعديل بيانات المستخدم</p>
    </div>
</div>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="form-card slide-in-right">
                <h2 class="form-title">تعديل المستخدم</h2>

                <form asp-action="Edit">
                    <div asp-validation-summary="ModelOnly" class="validation-summary-errors"></div>
                    <input type="hidden" asp-for="Id" />
                    <input type="hidden" asp-for="Email" />

                    <div class="mb-3">
                        <label asp-for="Name" class="form-label"></label>
                        <input asp-for="Name" class="form-control" />
                        <span asp-validation-for="Name" class="field-validation-error"></span>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">البريد الإلكتروني</label>
                        <input value="@Model.Email" class="form-control" readonly />
                    </div>

                    <div class="mb-3">
                        <label asp-for="PhoneNumber" class="form-label"></label>
                        <input asp-for="PhoneNumber" class="form-control" />
                        <span asp-validation-for="PhoneNumber" class="field-validation-error"></span>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">الأدوار</label>
                        <div class="row">
                            @foreach (var role in Model.AllRoles)
                            {
                                <div class="col-md-6">
                                    <div class="form-check mb-2">
                                        <input type="checkbox" name="SelectedRoles" value="@role" class="form-check-input"
                                               @(Model.UserRoles.Contains(role) ? "checked" : "") />
                                        <label class="form-check-label">@role</label>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-check-circle me-2"></i> حفظ
                        </button>
                        <a asp-action="Index" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-right me-2"></i> العودة للقائمة
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
