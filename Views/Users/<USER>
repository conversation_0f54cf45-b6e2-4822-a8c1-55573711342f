@model IEnumerable<Abayat.ViewModels.UserViewModel>
@using Abayat.Models

@{
    ViewData["Title"] = "إدارة المستخدمين";
}

@functions {
    public string GetStatusDisplayName(OrderStatus status)
    {
        return status switch
        {
            OrderStatus.Pending => "قيد الانتظار",
            OrderStatus.Confirmed => "تم التأكيد",
            OrderStatus.Processing => "قيد التجهيز",
            OrderStatus.Shipped => "تم الشحن",
            OrderStatus.Delivered => "تم التسليم",
            OrderStatus.Cancelled => "تم الإلغاء",
            _ => status.ToString()
        };
    }

    public string GetStatusBadgeClass(OrderStatus status)
    {
        return status switch
        {
            OrderStatus.Pending => "bg-warning text-dark",
            OrderStatus.Confirmed => "bg-info text-dark",
            OrderStatus.Processing => "bg-primary",
            OrderStatus.Shipped => "bg-info",
            OrderStatus.Delivered => "bg-success",
            OrderStatus.Cancelled => "bg-danger",
            _ => "bg-secondary"
        };
    }
}

<div class="products-header text-center">
    <div class="container">
        <h1 class="fade-in">@ViewData["Title"]</h1>
        <p class="lead mb-4">إدارة حسابات المستخدمين في النظام</p>
    </div>
</div>

<div class="container py-5">
    <div class="form-card slide-in-right">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="form-title">قائمة المستخدمين</h2>
            <a asp-action="Create" class="btn btn-primary">
                <i class="bi bi-person-plus-fill me-2"></i> إضافة مستخدم جديد
            </a>
        </div>

        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>الاسم</th>
                        <th>البريد الإلكتروني</th>
                        <th>رقم الهاتف</th>
                        <th>الأدوار</th>
                        <th>الطلبات</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model)
                    {
                        <tr>
                            <td>@item.Name</td>
                            <td>@item.Email</td>
                            <td>@item.PhoneNumber</td>
                            <td>
                                @string.Join(", ", item.Roles)
                            </td>
                            <td>
                                @if (item.Roles.Contains("Customer"))
                                {
                                    <div class="d-flex align-items-center">
                                        <div class="me-3">
                                            <span class="badge bg-primary rounded-pill">@item.OrdersCount</span>
                                        </div>
                                        <div>
                                            @if (item.OrdersCount > 0)
                                            {
                                                <button type="button" class="btn btn-sm btn-outline-info view-orders-btn" data-order-id="@item.Id">
                                                    <i class="bi bi-eye"></i> عرض الطلبات
                                                </button>
                                            }
                                            else
                                            {
                                                <span class="text-muted">لا توجد طلبات</span>
                                            }
                                        </div>
                                    </div>
                                }
                                else
                                {
                                    <span class="text-muted">-</span>
                                }
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-pencil-fill"></i> تعديل
                                    </a>
                                    <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-sm btn-outline-info">
                                        <i class="bi bi-info-circle-fill"></i> تفاصيل
                                    </a>
                                    <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-sm btn-outline-danger">
                                        <i class="bi bi-trash-fill"></i> حذف
                                    </a>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal عرض تفاصيل الطلب -->
<div class="modal fade" id="orderDetailsModal" tabindex="-1" aria-labelledby="orderDetailsModalLabel" role="dialog">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="orderDetailsModalLabel">تفاصيل الطلب</h5>
                <button type="button" class="btn-close close-modal" aria-label="Close" id="closeModalBtn" tabindex="0"></button>
            </div>
            <div class="modal-body p-0">
                <div id="orderDetailsContent">
                    <div class="text-center py-3">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <style>
        .order-filter {
            min-width: 200px;
        }

        .customer-info {
            display: flex;
            flex-direction: column;
        }

        .customer-name {
            font-weight: bold;
        }

        .customer-fullname {
            font-size: 0.85rem;
            color: #6c757d;
        }

        .order-item-img {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 8px;
        }

        /* تنسيقات النافذة المنبثقة لعرض الطلبات */
        .modal-body.p-0 {
            padding: 0 !important;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 1.5rem;
        }

        .order-products-table {
            margin-bottom: 0;
        }

        .order-products-table th {
            background-color: rgba(30, 58, 138, 0.05);
            font-weight: 600;
        }

        .order-products-table .total-amount {
            font-size: 1.1rem;
            color: var(--primary-color);
        }

        .info-item {
            margin-bottom: 1rem;
        }

        .info-label {
            font-weight: 600;
            color: var(--text-muted);
            font-size: 0.9rem;
        }

        .info-value {
            font-weight: 500;
            color: var(--text-dark);
        }
    </style>
}

@section Scripts {
    <script>
        $(document).ready(function () {
            // عرض تفاصيل الطلبات في نافذة منبثقة
            $('.view-orders-btn').on('click', function (e) {
                e.preventDefault();
                var userId = $(this).data('order-id');
                var userName = $(this).closest('tr').find('td:first-child').text();
                var userEmail = $(this).closest('tr').find('td:nth-child(2)').text();
                var userPhone = $(this).closest('tr').find('td:nth-child(3)').text();

                $('#orderDetailsModalLabel').text('طلبات ' + userName);
                $('#orderDetailsContent').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">جاري التحميل...</span></div></div>');

                // عرض النافذة مباشرة
                $('#orderDetailsModal').modal('show');

                // تحميل تفاصيل الطلب
                $.ajax({
                    url: '/Orders/Manage?userId=' + userId,
                    type: 'GET',
                    cache: false,
                    success: function (data) {
                        // إنشاء هيكل العرض
                        var content = $('<div class="row g-0"></div>');
                        var leftCol = $('<div class="col-md-8 border-end"><div class="p-4"></div></div>');
                        var rightCol = $('<div class="col-md-4"><div class="p-4"></div></div>');

                        // استخراج الطلبات من البيانات - استخدام طريقة أكثر مباشرة
                        var orderRows = [];
                        $(data).find('tr[data-user-id="' + userId + '"]').each(function() {
                            orderRows.push($(this));
                        });

                        // إضافة عنوان للطلبات مع عدد الطلبات
                        var ordersCount = orderRows.length;
                        leftCol.find('div').append('<h3 class="section-title border-bottom pb-2 mb-3">الطلبات <span class="badge bg-primary rounded-pill">' + ordersCount + '</span></h3>');

                        // إنشاء جدول مبسط للطلبات
                        var simpleTable = $('<table class="table table-hover"></table>');
                        var thead = $('<thead><tr><th>رقم الطلب</th><th>التاريخ</th><th>المبلغ</th><th>الحالة</th></tr></thead>');
                        var tbody = $('<tbody></tbody>');

                        // استخراج الطلبات من البيانات - استخدام طريقة أكثر مباشرة
                        var orderRows = [];
                        $(data).find('tr[data-user-id="' + userId + '"]').each(function() {
                            orderRows.push($(this));
                        });

                        if (orderRows.length > 0) {
                            // Crear la tabla primero
                            simpleTable.append(thead);
                            simpleTable.append(tbody);

                            // Añadir la tabla al contenido
                            leftCol.find('div').append('<div class="table-responsive"></div>');
                            leftCol.find('.table-responsive').append(simpleTable);

                            // Ahora añadir las filas
                            $.each(orderRows, function(index, rowElement) {
                                var orderId = rowElement.find('td:first-child').text();
                                var orderDate = rowElement.find('td:nth-child(3)').text();
                                var orderAmount = rowElement.find('td:nth-child(4)').text();
                                var orderStatus = rowElement.find('td:nth-child(5) .badge').text();
                                var statusClass = rowElement.find('td:nth-child(5) .badge').attr('class').replace('badge', '').trim();

                                var newRow = $('<tr></tr>');
                                newRow.append('<td>' + orderId + '</td>');
                                newRow.append('<td>' + orderDate + '</td>');
                                newRow.append('<td>' + orderAmount + '</td>');
                                newRow.append('<td><span class="badge ' + statusClass + '">' + orderStatus + '</span></td>');

                                tbody.append(newRow);
                            });

                            // La tabla ya se ha añadido anteriormente
                        } else {
                            leftCol.find('div').append('<div class="alert alert-info">لا توجد طلبات لهذا المستخدم</div>');
                        }

                        // إضافة معلومات المستخدم
                        rightCol.find('div').append('<h3 class="section-title border-bottom pb-2 mb-3">معلومات المستخدم</h3>');

                        // إضافة معلومات المستخدم
                        var userInfo = $('<div class="order-info"></div>');
                        userInfo.append('<div class="info-item"><span class="info-label d-block">الاسم</span><span class="info-value d-block">' + userName + '</span></div>');
                        userInfo.append('<div class="info-item"><span class="info-label d-block">البريد الإلكتروني</span><span class="info-value d-block">' + userEmail + '</span></div>');
                        userInfo.append('<div class="info-item"><span class="info-label d-block">رقم الهاتف</span><span class="info-value d-block">' + userPhone + '</span></div>');

                        // إضافة إحصائيات الطلبات
                        if (orderRows.length > 0) {
                            userInfo.append('<hr class="my-3">');
                            userInfo.append('<div class="info-item"><span class="info-label d-block">عدد الطلبات</span><span class="info-value d-block badge bg-primary rounded-pill">' + orderRows.length + '</span></div>');
                        }

                        rightCol.find('div').append(userInfo);

                        // إضافة الأعمدة إلى المحتوى
                        content.append(leftCol);
                        content.append(rightCol);

                        // عرض المحتوى
                        $('#orderDetailsContent').html(content);
                    },
                    error: function () {
                        $('#orderDetailsContent').html('<div class="alert alert-danger">حدث خطأ أثناء تحميل تفاصيل الطلبات</div>');
                    }
                });
            });

            // معالجة أحداث النافذة المنبثقة
            var modal = $('#orderDetailsModal');

            // قبل فتح النافذة المنبثقة
            modal.on('show.bs.modal', function () {
                // إعادة تعيين المحتوى إذا كان فارغًا
                if ($('#orderDetailsContent').is(':empty')) {
                    $('#orderDetailsContent').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">جاري التحميل...</span></div></div>');
                }
            });

            // قبل إغلاق النافذة المنبثقة
            modal.on('hide.bs.modal', function () {
                // إزالة التركيز من جميع العناصر داخل النافذة المنبثقة
                $('#closeModalBtn').blur();
                document.activeElement.blur();
            });

            // بعد إغلاق النافذة المنبثقة
            modal.on('hidden.bs.modal', function () {
                // إعادة تعيين المحتوى
                $('#orderDetailsContent').html('');
                // نقل التركيز إلى عنصر آمن في الصفحة
                setTimeout(function() {
                    // نقل التركيز إلى الصفحة الرئيسية
                    document.body.focus();
                }, 10);
            });

            // معالجة أزرار الإغلاق
            $('.close-modal').on('click', function() {
                // الحصول على معرف النافذة المنبثقة
                var modalElement = $(this).closest('.modal');

                // إغلاق النافذة المنبثقة باستخدام Bootstrap API
                var modalInstance = bootstrap.Modal.getInstance(modalElement[0]);
                if (modalInstance) {
                    modalInstance.hide();
                }

                return false;
            });

            // إضافة معالج عام للنقر على أي زر إغلاق
            $(document).on('click', '[data-bs-dismiss="modal"]', function() {
                var modalElement = $(this).closest('.modal')[0];
                var modalInstance = bootstrap.Modal.getInstance(modalElement);
                if (modalInstance) {
                    modalInstance.hide();
                }
                return false;
            });
        });
    </script>
}
