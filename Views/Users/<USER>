@model Abayat.ViewModels.UserViewModel

@{
    ViewData["Title"] = "حذف المستخدم";
}

<div class="products-header text-center">
    <div class="container">
        <h1 class="fade-in">@ViewData["Title"]</h1>
        <p class="lead mb-4">تأكيد حذف المستخدم</p>
    </div>
</div>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="form-card zoom-in">
                <div class="alert alert-danger mb-4">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-exclamation-triangle-fill fs-3 me-3"></i>
                        <div>
                            <h5 class="alert-heading mb-1">تحذير!</h5>
                            <p class="mb-0">هل أنت متأكد من رغبتك في حذف هذا المستخدم؟ لا يمكن التراجع عن هذه العملية.</p>
                        </div>
                    </div>
                </div>

                <div asp-validation-summary="All" class="validation-summary-errors"></div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">الاسم:</label>
                            <p class="form-control-static">@Model.Name</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">البريد الإلكتروني:</label>
                            <p class="form-control-static">@Model.Email</p>
                        </div>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">رقم الهاتف:</label>
                            <p class="form-control-static">@(string.IsNullOrEmpty(Model.PhoneNumber) ? "غير محدد" : Model.PhoneNumber)</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">الأدوار:</label>
                            <p class="form-control-static">
                                @if (Model.Roles.Any())
                                {
                                    @string.Join(", ", Model.Roles)
                                }
                                else
                                {
                                    <span class="text-muted">لا توجد أدوار</span>
                                }
                            </p>
                        </div>
                    </div>
                </div>

                <form asp-action="Delete">
                    <input type="hidden" name="id" value="@Model.Id" />
                    <div class="form-actions">
                        <button type="submit" class="btn btn-danger">
                            <i class="bi bi-trash-fill me-2"></i> حذف
                        </button>
                        <a asp-action="Index" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-right me-2"></i> العودة للقائمة
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
