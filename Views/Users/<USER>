@model Abayat.ViewModels.UserViewModel

@{
    ViewData["Title"] = "تفاصيل المستخدم";
}

<div class="products-header text-center">
    <div class="container">
        <h1 class="fade-in">@ViewData["Title"]</h1>
        <p class="lead mb-4">عرض تفاصيل المستخدم</p>
    </div>
</div>

<div class="container py-5">
    <div class="form-card slide-in-right">
        <h2 class="form-title">تفاصيل المستخدم</h2>

        <div class="row mb-4">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label fw-bold">الاسم:</label>
                    <p class="form-control-static">@Model.Name</p>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label fw-bold">البريد الإلكتروني:</label>
                    <p class="form-control-static">@Model.Email</p>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label fw-bold">رقم الهاتف:</label>
                    <p class="form-control-static">@(string.IsNullOrEmpty(Model.PhoneNumber) ? "غير محدد" : Model.PhoneNumber)</p>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label fw-bold">الأدوار:</label>
                    <p class="form-control-static">
                        @if (Model.Roles.Any())
                        {
                            @string.Join(", ", Model.Roles)
                        }
                        else
                        {
                            <span class="text-muted">لا توجد أدوار</span>
                        }
                    </p>
                </div>
            </div>
        </div>

        <div class="form-actions">
            <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary">
                <i class="bi bi-pencil-fill me-2"></i> تعديل
            </a>
            <a asp-action="Index" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-right me-2"></i> العودة للقائمة
            </a>
        </div>
    </div>
</div>
