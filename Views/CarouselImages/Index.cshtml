@model IEnumerable<Abayat.Models.CarouselImage>

@{
    ViewData["Title"] = "إدارة صور الشريط المتحرك";
}

<style>
    .carousel-admin-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 4rem 0;
        margin-bottom: 2rem;
    }

    .carousel-image-preview {
        position: relative;
        overflow: hidden;
        border-radius: 8px;
        transition: transform 0.3s ease;
    }

    .carousel-image-preview:hover {
        transform: scale(1.05);
    }

    .carousel-image-preview img {
        width: 120px;
        height: 80px;
        object-fit: cover;
        border-radius: 8px;
    }

    .status-badge {
        position: absolute;
        top: 5px;
        right: 5px;
        font-size: 0.7rem;
    }

    .order-badge {
        position: absolute;
        top: 5px;
        left: 5px;
        background: rgba(0,0,0,0.7);
        color: white;
        padding: 2px 6px;
        border-radius: 10px;
        font-size: 0.7rem;
    }

    .carousel-table {
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    }

    .table-header {
        background: linear-gradient(45deg, #6a0dad, #8a2be2);
        color: white;
    }

    .action-buttons .btn {
        margin: 0 2px;
        border-radius: 6px;
    }

    .preview-modal .modal-dialog {
        max-width: 800px;
    }

    .sortable-row {
        cursor: move;
    }

    .sortable-row:hover {
        background-color: #f8f9fa;
    }

    .drag-handle {
        cursor: grab;
        color: #6c757d;
    }

    .drag-handle:active {
        cursor: grabbing;
    }
</style>

<div class="carousel-admin-container">
    <div class="container text-center">
        <h1 class="display-4 mb-3">
            <i class="bi bi-images me-3"></i>
            إدارة صور الشريط المتحرك
        </h1>
        <p class="lead mb-4">إدارة الصور التي تظهر في الشريط المتحرك في الصفحة الرئيسية</p>
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="d-flex justify-content-center gap-3">
                    <a asp-action="Create" class="btn btn-light btn-lg">
                        <i class="bi bi-plus-circle me-2"></i> إضافة صورة جديدة
                    </a>
                    <button type="button" class="btn btn-outline-light btn-lg" data-bs-toggle="modal" data-bs-target="#previewModal">
                        <i class="bi bi-eye me-2"></i> معاينة الكاروسيل
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container py-5">
    <div class="row">
        <div class="col-12">
            <div class="carousel-table">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" id="carouselTable">
                        <thead class="table-header">
                            <tr>
                                <th width="5%">
                                    <i class="bi bi-arrows-move"></i>
                                </th>
                                <th width="20%">
                                    <i class="bi bi-card-text me-2"></i>العنوان
                                </th>
                                <th width="20%">
                                    <i class="bi bi-image me-2"></i>الصورة
                                </th>
                                <th width="15%">
                                    <i class="bi bi-link-45deg me-2"></i>الرابط
                                </th>
                                <th width="10%">
                                    <i class="bi bi-sort-numeric-up me-2"></i>الترتيب
                                </th>
                                <th width="10%">
                                    <i class="bi bi-toggle-on me-2"></i>الحالة
                                </th>
                                <th width="10%">
                                    <i class="bi bi-calendar me-2"></i>التاريخ
                                </th>
                                <th width="10%">
                                    <i class="bi bi-gear me-2"></i>الإجراءات
                                </th>
                            </tr>
                        </thead>
                        <tbody id="sortableTableBody">
                            @foreach (var item in Model.OrderBy(x => x.DisplayOrder))
                            {
                                <tr class="sortable-row" data-id="@item.Id">
                                    <td class="text-center">
                                        <i class="bi bi-grip-vertical drag-handle"></i>
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div>
                                                <strong>@item.Title</strong>
                                                @if (!string.IsNullOrEmpty(item.Description))
                                                {
                                                    <br><small class="text-muted">@item.Description</small>
                                                }
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        @if (!string.IsNullOrEmpty(item.ImageUrl))
                                        {
                                            <div class="carousel-image-preview">
                                                <img src="@item.ImageUrl" alt="@item.Title" class="img-thumbnail" />
                                                <span class="order-badge">@item.DisplayOrder</span>
                                                @if (item.IsActive)
                                                {
                                                    <span class="badge bg-success status-badge">نشط</span>
                                                }
                                                else
                                                {
                                                    <span class="badge bg-danger status-badge">غير نشط</span>
                                                }
                                            </div>
                                        }
                                        else
                                        {
                                            <div class="text-center text-muted p-3 border rounded">
                                                <i class="bi bi-image fs-4"></i>
                                                <br><small>لا توجد صورة</small>
                                            </div>
                                        }
                                    </td>
                                    <td>
                                        @if (!string.IsNullOrEmpty(item.LinkUrl))
                                        {
                                            <a href="@item.LinkUrl" target="_blank" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-box-arrow-up-right me-1"></i>
                                                عرض الرابط
                                            </a>
                                        }
                                        else
                                        {
                                            <span class="text-muted">-</span>
                                        }
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-primary fs-6">@item.DisplayOrder</span>
                                    </td>
                                    <td class="text-center">
                                        @if (item.IsActive)
                                        {
                                            <span class="badge bg-success">
                                                <i class="bi bi-check-circle me-1"></i>نشط
                                            </span>
                                        }
                                        else
                                        {
                                            <span class="badge bg-danger">
                                                <i class="bi bi-x-circle me-1"></i>غير نشط
                                            </span>
                                        }
                                    </td>
                                    <td class="text-center">
                                        <small class="text-muted">@item.CreatedAt.ToString("dd/MM/yyyy")</small>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-sm btn-warning" title="تعديل">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-sm btn-info" title="تفاصيل">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-sm btn-danger" title="حذف">
                                                <i class="bi bi-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>

            @if (!Model.Any())
            {
                <div class="text-center py-5">
                    <div class="mb-4">
                        <i class="bi bi-images text-muted" style="font-size: 4rem;"></i>
                    </div>
                    <h4 class="text-muted mb-3">لا توجد صور في الشريط المتحرك</h4>
                    <p class="text-muted mb-4">قم بإضافة صور جديدة لتظهر في الصفحة الرئيسية</p>
                    <a asp-action="Create" class="btn btn-primary btn-lg">
                        <i class="bi bi-plus-circle me-2"></i> إضافة أول صورة
                    </a>
                </div>
            }
        </div>
    </div>
</div>

<!-- مودال معاينة الكاروسيل -->
<div class="modal fade preview-modal" id="previewModal" tabindex="-1" aria-labelledby="previewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="previewModalLabel">
                    <i class="bi bi-eye me-2"></i>معاينة الشريط المتحرك
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-0">
                @if (Model.Any(x => x.IsActive))
                {
                    <div id="carouselPreview" class="carousel slide" data-bs-ride="carousel">
                        <div class="carousel-indicators">
                            @{
                                var activeImages = Model.Where(x => x.IsActive).OrderBy(x => x.DisplayOrder).ToList();
                            }
                            @for (int i = 0; i < activeImages.Count; i++)
                            {
                                <button type="button" data-bs-target="#carouselPreview" data-bs-slide-to="@i"
                                        class="@(i == 0 ? "active" : "")" aria-current="@(i == 0 ? "true" : "false")"
                                        aria-label="Slide @(i + 1)"></button>
                            }
                        </div>
                        <div class="carousel-inner">
                            @for (int i = 0; i < activeImages.Count; i++)
                            {
                                var image = activeImages[i];
                                <div class="carousel-item @(i == 0 ? "active" : "")">
                                    <img src="@image.ImageUrl" class="d-block w-100" alt="@image.Title" style="height: 400px; object-fit: cover;">
                                    <div class="carousel-caption d-none d-md-block">
                                        <h5>@image.Title</h5>
                                        @if (!string.IsNullOrEmpty(image.Description))
                                        {
                                            <p>@image.Description</p>
                                        }
                                    </div>
                                </div>
                            }
                        </div>
                        <button class="carousel-control-prev" type="button" data-bs-target="#carouselPreview" data-bs-slide="prev">
                            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">Previous</span>
                        </button>
                        <button class="carousel-control-next" type="button" data-bs-target="#carouselPreview" data-bs-slide="next">
                            <span class="carousel-control-next-icon" aria-hidden="true"></span>
                            <span class="visually-hidden">Next</span>
                        </button>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="bi bi-images text-muted" style="font-size: 3rem;"></i>
                        <h5 class="text-muted mt-3">لا توجد صور نشطة للمعاينة</h5>
                        <p class="text-muted">قم بتفعيل بعض الصور لمعاينة الكاروسيل</p>
                    </div>
                }
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <a href="/" class="btn btn-primary" target="_blank">
                    <i class="bi bi-box-arrow-up-right me-2"></i>عرض في الصفحة الرئيسية
                </a>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
    <script>
        $(document).ready(function() {
            // تهيئة السحب والإفلات
            const sortableTable = document.getElementById('sortableTableBody');
            if (sortableTable) {
                new Sortable(sortableTable, {
                    handle: '.drag-handle',
                    animation: 150,
                    ghostClass: 'sortable-ghost',
                    chosenClass: 'sortable-chosen',
                    dragClass: 'sortable-drag',
                    onEnd: function(evt) {
                        updateDisplayOrder();
                    }
                });
            }

            // تحديث ترتيب العرض
            function updateDisplayOrder() {
                const rows = document.querySelectorAll('#sortableTableBody tr');
                const updates = [];

                rows.forEach((row, index) => {
                    const id = row.getAttribute('data-id');
                    const newOrder = index + 1;
                    updates.push({ id: parseInt(id), displayOrder: newOrder });

                    // تحديث الرقم في الواجهة
                    const orderBadge = row.querySelector('.order-badge');
                    const orderCell = row.querySelector('td:nth-child(5) .badge');
                    if (orderBadge) orderBadge.textContent = newOrder;
                    if (orderCell) orderCell.textContent = newOrder;
                });

                // إرسال التحديث للخادم
                fetch('/CarouselImages/UpdateDisplayOrder', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                    },
                    body: JSON.stringify(updates)
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast('تم تحديث ترتيب الصور بنجاح', 'success');
                    } else {
                        showToast('حدث خطأ في تحديث الترتيب', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showToast('حدث خطأ في تحديث الترتيب', 'error');
                });
            }

            // دالة إظهار الرسائل
            function showToast(message, type = 'info') {
                const toast = $(`
                    <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                        <div class="d-flex">
                            <div class="toast-body">
                                ${message}
                            </div>
                            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                        </div>
                    </div>
                `);

                // إضافة container للـ toasts إذا لم يكن موجوداً
                if ($('#toast-container').length === 0) {
                    $('body').append('<div id="toast-container" class="toast-container position-fixed top-0 end-0 p-3"></div>');
                }

                $('#toast-container').append(toast);
                toast.toast('show');

                // إزالة التوست بعد 5 ثوان
                setTimeout(() => {
                    toast.remove();
                }, 5000);
            }

            // تحسين تجربة المستخدم
            $('.carousel-image-preview img').on('click', function() {
                const src = $(this).attr('src');
                const title = $(this).attr('alt');

                const modal = $(`
                    <div class="modal fade" id="imageModal" tabindex="-1">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">${title}</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body text-center">
                                    <img src="${src}" class="img-fluid" alt="${title}">
                                </div>
                            </div>
                        </div>
                    </div>
                `);

                $('body').append(modal);
                modal.modal('show');

                modal.on('hidden.bs.modal', function() {
                    modal.remove();
                });
            });
        });
    </script>

    <style>
        .sortable-ghost {
            opacity: 0.4;
        }

        .sortable-chosen {
            background-color: #e3f2fd;
        }

        .sortable-drag {
            background-color: #fff;
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
        }

        .toast-container {
            z-index: 1060;
        }
    </style>
}
