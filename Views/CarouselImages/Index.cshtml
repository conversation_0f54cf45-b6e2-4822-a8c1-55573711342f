@model IEnumerable<Abayat.Models.CarouselImage>

@{
    ViewData["Title"] = "إدارة صور الشريط المتحرك";
}

<div class="products-header text-center">
    <div class="container">
        <h1 class="fade-in">@ViewData["Title"]</h1>
        <p class="lead mb-4">إدارة الصور التي تظهر في الشريط المتحرك في الصفحة الرئيسية</p>
    </div>
</div>

<div class="container py-5">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>قائمة الصور</h2>
        <a asp-action="Create" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> إضافة صورة جديدة
        </a>
    </div>

    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead class="table-dark">
                <tr>
                    <th>
                        @Html.DisplayNameFor(model => model.Title)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.ImageUrl)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.LinkUrl)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.DisplayOrder)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.IsActive)
                    </th>
                    <th>
                        @Html.DisplayNameFor(model => model.CreatedAt)
                    </th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var item in Model)
                {
                    <tr>
                        <td>
                            @Html.DisplayFor(modelItem => item.Title)
                        </td>
                        <td>
                            @if (!string.IsNullOrEmpty(item.ImageUrl))
                            {
                                <img src="@item.ImageUrl" alt="@item.Title" style="max-width: 100px; max-height: 60px;" class="img-thumbnail" />
                            }
                            else
                            {
                                <span class="text-muted">لا توجد صورة</span>
                            }
                        </td>
                        <td>
                            @if (!string.IsNullOrEmpty(item.LinkUrl))
                            {
                                <a href="@item.LinkUrl" target="_blank">@item.LinkUrl</a>
                            }
                            else
                            {
                                <span class="text-muted">-</span>
                            }
                        </td>
                        <td>
                            @Html.DisplayFor(modelItem => item.DisplayOrder)
                        </td>
                        <td>
                            @if (item.IsActive)
                            {
                                <span class="badge bg-success">نشط</span>
                            }
                            else
                            {
                                <span class="badge bg-danger">غير نشط</span>
                            }
                        </td>
                        <td>
                            @item.CreatedAt.ToString("yyyy/MM/dd")
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-sm btn-primary">
                                    <i class="bi bi-pencil"></i> تعديل
                                </a>
                                <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-sm btn-info">
                                    <i class="bi bi-eye"></i> تفاصيل
                                </a>
                                <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-sm btn-danger">
                                    <i class="bi bi-trash"></i> حذف
                                </a>
                            </div>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>

    @if (!Model.Any())
    {
        <div class="alert alert-info text-center mt-4">
            <i class="bi bi-info-circle fs-4"></i>
            <p class="mb-0 mt-2">لا توجد صور في الشريط المتحرك حالياً. قم بإضافة صور جديدة.</p>
        </div>
    }
</div>
