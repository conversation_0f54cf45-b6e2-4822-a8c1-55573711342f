@model Abayat.Models.CarouselImage

@{
    ViewData["Title"] = "تعديل صورة الشريط المتحرك";
}

<div class="products-header text-center">
    <div class="container">
        <h1 class="fade-in">@ViewData["Title"]</h1>
        <p class="lead mb-4">تعديل بيانات صورة الشريط المتحرك "@Model.Title"</p>
    </div>
</div>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="form-card slide-in-right">
                <h2 class="form-title">تعديل بيانات الصورة</h2>

                <form asp-action="Edit" enctype="multipart/form-data">
                    <div asp-validation-summary="ModelOnly" class="validation-summary-errors"></div>
                    <input type="hidden" asp-for="Id" />
                    <input type="hidden" asp-for="CreatedAt" />
                    <input type="hidden" asp-for="ImageUrl" />

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="Title" class="form-label">عنوان الصورة</label>
                                <input asp-for="Title" class="form-control" placeholder="أدخل عنوان الصورة" />
                                <span asp-validation-for="Title" class="field-validation-error"></span>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="DisplayOrder" class="form-label">ترتيب العرض</label>
                                <input asp-for="DisplayOrder" class="form-control" type="number" min="0" />
                                <span asp-validation-for="DisplayOrder" class="field-validation-error"></span>
                                <small class="text-muted">الرقم الأصغر يظهر أولاً</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label asp-for="Description" class="form-label">وصف الصورة (اختياري)</label>
                        <textarea asp-for="Description" class="form-control" rows="3" placeholder="أدخل وصف الصورة"></textarea>
                        <span asp-validation-for="Description" class="field-validation-error"></span>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="imageFile" class="form-label">تغيير الصورة (اختياري)</label>
                                <input type="file" name="imageFile" id="imageFile" class="form-control" accept="image/*" />
                                <small class="text-muted">يفضل صورة بحجم 1200x400 بكسل</small>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="LinkUrl" class="form-label">رابط التوجيه (اختياري)</label>
                                <input asp-for="LinkUrl" class="form-control" placeholder="أدخل الرابط الذي سيتم التوجيه إليه عند النقر على الصورة" />
                                <span asp-validation-for="LinkUrl" class="field-validation-error"></span>
                            </div>
                        </div>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.ImageUrl))
                    {
                        <div class="form-group mt-3">
                            <label class="form-label">الصورة الحالية</label>
                            <div class="p-3 border rounded">
                                <div class="d-flex align-items-center">
                                    <img src="@Model.ImageUrl" alt="@Model.Title" class="img-thumbnail me-3" style="max-height: 100px; max-width: 200px;" />
                                    <div>
                                        <p class="mb-0 text-muted">قم بتحميل صورة جديدة لتغيير الصورة الحالية.</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }

                    <div class="form-group mt-3">
                        <div class="form-check">
                            <input asp-for="IsActive" class="form-check-input" />
                            <label asp-for="IsActive" class="form-check-label">نشط</label>
                        </div>
                    </div>

                    <div class="form-actions">
                        <a asp-action="Index" class="btn btn-outline-secondary form-btn">
                            <i class="bi bi-arrow-right"></i> العودة إلى القائمة
                        </a>
                        <button type="submit" class="btn btn-primary form-btn">
                            <i class="bi bi-check-circle"></i> حفظ التغييرات
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
