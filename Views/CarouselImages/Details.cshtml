@model Abayat.Models.CarouselImage

@{
    ViewData["Title"] = "تفاصيل صورة الشريط المتحرك";
}

<div class="products-header text-center">
    <div class="container">
        <h1 class="fade-in">@ViewData["Title"]</h1>
        <p class="lead mb-4">عرض تفاصيل صورة الشريط المتحرك "@Model.Title"</p>
    </div>
</div>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">تفاصيل الصورة</h3>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        @if (!string.IsNullOrEmpty(Model.ImageUrl))
                        {
                            <img src="@Model.ImageUrl" alt="@Model.Title" class="img-fluid rounded" style="max-height: 300px;" />
                        }
                        else
                        {
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle"></i> لا توجد صورة
                            </div>
                        }
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="fw-bold">@Html.DisplayNameFor(model => model.Title)</label>
                                <p>@Html.DisplayFor(model => model.Title)</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="fw-bold">@Html.DisplayNameFor(model => model.DisplayOrder)</label>
                                <p>@Html.DisplayFor(model => model.DisplayOrder)</p>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="fw-bold">@Html.DisplayNameFor(model => model.Description)</label>
                        @if (!string.IsNullOrEmpty(Model.Description))
                        {
                            <p>@Html.DisplayFor(model => model.Description)</p>
                        }
                        else
                        {
                            <p class="text-muted">لا يوجد وصف</p>
                        }
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="fw-bold">@Html.DisplayNameFor(model => model.LinkUrl)</label>
                                @if (!string.IsNullOrEmpty(Model.LinkUrl))
                                {
                                    <p><a href="@Model.LinkUrl" target="_blank">@Model.LinkUrl</a></p>
                                }
                                else
                                {
                                    <p class="text-muted">لا يوجد رابط</p>
                                }
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="fw-bold">@Html.DisplayNameFor(model => model.IsActive)</label>
                                <p>
                                    @if (Model.IsActive)
                                    {
                                        <span class="badge bg-success">نشط</span>
                                    }
                                    else
                                    {
                                        <span class="badge bg-danger">غير نشط</span>
                                    }
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="fw-bold">@Html.DisplayNameFor(model => model.CreatedAt)</label>
                        <p>@Model.CreatedAt.ToString("yyyy/MM/dd HH:mm")</p>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-flex justify-content-between">
                        <a asp-action="Index" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-right"></i> العودة إلى القائمة
                        </a>
                        <div>
                            <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary">
                                <i class="bi bi-pencil"></i> تعديل
                            </a>
                            <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-danger">
                                <i class="bi bi-trash"></i> حذف
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
