@model Abayat.Models.CarouselImage

@{
    ViewData["Title"] = "إضافة صورة جديدة للشريط المتحرك";
}

<div class="products-header text-center">
    <div class="container">
        <h1 class="fade-in">@ViewData["Title"]</h1>
        <p class="lead mb-4">إضافة صورة جديدة للعرض في الشريط المتحرك في الصفحة الرئيسية</p>
    </div>
</div>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="form-card zoom-in">
                <h2 class="form-title">بيانات الصورة</h2>

                <form asp-action="Create" enctype="multipart/form-data">
                    <div asp-validation-summary="ModelOnly" class="validation-summary-errors"></div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="Title" class="form-label">عنوان الصورة</label>
                                <input asp-for="Title" class="form-control" placeholder="أدخل عنوان الصورة" />
                                <span asp-validation-for="Title" class="field-validation-error"></span>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="DisplayOrder" class="form-label">ترتيب العرض</label>
                                <input asp-for="DisplayOrder" class="form-control" type="number" min="0" />
                                <span asp-validation-for="DisplayOrder" class="field-validation-error"></span>
                                <small class="text-muted">الرقم الأصغر يظهر أولاً</small>
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label asp-for="Description" class="form-label">وصف الصورة (اختياري)</label>
                        <textarea asp-for="Description" class="form-control" rows="3" placeholder="أدخل وصف الصورة"></textarea>
                        <span asp-validation-for="Description" class="field-validation-error"></span>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="imageFile" class="form-label">الصورة</label>
                                <input type="file" name="imageFile" id="imageFile" class="form-control" accept="image/*" required />
                                <small class="text-muted">يفضل صورة بأبعاد 800×500 بكسل</small>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group">
                                <label asp-for="LinkUrl" class="form-label">رابط التوجيه (اختياري)</label>
                                <input asp-for="LinkUrl" class="form-control" placeholder="أدخل الرابط الذي سيتم التوجيه إليه عند النقر على الصورة" />
                                <span asp-validation-for="LinkUrl" class="field-validation-error"></span>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mt-3">
                        <div class="form-check">
                            <input asp-for="IsActive" class="form-check-input" checked />
                            <label asp-for="IsActive" class="form-check-label">نشط</label>
                        </div>
                    </div>

                    <div class="form-actions">
                        <a asp-action="Index" class="btn btn-outline-secondary form-btn">
                            <i class="bi bi-arrow-right"></i> العودة إلى القائمة
                        </a>
                        <button type="submit" class="btn btn-primary form-btn">
                            <i class="bi bi-check-circle"></i> إضافة الصورة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script src="~/js/carousel-form.js" asp-append-version="true"></script>
}
