@model IEnumerable<Abayat.Models.Category>

@{
    ViewData["Title"] = "الفئات";
}

<section class="hero-section">
    <div class="container">
        <div class="hero-content text-center">
            <h1 class="display-4 animate__animated animate__fadeInDown">@ViewData["Title"]</h1>
            <p class="lead mb-4 animate__animated animate__fadeIn animate__delay-1s">تصفحي فئات منتجاتنا المتنوعة واختاري ما يناسبك</p>
            @if (User.IsInRole("Admin"))
            {
                <div class="animate__animated animate__fadeInUp animate__delay-1s">
                    <a asp-action="Create" class="btn btn-primary btn-lg">
                        <i class="bi bi-plus-circle me-2"></i> إضافة فئة جديدة
                    </a>
                </div>
            }
        </div>
    </div>
</section>

<section class="categories-section py-5">
    <div class="container">
        <div class="d-flex justify-content-end mb-4">
            <div id="categoriesDisplayControls" class="display-controls">
                <div class="display-options-section d-flex gap-3 align-items-center">
                    <span class="display-options-section-title">حجم الصور:</span>
                    <div class="display-control size-control" data-size="tiny" title="صغير جداً">
                        <i class="bi bi-image"></i>
                    </div>
                    <div class="display-control size-control" data-size="small" title="صغير">
                        <i class="bi bi-image"></i>
                    </div>
                    <div class="display-control size-control" data-size="medium" title="متوسط">
                        <i class="bi bi-image"></i>
                    </div>
                    <div class="display-control size-control" data-size="large" title="كبير">
                        <i class="bi bi-image"></i>
                    </div>
                </div>

                <div class="display-options-section d-flex gap-3 align-items-center ms-4">
                    <span class="display-options-section-title">عناصر في الصف:</span>
                    <div class="display-control row-control" data-items="2" title="2 عناصر">2</div>
                    <div class="display-control row-control" data-items="3" title="3 عناصر">3</div>
                    <div class="display-control row-control" data-items="4" title="4 عناصر">4</div>
                </div>
            </div>
        </div>

        <div class="categories-grid">
            @foreach (var item in Model)
            {
                <div class="category-item animate__animated animate__fadeInUp">
                    <div class="category-inner">
                        <div class="category-front">
                            @if (!string.IsNullOrEmpty(item.ImageUrl))
                            {
                                <img src="@item.ImageUrl" class="category-img" alt="@item.Name">
                            }
                            else
                            {
                                <img src="https://placehold.co/600x400/6a0dad/ffffff?text=@item.Name" class="category-img" alt="@item.Name">
                            }
                            <div class="category-content">
                                <h3 class="category-name">@item.Name</h3>
                                @if (item.Products != null)
                                {
                                    <span class="category-badge">@item.Products.Count منتج</span>
                                }
                                else
                                {
                                    <span class="category-badge">0 منتج</span>
                                }
                            </div>
                        </div>
                        <div class="category-back <EMAIL>">
                            <div class="category-back-content">
                                <h3 class="category-name">@item.Name</h3>
                                <p class="category-description">@(item.Description ?? "استكشفي مجموعتنا المتنوعة من " + item.Name)</p>
                                <div class="category-actions">
                                    <a asp-controller="Products" asp-action="Index" asp-route-category="@item.Slug" class="btn btn-primary category-btn">
                                        <i class="bi bi-eye me-1"></i> عرض المنتجات
                                    </a>

                                    @if (User.IsInRole("Admin"))
                                    {
                                        <div class="admin-actions mt-2">
                                            <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-sm btn-outline-light">
                                                <i class="bi bi-pencil"></i> تعديل
                                            </a>
                                            <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-sm btn-outline-light">
                                                <i class="bi bi-info-circle"></i> التفاصيل
                                            </a>
                                            <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-sm btn-outline-danger">
                                                <i class="bi bi-trash"></i> حذف
                                            </a>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>

    <!-- Panel de opciones de visualización completo -->
    <div id="displayOptionsPanel" class="display-options-panel">
        <button class="display-options-toggle" onclick="toggleDisplayOptions()">
            <i class="bi bi-sliders"></i>
        </button>

        <div class="display-options-header">
            <h5 class="display-options-title">خيارات العرض</h5>
            <button class="display-options-close" onclick="toggleDisplayOptions()">
                <i class="bi bi-x-lg"></i>
            </button>
        </div>

        <div class="display-options-section">
            <div class="display-options-section-title">حجم الصور</div>
            <div class="display-controls">
                <div class="display-control size-control" data-size="tiny">صغير جداً</div>
                <div class="display-control size-control" data-size="small">صغير</div>
                <div class="display-control size-control" data-size="medium">متوسط</div>
                <div class="display-control size-control" data-size="large">كبير</div>
            </div>
        </div>

        <div class="display-options-section">
            <div class="display-options-section-title">عناصر في الصف</div>
            <div class="display-controls">
                <div class="display-control row-control" data-items="2">2</div>
                <div class="display-control row-control" data-items="3">3</div>
                <div class="display-control row-control" data-items="4">4</div>
                <div class="display-control row-control" data-items="6">6</div>
            </div>
        </div>

        <div class="display-options-section">
            <div class="display-options-section-title">تأثير الصورة</div>
            <div class="display-controls">
                <div class="display-control effect-control" data-effect="zoom">تكبير</div>
                <div class="display-control effect-control" data-effect="fade">تلاشي</div>
                <div class="display-control effect-control" data-effect="none">بدون</div>
            </div>
        </div>
    </div>

    <!-- Botón flotante para opciones de visualización en móvil -->
    <div class="display-options-fab d-md-none" onclick="toggleDisplayOptions()">
        <i class="bi bi-sliders"></i>
    </div>
</section>

<section class="cta-section">
    <div class="container">
        <div class="cta-content">
            <h2 class="cta-title animate__animated animate__fadeIn">استكشفي مجموعتنا المتنوعة</h2>
            <p class="cta-text animate__animated animate__fadeIn animate__delay-1s">نقدم لكِ مجموعة متنوعة من العبايات والأقمشة والإكسسوارات والمخور بأعلى جودة وأفضل الأسعار</p>
            <div class="cta-buttons animate__animated animate__fadeInUp animate__delay-1s">
                <a asp-controller="Products" asp-action="Index" class="btn btn-accent btn-lg">تسوق الآن</a>
            </div>
        </div>
    </div>
</section>
