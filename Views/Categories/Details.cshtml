@model Abayat.Models.Category

@{
    ViewData["Title"] = Model.Name;
}

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <h1>@Model.Name</h1>

            @if (!string.IsNullOrEmpty(Model.Description))
            {
                <p class="lead">@Model.Description</p>
            }

            <hr>

            <dl class="row">
                <dt class="col-sm-2">الاسم</dt>
                <dd class="col-sm-10">@Model.Name</dd>

                <dt class="col-sm-2">الوصف</dt>
                <dd class="col-sm-10">@Model.Description</dd>

                <dt class="col-sm-2">الرمز</dt>
                <dd class="col-sm-10">@Model.Slug</dd>

                <dt class="col-sm-2">ترتيب العرض</dt>
                <dd class="col-sm-10">@Model.DisplayOrder</dd>

                <dt class="col-sm-2">تاريخ الإنشاء</dt>
                <dd class="col-sm-10">@Model.CreatedAt.ToString("dd/MM/yyyy HH:mm")</dd>

                @if (!string.IsNullOrEmpty(Model.ImageUrl))
                {
                    <dt class="col-sm-2">الصورة</dt>
                    <dd class="col-sm-10">
                        <img src="@Model.ImageUrl" alt="@Model.Name" class="img-thumbnail" style="max-width: 200px;">
                    </dd>
                }
            </dl>

            <div class="mt-4">
                <a asp-action="Index" class="btn btn-secondary">العودة إلى القائمة</a>
                @if (User.IsInRole("Admin"))
                {
                    <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-primary">تعديل</a>
                    <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-danger">حذف</a>
                }
            </div>
        </div>
    </div>
</div>
