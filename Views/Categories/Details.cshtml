@model Abayat.Models.Category

@{
    ViewData["Title"] = "تفاصيل الفئة";
}

<div class="products-header text-center">
    <div class="container">
        <h1 class="fade-in">@Model.Name</h1>
        <p class="lead mb-4">@Model.Description</p>
        <nav aria-label="breadcrumb" class="mb-4 d-flex justify-content-center">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index" class="text-white">الرئيسية</a></li>
                <li class="breadcrumb-item"><a asp-controller="Categories" asp-action="Index" class="text-white">الفئات</a></li>
                <li class="breadcrumb-item active text-white" aria-current="page">@Model.Name</li>
            </ol>
        </nav>
    </div>
</div>

<div class="container py-5">
    <div class="row">
        <div class="col-lg-4 mb-4">
            <div class="card h-100 border-0 shadow-sm">
                @if (!string.IsNullOrEmpty(Model.ImageUrl))
                {
                    <img src="@Model.ImageUrl" class="card-img-top" alt="@Model.Name" style="height: 250px; object-fit: cover;">
                }
                else
                {
                    <img src="https://placehold.co/600x400/6a0dad/ffffff?text=@Model.Name" class="card-img-top" alt="@Model.Name" style="height: 250px; object-fit: cover;">
                }
                <div class="card-body">
                    <h3 class="card-title">@Model.Name</h3>
                    <p class="card-text">@Model.Description</p>
                    <div class="d-flex align-items-center mb-3">
                        <i class="bi bi-tag text-muted me-2"></i>
                        <span class="text-muted">الرمز: @Model.Slug</span>
                    </div>
                    <div class="d-flex align-items-center mb-3">
                        <i class="bi bi-sort-numeric-down text-muted me-2"></i>
                        <span class="text-muted">ترتيب العرض: @Model.DisplayOrder</span>
                    </div>
                    <div class="d-flex align-items-center">
                        <i class="bi bi-calendar3 text-muted me-2"></i>
                        <span class="text-muted">تاريخ الإضافة: @Model.CreatedAt.ToString("dd/MM/yyyy")</span>
                    </div>
                </div>
                <div class="card-footer bg-transparent border-top-0">
                    <div class="d-flex justify-content-between">
                        <a asp-action="Index" class="btn btn-outline-primary">
                            <i class="bi bi-arrow-right me-1"></i> العودة إلى القائمة
                        </a>
                        @if (User.Identity.IsAuthenticated)
                        {
                            <div>
                                <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-outline-secondary">
                                    <i class="bi bi-pencil me-1"></i> تعديل
                                </a>
                                <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-outline-danger">
                                    <i class="bi bi-trash me-1"></i> حذف
                                </a>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">منتجات الفئة</h3>
                </div>
                <div class="card-body">
                    @if (Model.Products != null && Model.Products.Any())
                    {
                        <div class="row">
                            @foreach (var product in Model.Products)
                            {
                                <div class="col-md-6 col-lg-4 mb-4">
                                    <div class="card h-100 border-0 shadow-sm">
                                        @if (!string.IsNullOrEmpty(product.ImageUrl))
                                        {
                                            <img src="@product.ImageUrl" class="card-img-top" alt="@product.Name" style="height: 180px; object-fit: cover;">
                                        }
                                        else
                                        {
                                            <img src="https://placehold.co/600x400/6a0dad/ffffff?text=@product.Name" class="card-img-top" alt="@product.Name" style="height: 180px; object-fit: cover;">
                                        }
                                        <div class="card-body">
                                            <h5 class="card-title">@product.Name</h5>
                                            <p class="card-text text-truncate">@product.Description</p>
                                            <p class="card-text fw-bold text-primary">@product.Price.ToString("C")</p>
                                        </div>
                                        <div class="card-footer bg-transparent">
                                            <a asp-controller="Products" asp-action="Details" asp-route-id="@product.Id" class="btn btn-sm btn-outline-primary w-100">
                                                <i class="bi bi-eye me-1"></i> التفاصيل
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            لا توجد منتجات في هذه الفئة حالياً.
                        </div>
                    }
                </div>
                <div class="card-footer bg-transparent">
                    <a asp-controller="Products" asp-action="Index" asp-route-category="@Model.Slug" class="btn btn-primary">
                        <i class="bi bi-grid me-1"></i> عرض كل المنتجات في هذه الفئة
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
