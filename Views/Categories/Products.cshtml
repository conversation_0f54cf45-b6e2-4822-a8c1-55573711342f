@model Abayat.Models.Category

@{
    ViewData["Title"] = "منتجات " + Model.Name;
}

@* Token antifalsificación para solicitudes AJAX *@
@Html.AntiForgeryToken()

@section Styles {
    <link rel="stylesheet" href="~/css/product-display.css" asp-append-version="true" />
}

<div class="products-header text-center">
    <div class="container">
        <h1 class="fade-in">منتجات @Model.Name</h1>
        <p class="lead mb-4">@Model.Description</p>
        <nav aria-label="breadcrumb" class="mb-4 d-flex justify-content-center">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index" class="text-white">الرئيسية</a></li>
                <li class="breadcrumb-item"><a asp-controller="Categories" asp-action="Index" class="text-white">الفئات</a></li>
                <li class="breadcrumb-item active text-white" aria-current="page">@Model.Name</li>
            </ol>
        </nav>
    </div>
</div>

<div class="container py-5">
    @if (Model.Products != null && Model.Products.Any())
    {
        <div class="products-grid">
            @foreach (var item in Model.Products)
            {
                <div class="product-item animate__animated animate__fadeInUp">
                    <div class="product-item-img-container">
                        @if (!string.IsNullOrEmpty(item.ImageUrl))
                        {
                            <img src="@item.ImageUrl" class="product-item-img" alt="@item.Name">
                        }
                        else
                        {
                            <img src="https://placehold.co/600x400/6a0dad/ffffff?text=@item.Name" class="product-item-img" alt="@item.Name">
                        }

                        <div class="product-item-discount">-@item.DiscountPercentage%</div>

                        <div class="product-item-availability @(item.IsAvailable ? "available" : "not-available")">
                            @(item.IsAvailable ? "متوفر" : "غير متوفر")
                        </div>

                        <!-- زر المفضلة في الزاوية العلوية -->
                        <button class="add-to-wishlist-btn" data-product-id="@item.Id" title="إضافة للمفضلة">
                            <i class="bi bi-heart"></i>
                        </button>

                        <div class="product-item-actions">
                            <button class="product-item-action-btn add-to-cart-btn" data-product-id="@item.Id" title="إضافة للسلة">
                                <i class="bi bi-cart-plus"></i>
                            </button>
                            <button class="product-item-action-btn add-to-compare-btn" data-product-id="@item.Id" title="إضافة للمقارنة">
                                <i class="bi bi-arrow-left-right"></i>
                            </button>
                        </div>

                        @if (User.IsInRole("Admin"))
                        {
                            <div class="product-actions-menu">
                                <a asp-controller="Products" asp-action="Edit" asp-route-id="@item.Id" class="product-action-btn" title="تعديل">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <a asp-controller="Products" asp-action="Delete" asp-route-id="@item.Id" class="product-action-btn" title="حذف">
                                    <i class="bi bi-trash"></i>
                                </a>
                            </div>
                        }
                    </div>
                    <div class="product-item-info">
                        <h3 class="product-item-title">@item.Name</h3>
                        <div class="product-item-price">
                            <div class="product-item-price-current">@item.DiscountedPrice.ToString("N0") ر.ع</div>
                            <div class="product-item-price-original">@item.Price.ToString("N0") ر.ع</div>
                        </div>
                    </div>
                    <a href="/Products/Details/@item.Id" class="product-link"></a>
                </div>
            }
        </div>
    }
    else
    {
        <div class="alert alert-info">
            <div class="d-flex align-items-center">
                <i class="bi bi-info-circle-fill fs-3 me-3"></i>
                <div>
                    <h5 class="alert-heading mb-1">لا توجد منتجات</h5>
                    <p class="mb-0">لا توجد منتجات في هذه الفئة حالياً.</p>
                </div>
            </div>
        </div>

        @if (User.Identity.IsAuthenticated)
        {
            <div class="text-center mt-4">
                <a asp-controller="Products" asp-action="Create" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i> إضافة منتج جديد
                </a>
            </div>
        }
    }

    <div class="text-center mt-5">
        <a asp-action="Index" class="btn btn-outline-primary">
            <i class="bi bi-arrow-right me-1"></i> العودة إلى قائمة الفئات
        </a>
    </div>
</div>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // تهيئة أزرار المفضلة والمقارنة والسلة
            // كل وظيفة معرفة في هذا الملف
            initWishlistButtons();
            initCompareButtons();
            initCartButtons();
        });

        // Inicializar botones de favoritos
        function initWishlistButtons() {
            const wishlistButtons = document.querySelectorAll('.wishlist-btn, .add-to-wishlist-btn');

            // Cargar favoritos guardados
            let wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];

            // Marcar botones de productos que ya están en favoritos
            wishlistButtons.forEach(button => {
                const productId = button.getAttribute('data-product-id');
                if (wishlist.includes(productId)) {
                    button.classList.add('active');
                }
            });

            // Añadir evento de clic a los botones
            wishlistButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const productId = this.getAttribute('data-product-id');
                    const productCard = this.closest('.product-card');

                    // Actualizar todos los botones de favoritos para este producto
                    const allWishlistButtons = productCard.querySelectorAll('.wishlist-btn, .add-to-wishlist-btn');

                    // Alternar estado activo en todos los botones
                    allWishlistButtons.forEach(btn => {
                        btn.classList.toggle('active');
                    });

                    // Actualizar lista de favoritos
                    if (this.classList.contains('active')) {
                        // Añadir a favoritos si no existe
                        if (!wishlist.includes(productId)) {
                            wishlist.push(productId);
                            showToast('تمت إضافة المنتج إلى المفضلة');
                        }
                    } else {
                        // Eliminar de favoritos
                        wishlist = wishlist.filter(id => id !== productId);
                        showToast('تمت إزالة المنتج من المفضلة');
                    }

                    // Guardar en localStorage
                    localStorage.setItem('wishlist', JSON.stringify(wishlist));

                    // إطلاق حدث تحديث المفضلة
                    $(document).trigger('wishlist:updated');
                });
            });
        }

        // Inicializar botones de comparación
        function initCompareButtons() {
            const compareButtons = document.querySelectorAll('.add-to-compare-btn');

            // Cargar productos en comparación
            let compareList = JSON.parse(localStorage.getItem('compareList')) || [];

            // Marcar botones de productos que ya están en comparación
            compareButtons.forEach(button => {
                const productId = button.getAttribute('data-product-id');
                const productCard = button.closest('.product-card');

                if (compareList.includes(productId)) {
                    button.classList.add('active');
                    if (productCard) {
                        productCard.classList.add('in-compare');
                    }
                }
            });

            // Añadir evento de clic a los botones
            compareButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const productId = this.getAttribute('data-product-id');
                    const productCard = this.closest('.product-card');

                    // Alternar estado activo
                    this.classList.toggle('active');
                    if (productCard) {
                        productCard.classList.toggle('in-compare');
                    }

                    // Actualizar lista de comparación
                    if (this.classList.contains('active')) {
                        // Limitar a 4 productos en comparación
                        if (compareList.length >= 4) {
                            // Eliminar el primer producto de la lista
                            const removedId = compareList.shift();
                            // Actualizar UI para el producto eliminado
                            const removedButton = document.querySelector(`.add-to-compare-btn[data-product-id="${removedId}"]`);
                            if (removedButton) {
                                removedButton.classList.remove('active');
                                const removedCard = removedButton.closest('.product-card');
                                if (removedCard) {
                                    removedCard.classList.remove('in-compare');
                                }
                            }
                        }

                        // Añadir a comparación si no existe
                        if (!compareList.includes(productId)) {
                            compareList.push(productId);
                            showToast('تمت إضافة المنتج إلى المقارنة');
                        }
                    } else {
                        // Eliminar de comparación
                        compareList = compareList.filter(id => id !== productId);
                        showToast('تمت إزالة المنتج من المقارنة');
                    }

                    // Guardar en localStorage
                    localStorage.setItem('compareList', JSON.stringify(compareList));
                });
            });
        }

        // ملاحظة: تم نقل دوال السلة إلى ملف product-actions.js لتجنب التكرار

        function showToast(message) {
            // Crear elemento toast si no existe
            let toast = document.getElementById('toast-notification');
            if (!toast) {
                toast = document.createElement('div');
                toast.id = 'toast-notification';
                toast.style.position = 'fixed';
                toast.style.bottom = '20px';
                toast.style.right = '20px';
                toast.style.backgroundColor = 'rgba(106, 13, 173, 0.9)';
                toast.style.color = 'white';
                toast.style.padding = '12px 20px';
                toast.style.borderRadius = '4px';
                toast.style.zIndex = '1000';
                toast.style.transition = 'opacity 0.5s ease-in-out';
                toast.style.opacity = '0';
                toast.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
                document.body.appendChild(toast);
            }

            // Mostrar mensaje
            toast.textContent = message;
            toast.style.opacity = '1';

            // Ocultar después de 3 segundos
            setTimeout(() => {
                toast.style.opacity = '0';
            }, 3000);
        }
    </script>
}
