@model Abayat.Models.Category

@{
    ViewData["Title"] = "حذف الفئة";
}

<div class="products-header text-center">
    <div class="container">
        <h1 class="fade-in">حذف الفئة</h1>
        <p class="lead mb-4">تأكيد حذف الفئة "@Model.Name"</p>
    </div>
</div>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="form-card zoom-in">
                <div class="alert alert-danger mb-4">
                    <div class="d-flex align-items-center">
                        <i class="bi bi-exclamation-triangle-fill fs-3 me-3"></i>
                        <div>
                            <h5 class="alert-heading mb-1">تحذير!</h5>
                            <p class="mb-0">هل أنت متأكد من رغبتك في حذف هذه الفئة؟ سيتم حذف جميع المنتجات المرتبطة بها أيضاً. لا يمكن التراجع عن هذه العملية.</p>
                        </div>
                    </div>
                </div>
                
                <div class="product-details mb-4">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="position-relative overflow-hidden rounded">
                                @if (!string.IsNullOrEmpty(Model.ImageUrl))
                                {
                                    <img src="@Model.ImageUrl" alt="@Model.Name" class="img-fluid" style="height: 200px; width: 100%; object-fit: cover;" />
                                }
                                else
                                {
                                    <img src="https://placehold.co/600x400/6a0dad/ffffff?text=@Model.Name" class="img-fluid" alt="@Model.Name" style="height: 200px; width: 100%; object-fit: cover;">
                                }
                                
                                <div class="position-absolute top-0 start-0 w-100 h-100 bg-danger" style="opacity: 0.2;"></div>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <h3 class="product-details-title mb-2">@Model.Name</h3>
                            
                            <p class="product-description">@Model.Description</p>
                            
                            <div class="d-flex align-items-center mb-2">
                                <i class="bi bi-tag text-muted me-2"></i>
                                <span class="text-muted">الرمز: @Model.Slug</span>
                            </div>
                            
                            <div class="d-flex align-items-center mb-2">
                                <i class="bi bi-sort-numeric-down text-muted me-2"></i>
                                <span class="text-muted">ترتيب العرض: @Model.DisplayOrder</span>
                            </div>
                            
                            <div class="d-flex align-items-center">
                                <i class="bi bi-calendar3 text-muted me-2"></i>
                                <span class="text-muted">تاريخ الإضافة: @Model.CreatedAt.ToString("dd/MM/yyyy")</span>
                            </div>
                            
                            @if (Model.Products != null)
                            {
                                <div class="alert alert-warning mt-3">
                                    <i class="bi bi-exclamation-circle me-2"></i>
                                    سيتم حذف @Model.Products.Count منتج مرتبط بهذه الفئة.
                                </div>
                            }
                        </div>
                    </div>
                </div>
                
                <form asp-action="Delete">
                    <input type="hidden" asp-for="Id" />
                    <div class="form-actions">
                        <a asp-action="Index" class="btn btn-outline-secondary form-btn">
                            <i class="bi bi-arrow-right"></i> العودة إلى القائمة
                        </a>
                        <button type="submit" class="btn btn-danger form-btn">
                            <i class="bi bi-trash"></i> تأكيد الحذف
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
