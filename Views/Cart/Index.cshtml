@model Abayat.Models.ShoppingCart

@{
    ViewData["Title"] = "سلة المشتريات";
}

<div class="products-header text-center">
    <div class="container">
        <h1 class="fade-in">@ViewData["Title"]</h1>
        <p class="lead mb-4">مراجعة المنتجات في سلة المشتريات</p>
    </div>
</div>

<div class="container py-5">
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i> @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i> @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <div class="form-card slide-in-right">
        @if (Model.CartItems != null && Model.CartItems.Any())
        {
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>المنتج</th>
                            <th>السعر</th>
                            <th>الكمية</th>
                            <th>المجموع</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in Model.CartItems)
                        {
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        @if (!string.IsNullOrEmpty(item.Product?.ImageUrl))
                                        {
                                            <img src="@item.Product.ImageUrl" alt="@item.Product.Name" class="cart-item-img me-3">
                                        }
                                        else
                                        {
                                            <img src="https://placehold.co/100x100/6a0dad/ffffff?text=@item.Product?.Name" alt="@item.Product?.Name" class="cart-item-img me-3">
                                        }
                                        <div>
                                            <h5 class="mb-0">@item.Product?.Name</h5>
                                            <small class="text-muted">@item.Product?.Category?.Name</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    @if (item.Product?.DiscountPercentage > 0)
                                    {
                                        <div class="cart-price-with-discount">
                                            <span class="cart-original-price">ر.ع @item.Product.Price.ToString("N0")</span>
                                            <span class="cart-discounted-price">ر.ع @item.Product.DiscountedPrice.ToString("N0")</span>
                                        </div>
                                    }
                                    else
                                    {
                                        <span class="currency">ر.ع</span> @item.Product?.Price.ToString("N0")
                                    }
                                </td>
                                <td>
                                    <form asp-action="UpdateQuantity" method="post" class="d-flex align-items-center">
                                        <input type="hidden" name="cartItemId" value="@item.Id" />
                                        <input type="hidden" name="productId" value="@item.ProductId" />
                                        <div class="d-flex align-items-center">
                                            <button type="button" class="btn btn-outline-primary rounded-circle quantity-btn" data-action="decrease">
                                                <i class="bi bi-dash"></i>
                                            </button>
                                            <input type="number" name="quantity" value="@item.Quantity" min="1" max="100" class="form-control text-center quantity-input mx-2" style="width: 60px;" readonly />
                                            <button type="button" class="btn btn-outline-primary rounded-circle quantity-btn" data-action="increase">
                                                <i class="bi bi-plus"></i>
                                            </button>
                                        </div>
                                    </form>
                                </td>
                                <td><span class="currency">ر.ع</span> @item.TotalPrice.ToString("N0")</td>
                                <td>
                                    <form asp-action="RemoveItem" method="post">
                                        <input type="hidden" name="cartItemId" value="@item.Id" />
                                        <input type="hidden" name="productId" value="@item.ProductId" />
                                        <button type="submit" class="btn btn-sm btn-outline-danger">
                                            <i class="bi bi-trash"></i> حذف
                                        </button>
                                    </form>
                                </td>
                            </tr>
                        }
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="3" class="text-end fw-bold">المجموع الكلي:</td>
                            <td class="fw-bold"><span class="currency">ر.ع</span> @Model.TotalAmount.ToString("N0")</td>
                            <td></td>
                        </tr>
                    </tfoot>
                </table>
            </div>

            <div class="d-flex justify-content-between mt-4">
                <form asp-action="ClearCart" method="post">
                    <button type="submit" class="btn btn-outline-danger">
                        <i class="bi bi-trash me-2"></i> تفريغ السلة
                    </button>
                </form>
                <div>
                    <a asp-controller="Products" asp-action="Index" class="btn btn-outline-secondary me-2">
                        <i class="bi bi-arrow-left me-2"></i> مواصلة التسوق
                    </a>
                    <a asp-action="Checkout" class="btn btn-primary">
                        <i class="bi bi-credit-card me-2"></i> إتمام الطلب
                    </a>
                </div>
            </div>
        }
        else
        {
            <div class="text-center py-5">
                <i class="bi bi-cart-x display-1 text-muted mb-3"></i>
                <h3>سلة المشتريات فارغة</h3>
                <p class="text-muted mb-4">لم تقم بإضافة أي منتجات إلى سلة المشتريات بعد.</p>
                <a asp-controller="Products" asp-action="Index" class="btn btn-primary">
                    <i class="bi bi-bag me-2"></i> تصفح المنتجات
                </a>
            </div>
        }
    </div>
</div>

@section Styles {
    <style>
        .cart-item-img {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 8px;
        }

        .quantity-control {
            max-width: 120px;
        }

        .quantity-input {
            text-align: center;
        }

        .cart-price-with-discount {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .cart-original-price {
            font-size: 0.9rem;
            color: #999;
            text-decoration: line-through;
        }

        .cart-discounted-price {
            font-size: 1.1rem;
            color: #6a0dad;
            font-weight: 600;
        }
    </style>
}

@section Scripts {
    <script>
        $(document).ready(function () {
            // تحديث عداد السلة والمبلغ الإجمالي عند تحميل الصفحة
            if (typeof updateCartCount === 'function') {
                updateCartCount();
            }
            if (typeof updateCartTotal === 'function') {
                updateCartTotal();
            }

            // زيادة ونقصان الكمية
            $('.quantity-btn').on('click', function () {
                var action = $(this).data('action');
                var form = $(this).closest('form');
                var input = form.find('.quantity-input');
                var currentValue = parseInt(input.val());

                if (action === 'increase') {
                    if (currentValue < 100) {
                        input.val(currentValue + 1);
                        // تقديم النموذج تلقائياً بعد تغيير الكمية
                        form.submit();
                    }
                } else if (action === 'decrease') {
                    if (currentValue > 1) {
                        input.val(currentValue - 1);
                        // تقديم النموذج تلقائياً بعد تغيير الكمية
                        form.submit();
                    }
                }
            });

            // تحديث العداد عند حذف منتج
            $('.btn-danger').on('click', function() {
                // تأخير قصير للسماح للحذف بالاكتمال
                setTimeout(function() {
                    if (typeof updateCartCount === 'function') {
                        updateCartCount();
                    }
                }, 500);
            });

            // تحديث العداد عند تغيير الكمية
            $('form[action*="UpdateQuantity"]').on('submit', function() {
                setTimeout(function() {
                    if (typeof updateCartCount === 'function') {
                        updateCartCount();
                    }
                }, 500);
            });
        });
    </script>
}
