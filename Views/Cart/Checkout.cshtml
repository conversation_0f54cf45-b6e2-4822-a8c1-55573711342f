@model Abayat.ViewModels.CheckoutViewModel

@{
    ViewData["Title"] = "إتمام الطلب";
}

<div class="products-header text-center">
    <div class="container">
        <h1 class="fade-in">@ViewData["Title"]</h1>
        <p class="lead mb-4">أدخل بيانات الشحن لإتمام الطلب</p>
    </div>
</div>

<div class="container py-5">
    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i> @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <div class="row">
        <div class="col-lg-8">
            <div class="form-card slide-in-right">
                <h2 class="form-title">بيانات الشحن</h2>

                <form action="/Orders/Create" method="post" id="checkoutForm" enctype="multipart/form-data">
                    @Html.AntiForgeryToken()
                    <div asp-validation-summary="ModelOnly" class="validation-summary-errors"></div>

                    <div class="mb-3">
                        <label asp-for="FullName" class="form-label">الاسم الكامل</label>
                        <input asp-for="FullName" class="form-control" required />
                        <span asp-validation-for="FullName" class="field-validation-error"></span>
                    </div>

                    <div class="mb-3">
                        <label asp-for="PhoneNumber" class="form-label">رقم الهاتف</label>
                        <input asp-for="PhoneNumber" class="form-control" required />
                        <span asp-validation-for="PhoneNumber" class="field-validation-error"></span>
                    </div>

                    <div class="mb-3">
                        <label asp-for="Address" class="form-label">العنوان</label>
                        <textarea asp-for="Address" class="form-control" rows="3" required></textarea>
                        <span asp-validation-for="Address" class="field-validation-error"></span>
                    </div>

                    <div class="mb-3">
                        <label asp-for="Notes" class="form-label">ملاحظات إضافية (اختياري)</label>
                        <textarea asp-for="Notes" class="form-control" rows="3"></textarea>
                        <span asp-validation-for="Notes" class="field-validation-error"></span>
                    </div>

                    <!-- قسم طرق الدفع -->
                    <div class="mb-4">
                        <h4 class="form-subtitle mb-3">طريقة الدفع</h4>

                        <div class="payment-methods">
                            <!-- الدفع عند الاستلام -->
                            <div class="payment-option mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" asp-for="PaymentMethod" value="0" id="cashOnDelivery" checked>
                                    <label class="form-check-label" for="cashOnDelivery">
                                        <i class="bi bi-cash-coin me-2"></i>
                                        الدفع عند الاستلام
                                    </label>
                                </div>
                                <small class="text-muted d-block mt-1 me-4">ادفع نقداً عند استلام الطلب</small>
                            </div>

                            <!-- تحويل عبر رقم الهاتف -->
                            <div class="payment-option mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" asp-for="PaymentMethod" value="1" id="phoneTransfer">
                                    <label class="form-check-label" for="phoneTransfer">
                                        <i class="bi bi-phone me-2"></i>
                                        تحويل عبر رقم الهاتف
                                    </label>
                                </div>
                                <small class="text-muted d-block mt-1 me-4">حول المبلغ عبر رقم الهاتف وأرسل وصل التحويل</small>

                                <!-- تفاصيل التحويل عبر الهاتف -->
                                <div class="phone-transfer-details mt-3 d-none">
                                    <div class="alert alert-info">
                                        <strong>معلومات التحويل:</strong><br>
                                        رقم الهاتف: <strong>+966 50 123 4567</strong><br>
                                        الاسم: <strong>متجر العبايات</strong>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label asp-for="PaymentPhoneNumber" class="form-label">رقم هاتفك المستخدم في التحويل</label>
                                            <input asp-for="PaymentPhoneNumber" class="form-control" placeholder="مثال: 0501234567">
                                            <span asp-validation-for="PaymentPhoneNumber" class="field-validation-error"></span>
                                        </div>
                                        <div class="col-md-6 mb-3">
                                            <label asp-for="PaymentReceipt" class="form-label">وصل التحويل</label>
                                            <input asp-for="PaymentReceipt" type="file" class="form-control" accept="image/*,.pdf">
                                            <span asp-validation-for="PaymentReceipt" class="field-validation-error"></span>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label asp-for="PaymentNotes" class="form-label">ملاحظات الدفع (اختياري)</label>
                                        <textarea asp-for="PaymentNotes" class="form-control" rows="2" placeholder="أي ملاحظات إضافية حول عملية التحويل"></textarea>
                                        <span asp-validation-for="PaymentNotes" class="field-validation-error"></span>
                                    </div>
                                </div>
                            </div>

                            <!-- تحويل بنكي (غير متوفر) -->
                            <div class="payment-option mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" asp-for="PaymentMethod" value="2" id="bankTransfer" disabled>
                                    <label class="form-check-label text-muted" for="bankTransfer">
                                        <i class="bi bi-bank me-2"></i>
                                        تحويل بنكي (غير متوفر حالياً)
                                    </label>
                                </div>
                            </div>

                            <!-- بطاقة الخصم (غير متوفر) -->
                            <div class="payment-option mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" asp-for="PaymentMethod" value="3" id="debitCard" disabled>
                                    <label class="form-check-label text-muted" for="debitCard">
                                        <i class="bi bi-credit-card me-2"></i>
                                        بطاقة الخصم (غير متوفر حالياً)
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary" id="confirmOrderBtn">
                            <i class="bi bi-check-circle me-2"></i> تأكيد الطلب
                        </button>
                        <a href="/Cart" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-right me-2"></i> العودة للسلة
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="form-card slide-in-left">
                <h2 class="form-title">ملخص الطلب</h2>

                <div id="order-summary">
                    <!-- سيتم تحميل ملخص الطلب هنا -->
                    <div class="text-center py-3">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <style>
        .payment-methods {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            background-color: #f9f9f9;
        }

        .payment-option {
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
            background-color: white;
            transition: all 0.3s ease;
        }

        .payment-option:hover {
            border-color: #007bff;
            box-shadow: 0 2px 4px rgba(0,123,255,0.1);
        }

        .payment-option .form-check-input:checked + .form-check-label {
            color: #007bff;
            font-weight: 500;
        }

        .payment-option .form-check-input:disabled + .form-check-label {
            opacity: 0.6;
        }

        .phone-transfer-details {
            margin-top: 15px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #dee2e6;
        }

        .form-subtitle {
            color: #333;
            font-weight: 600;
            border-bottom: 2px solid #007bff;
            padding-bottom: 8px;
            margin-bottom: 20px;
        }

        .alert-info {
            background-color: #e3f2fd;
            border-color: #2196f3;
            color: #1976d2;
        }
    </style>
}

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        $(document).ready(function () {
            // تحميل ملخص الطلب
            $.ajax({
                url: '/Cart/GetCartItemsCount',
                type: 'GET',
                success: function (data) {
                    if (data.count === 0) {
                        window.location.href = '/Cart';
                    } else {
                        loadOrderSummary();
                    }
                }
            });

            function loadOrderSummary() {
                $.ajax({
                    url: '/Cart/GetOrderSummary',
                    type: 'GET',
                    success: function (data) {
                        $('#order-summary').html(data);
                    },
                    error: function () {
                        $('#order-summary').html('<div class="alert alert-warning">حدث خطأ في تحميل ملخص الطلب</div>');
                    }
                });
            }

            // التحكم في طرق الدفع
            $('input[name="PaymentMethod"]').change(function () {
                var selectedMethod = $(this).val();

                // إخفاء جميع تفاصيل طرق الدفع
                $('.phone-transfer-details').addClass('d-none');

                // إظهار التفاصيل المناسبة
                if (selectedMethod === '1') { // تحويل عبر الهاتف
                    $('.phone-transfer-details').removeClass('d-none');
                    // جعل الحقول مطلوبة
                    $('input[name="PaymentPhoneNumber"]').attr('required', true);
                    $('input[name="PaymentReceipt"]').attr('required', true);
                } else {
                    // إزالة الحقول المطلوبة
                    $('input[name="PaymentPhoneNumber"]').removeAttr('required');
                    $('input[name="PaymentReceipt"]').removeAttr('required');
                }
            });

            // التحقق من صحة النموذج قبل الإرسال
            $('#checkoutForm').submit(function (e) {
                var selectedMethod = $('input[name="PaymentMethod"]:checked').val();
                console.log('Form submission - Payment method:', selectedMethod);

                if (selectedMethod === '1') { // تحويل عبر الهاتف
                    var phoneNumber = $('input[name="PaymentPhoneNumber"]').val();
                    var receiptFiles = $('input[name="PaymentReceipt"]')[0].files;
                    var receipt = receiptFiles.length;

                    console.log('Phone number:', phoneNumber);
                    console.log('Receipt files count:', receipt);
                    if (receipt > 0) {
                        console.log('Receipt file name:', receiptFiles[0].name);
                        console.log('Receipt file size:', receiptFiles[0].size);
                    }

                    if (!phoneNumber || phoneNumber.trim() === '') {
                        e.preventDefault();
                        alert('يرجى إدخال رقم الهاتف المستخدم في التحويل');
                        return false;
                    }

                    if (receipt === 0) {
                        e.preventDefault();
                        alert('يرجى رفع وصل التحويل');
                        return false;
                    }
                }
            });
        });
    </script>
}
