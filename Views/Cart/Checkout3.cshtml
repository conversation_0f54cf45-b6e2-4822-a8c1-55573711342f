@model Abayat.ViewModels.CheckoutViewModel

@{
    ViewData["Title"] = "إتمام الطلب";
}

<div class="container py-4">
    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i> @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <div class="row">
        <div class="col-md-8">
            <div class="checkout-section">
                <h2 class="section-title">بيانات الشحن</h2>
                <form method="post" action="/Orders/Create" id="checkoutForm">
                    @Html.AntiForgeryToken()

                    <div class="form-group mb-3">
                        <label for="FullName">الاسم الكامل</label>
                        <input type="text" class="form-control" id="FullName" name="FullName" value="@Model.FullName" required>
                    </div>

                    <div class="form-group mb-3">
                        <label for="PhoneNumber">رقم الهاتف</label>
                        <input type="tel" class="form-control" id="PhoneNumber" name="PhoneNumber" value="@Model.PhoneNumber" required>
                    </div>

                    <div class="form-group mb-3">
                        <label for="Address">العنوان</label>
                        <textarea class="form-control" id="Address" name="Address" rows="3" required>@Model.Address</textarea>
                    </div>

                    <div class="form-group mb-3">
                        <label for="Notes">ملاحظات إضافية (اختياري)</label>
                        <textarea class="form-control" id="Notes" name="Notes" rows="3">@Model.Notes</textarea>
                    </div>

                    <div class="d-flex justify-content-between mt-4">
                        <a href="/Cart" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-right me-2"></i> العودة للسلة
                        </a>
                        <button type="submit" class="btn btn-purple" id="confirmOrderBtn">
                            تأكيد الطلب <i class="bi bi-check-circle ms-2"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="col-md-4">
            <div class="order-summary-section">
                <h2 class="section-title">ملخص الطلب</h2>
                <div id="order-summary" class="order-items">
                    <!-- سيتم تحميل ملخص الطلب هنا -->
                    <div class="text-center py-3">
                        <div class="spinner-border text-purple" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .section-title {
        color: #6a0dad;
        border-bottom: 2px solid rgba(106, 13, 173, 0.1);
        padding-bottom: 10px;
        margin-bottom: 20px;
        font-size: 1.5rem;
        font-weight: bold;
    }
    
    .checkout-section, .order-summary-section {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .order-items {
        max-height: 400px;
        overflow-y: auto;
    }
    
    .form-control:focus {
        border-color: #6a0dad;
        box-shadow: 0 0 0 0.25rem rgba(106, 13, 173, 0.25);
    }
    
    .btn-purple {
        background-color: #6a0dad;
        border-color: #6a0dad;
        color: white;
    }
    
    .btn-purple:hover {
        background-color: #5a0c93;
        border-color: #5a0c93;
        color: white;
    }
</style>

@section Scripts {
    <script>
        $(document).ready(function () {
            // تحميل ملخص الطلب
            $.ajax({
                url: '/Cart/GetCartItemsCount',
                type: 'GET',
                success: function (data) {
                    if (data.count === 0) {
                        window.location.href = '/Cart';
                    } else {
                        loadOrderSummary();
                    }
                }
            });

            function loadOrderSummary() {
                $.ajax({
                    url: '/Cart/GetOrderSummary',
                    type: 'GET',
                    success: function (data) {
                        $('#order-summary').html(data);
                    },
                    error: function () {
                        $('#order-summary').html('<div class="alert alert-warning">حدث خطأ في تحميل ملخص الطلب</div>');
                    }
                });
            }
            
            // إضافة معالج النموذج
            $('#checkoutForm').on('submit', function() {
                $('#confirmOrderBtn').prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> جاري التنفيذ...');
                return true;
            });
        });
    </script>
}
