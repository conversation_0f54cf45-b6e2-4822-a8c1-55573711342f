@model Abayat.Models.ShoppingCart

@if (Model.CartItems != null && Model.CartItems.Any())
{
    <div class="order-summary-items">
        @foreach (var item in Model.CartItems)
        {
            <div class="order-summary-item">
                <div class="item-info">
                    <div class="item-image">
                        @if (!string.IsNullOrEmpty(item.Product?.ImageUrl))
                        {
                            <img src="@item.Product.ImageUrl" alt="@item.Product.Name" class="summary-item-img">
                        }
                        else
                        {
                            <img src="https://placehold.co/60x60/6a0dad/ffffff?text=@item.Product?.Name" alt="@item.Product?.Name" class="summary-item-img">
                        }
                    </div>
                    <div class="item-details">
                        <h6 class="item-name">@item.Product?.Name</h6>
                        <div class="item-quantity">الكمية: @item.Quantity</div>
                        <div class="item-price">
                            @if (item.Product?.DiscountPercentage > 0)
                            {
                                <span class="summary-original-price">@item.Product.Price.ToString("N0") ر.ع</span>
                                <span class="summary-discounted-price">@item.Product.DiscountedPrice.ToString("N0") ر.ع</span>
                            }
                            else
                            {
                                <span class="summary-price">@item.Product?.Price.ToString("N0") ر.ع</span>
                            }
                        </div>
                    </div>
                </div>
                <div class="item-total">
                    @item.TotalPrice.ToString("N0") ر.ع
                </div>
            </div>
        }
    </div>
    
    <div class="order-summary-total">
        <div class="summary-row">
            <span>المجموع الفرعي:</span>
            <span>@Model.TotalAmount.ToString("N0") ر.ع</span>
        </div>
        <div class="summary-row">
            <span>رسوم التوصيل:</span>
            <span>مجاني</span>
        </div>
        <div class="summary-row total-row">
            <span>المجموع الكلي:</span>
            <span>@Model.TotalAmount.ToString("N0") ر.ع</span>
        </div>
    </div>
}
else
{
    <div class="empty-cart-summary">
        <i class="bi bi-cart-x"></i>
        <p>لا توجد منتجات في السلة</p>
    </div>
}

<style>
.order-summary-items {
    max-height: 400px;
    overflow-y: auto;
    margin-bottom: 20px;
}

.order-summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #eee;
}

.order-summary-item:last-child {
    border-bottom: none;
}

.item-info {
    display: flex;
    align-items: center;
    flex: 1;
}

.summary-item-img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
    margin-left: 15px;
}

.item-details {
    flex: 1;
}

.item-name {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 5px;
    color: #333;
    line-height: 1.3;
}

.item-quantity {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.item-price {
    font-size: 13px;
}

.summary-original-price {
    color: #999;
    text-decoration: line-through;
    font-size: 11px;
    margin-left: 8px;
}

.summary-discounted-price {
    color: #6a0dad;
    font-weight: 600;
}

.summary-price {
    color: #6a0dad;
    font-weight: 600;
}

.item-total {
    font-weight: 700;
    color: #6a0dad;
    font-size: 14px;
    text-align: left;
    min-width: 80px;
}

.order-summary-total {
    border-top: 2px solid #6a0dad;
    padding-top: 15px;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-size: 14px;
}

.summary-row:last-child {
    margin-bottom: 0;
}

.total-row {
    font-weight: 700;
    font-size: 16px;
    color: #6a0dad;
    border-top: 1px solid #eee;
    padding-top: 10px;
    margin-top: 10px;
}

.empty-cart-summary {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.empty-cart-summary i {
    font-size: 48px;
    margin-bottom: 15px;
    color: #ccc;
}

/* تحسينات للشاشات الصغيرة */
@@media (max-width: 768px) {
    .order-summary-item {
        flex-direction: column;
        align-items: flex-start;
        padding: 12px 0;
    }
    
    .item-info {
        width: 100%;
        margin-bottom: 8px;
    }
    
    .item-total {
        align-self: flex-end;
        font-size: 16px;
    }
    
    .summary-item-img {
        width: 50px;
        height: 50px;
        margin-left: 10px;
    }
    
    .item-name {
        font-size: 13px;
    }
}
</style>
