@{
    ViewData["Title"] = "إضافة صورة للشريط المتحرك";
    Layout = "_Layout";
}

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0">إضافة صورة جديدة للشريط المتحرك</h3>
                </div>
                <div class="card-body">
                    <form id="carouselForm" method="post" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="title" class="form-label">عنوان الصورة</label>
                            <input type="text" class="form-control" id="title" name="title" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label">وصف الصورة (اختياري)</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>
                        
                        <div class="mb-3">
                            <label for="linkUrl" class="form-label">رابط التوجيه (اختياري)</label>
                            <input type="url" class="form-control" id="linkUrl" name="linkUrl">
                            <div class="form-text">الرابط الذي سيتم التوجيه إليه عند النقر على الصورة</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="displayOrder" class="form-label">ترتيب العرض</label>
                            <input type="number" class="form-control" id="displayOrder" name="displayOrder" value="0" min="0">
                            <div class="form-text">الرقم الأصغر يظهر أولاً</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="imageFile" class="form-label">الصورة</label>
                            <input type="file" class="form-control" id="imageFile" name="imageFile" accept="image/*" required>
                            <div class="form-text">يفضل صورة بأبعاد 800×500 بكسل</div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="isActive" name="isActive" checked>
                            <label class="form-check-label" for="isActive">نشط</label>
                        </div>
                        
                        <div class="preview-container text-center my-3">
                            <img id="imagePreview" src="#" alt="معاينة الصورة" style="max-width: 100%; max-height: 300px; display: none;">
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-cloud-upload"></i> إضافة الصورة
                            </button>
                            <a asp-controller="CarouselImages" asp-action="Index" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-right"></i> العودة إلى القائمة
                            </a>
                        </div>
                    </form>
                    
                    <div id="successMessage" class="alert alert-success mt-3" style="display: none;">
                        <i class="bi bi-check-circle-fill"></i> تم إضافة الصورة بنجاح!
                    </div>
                    
                    <div id="errorMessage" class="alert alert-danger mt-3" style="display: none;">
                        <i class="bi bi-exclamation-triangle-fill"></i> <span id="errorText">حدث خطأ أثناء إضافة الصورة</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // معاينة الصورة قبل التحميل
            const imageFile = document.getElementById('imageFile');
            const imagePreview = document.getElementById('imagePreview');
            
            imageFile.addEventListener('change', function() {
                const file = this.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        imagePreview.src = e.target.result;
                        imagePreview.style.display = 'block';
                    }
                    reader.readAsDataURL(file);
                }
            });
            
            // معالجة تحميل النموذج
            const carouselForm = document.getElementById('carouselForm');
            const successMessage = document.getElementById('successMessage');
            const errorMessage = document.getElementById('errorMessage');
            const errorText = document.getElementById('errorText');
            
            carouselForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // إخفاء الرسائل السابقة
                successMessage.style.display = 'none';
                errorMessage.style.display = 'none';
                
                // إنشاء كائن FormData
                const formData = new FormData(carouselForm);
                
                // إرسال البيانات باستخدام Fetch API
                fetch('@Url.Action("Upload", "SimpleCarousel")', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    if (!response.ok) {
                        return response.text().then(text => {
                            throw new Error(text || 'حدث خطأ أثناء إضافة الصورة');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    // عرض رسالة النجاح
                    successMessage.style.display = 'block';
                    
                    // إعادة تعيين النموذج
                    carouselForm.reset();
                    imagePreview.style.display = 'none';
                    
                    // التمرير إلى رسالة النجاح
                    successMessage.scrollIntoView({ behavior: 'smooth' });
                    
                    // إعادة التوجيه بعد ثانيتين
                    setTimeout(function() {
                        window.location.href = '@Url.Action("Index", "CarouselImages")';
                    }, 2000);
                })
                .catch(error => {
                    // عرض رسالة الخطأ
                    errorText.textContent = error.message || 'حدث خطأ أثناء إضافة الصورة';
                    errorMessage.style.display = 'block';
                    
                    // التمرير إلى رسالة الخطأ
                    errorMessage.scrollIntoView({ behavior: 'smooth' });
                });
            });
        });
    </script>
}
