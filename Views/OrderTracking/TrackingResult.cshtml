@model Abayat.Models.Order

@{
    ViewData["Title"] = "نتيجة تتبع الطلب";

    string GetStatusBadgeClass(OrderStatus status)
    {
        return status switch
        {
            OrderStatus.Pending => "bg-warning",
            OrderStatus.Confirmed => "bg-info",
            OrderStatus.Processing => "bg-primary",
            OrderStatus.Shipped => "bg-info",
            OrderStatus.Delivered => "bg-success",
            OrderStatus.Cancelled => "bg-danger",
            _ => "bg-secondary"
        };
    }

    string GetStatusDisplayName(OrderStatus status)
    {
        return status switch
        {
            OrderStatus.Pending => "قيد الانتظار",
            OrderStatus.Confirmed => "تم التأكيد",
            OrderStatus.Processing => "قيد التجهيز",
            OrderStatus.Shipped => "تم الشحن",
            OrderStatus.Delivered => "تم التسليم",
            OrderStatus.Cancelled => "تم الإلغاء",
            _ => "غير معروف"
        };
    }
}

<div class="products-header text-center py-2">
    <div class="container">
        <h1 class="fade-in mb-1">تتبع الطلب #@Model.Id</h1>
        <p class="lead mb-0">معلومات وحالة الطلب</p>
    </div>
</div>

<div class="container py-2">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-body p-3">
                    <h3 class="card-title border-bottom pb-2 mb-2">معلومات الطلب</h3>

                    <div class="row mb-2">
                        <div class="col-md-6">
                            <div class="mb-2">
                                <h5 class="text-muted mb-1">رقم الطلب</h5>
                                <p class="fs-5 mb-1">#@Model.Id</p>
                            </div>

                            <div class="mb-2">
                                <h5 class="text-muted mb-1">تاريخ الطلب</h5>
                                <p class="fs-5 mb-1">@Model.OrderDate.ToString("yyyy/MM/dd HH:mm")</p>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-2">
                                <h5 class="text-muted mb-1">الاسم</h5>
                                <p class="fs-5 mb-1">@Model.FullName</p>
                            </div>

                            <div class="mb-2">
                                <h5 class="text-muted mb-1">رقم الهاتف</h5>
                                <p class="fs-5 mb-1">@Model.PhoneNumber</p>
                            </div>
                        </div>
                    </div>

                    <div class="mb-2">
                        <h5 class="text-muted mb-1">العنوان</h5>
                        <p class="fs-5 mb-1">@Model.Address</p>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.Notes))
                    {
                        <div class="mb-2">
                            <h5 class="text-muted mb-1">ملاحظات</h5>
                            <p class="fs-5 mb-1">@Model.Notes</p>
                        </div>
                    }

                    <div class="mb-2">
                        <h5 class="text-muted mb-1">المبلغ الإجمالي</h5>
                        <p class="fs-5 fw-bold mb-1"><span class="currency">ر.ع</span> @Model.TotalAmount.ToString("N0")</p>
                    </div>

                    <hr class="my-2">

                    <h3 class="card-title border-bottom pb-2 mb-2">حالة الطلب</h3>

                    <div class="text-center mb-2">
                        <span class="badge @GetStatusBadgeClass(Model.Status) fs-5 p-2">
                            @GetStatusDisplayName(Model.Status)
                        </span>
                    </div>

                    <div class="order-tracking-progress my-2">
                        <div class="progress-track">
                            <ul class="progressbar">
                                <li class="@(Model.Status >= OrderStatus.Pending ? "active" : "")">
                                    <i class="bi bi-clock"></i>
                                    <span>قيد الانتظار</span>
                                </li>
                                <li class="@(Model.Status >= OrderStatus.Confirmed ? "active" : "")">
                                    <i class="bi bi-check-circle"></i>
                                    <span>تم التأكيد</span>
                                </li>
                                <li class="@(Model.Status >= OrderStatus.Processing ? "active" : "")">
                                    <i class="bi bi-gear"></i>
                                    <span>قيد التجهيز</span>
                                </li>
                                <li class="@(Model.Status >= OrderStatus.Shipped ? "active" : "")">
                                    <i class="bi bi-truck"></i>
                                    <span>تم الشحن</span>
                                </li>
                                <li class="@(Model.Status >= OrderStatus.Delivered ? "active" : "")">
                                    <i class="bi bi-house-check"></i>
                                    <span>تم التسليم</span>
                                </li>
                            </ul>
                        </div>
                    </div>

                    @if (Model.Status == OrderStatus.Cancelled)
                    {
                        <div class="alert alert-danger mt-2">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            تم إلغاء هذا الطلب
                        </div>
                    }

                    <hr class="my-2">

                    <h3 class="card-title border-bottom pb-2 mb-2">منتجات الطلب</h3>

                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>المنتج</th>
                                    <th>السعر</th>
                                    <th>الكمية</th>
                                    <th>المجموع</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (Model.OrderItems != null && Model.OrderItems.Any())
                                {
                                    foreach (var item in Model.OrderItems)
                                    {
                                        <tr>
                                            <td>@item.ProductName</td>
                                            <td><span class="currency">ر.ع</span> @item.UnitPrice.ToString("N0")</td>
                                            <td>@item.Quantity</td>
                                            <td><span class="currency">ر.ع</span> @item.TotalPrice.ToString("N0")</td>
                                        </tr>
                                    }
                                }
                                else
                                {
                                    <tr>
                                        <td colspan="4" class="text-center">لا توجد منتجات في هذا الطلب</td>
                                    </tr>
                                }
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="3" class="text-end fw-bold">المجموع الكلي:</td>
                                    <td class="fw-bold"><span class="currency">ر.ع</span> @Model.TotalAmount.ToString("N0")</td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>

                    <div class="text-center mt-3">
                        <a asp-action="Index" class="btn btn-outline-primary btn-sm">
                            <i class="bi bi-arrow-right"></i> العودة إلى صفحة تتبع الطلب
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <link rel="stylesheet" href="~/css/order-tracking.css" asp-append-version="true" />
}
