@model Abayat.ViewModels.OrderTrackingViewModel

@{
    ViewData["Title"] = "تتبع الطلب";
}

<div class="products-header text-center">
    <div class="container">
        <h1 class="fade-in">تتبع الطلب</h1>
        <p class="lead mb-4">يمكنك متابعة حالة طلبك من خلال إدخال رقم الطلب أو رقم الهاتف</p>
    </div>
</div>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-body p-4">
                    <h3 class="card-title text-center mb-4">تتبع حالة الطلب</h3>

                    @if (!ViewData.ModelState.IsValid)
                    {
                        <div class="alert alert-danger">
                            <ul>
                                @foreach (var error in ViewData.ModelState.Values.SelectMany(v => v.Errors))
                                {
                                    <li>@error.ErrorMessage</li>
                                }
                            </ul>
                        </div>
                    }

                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger">
                            @TempData["ErrorMessage"]
                        </div>
                    }

                    <form asp-controller="OrderTracking" asp-action="Track" method="post" id="trackingForm">
                        @Html.AntiForgeryToken()
                        <div class="mb-3">
                            <label asp-for="OrderId" class="form-label">رقم الطلب</label>
                            <input asp-for="OrderId" class="form-control" placeholder="أدخل رقم الطلب" id="OrderId" />
                            <span asp-validation-for="OrderId" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <p class="text-center my-2">أو</p>
                        </div>

                        <div class="mb-3">
                            <label asp-for="PhoneNumber" class="form-label">رقم الهاتف</label>
                            <input asp-for="PhoneNumber" class="form-control" placeholder="أدخل رقم الهاتف المستخدم في الطلب" id="PhoneNumber" />
                            <span asp-validation-for="PhoneNumber" class="text-danger"></span>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary" style="background-color: #6a0dad; border-color: #6a0dad;">تتبع الطلب</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script>
        $(document).ready(function() {
            $.validator.unobtrusive.parse("#trackingForm");

            // تعطيل معالج الأحداث العام في site.js
            $("#trackingForm").attr("data-custom-submit", "true");

            $("#trackingForm").submit(function(e) {
                var orderId = $("#OrderId").val();
                var phoneNumber = $("#PhoneNumber").val();
                console.log("OrderId:", orderId, "PhoneNumber:", phoneNumber);

                // التحقق من إدخال رقم الطلب أو رقم الهاتف
                if ((!orderId || orderId.trim() === "") && (!phoneNumber || phoneNumber.trim() === "")) {
                    e.preventDefault();
                    alert("يرجى إدخال رقم الطلب أو رقم الهاتف على الأقل");
                    return false;
                }

                // السماح بإرسال النموذج بشكل طبيعي
                return true;
            });
        });
    </script>
}
