@model IEnumerable<Abayat.Models.Order>

@{
    ViewData["Title"] = "إدارة الطلبات";
}

<div class="products-header text-center">
    <div class="container">
        <h1 class="fade-in">
            @if (ViewData["FilteredUserName"] != null)
            {
                <span>طلبات @ViewData["FilteredUserName"]</span>
            }
            else
            {
                @ViewData["Title"]
            }
        </h1>
        <p class="lead mb-4">
            @if (ViewData["FilteredUserName"] != null)
            {
                <span>إدارة طلبات العميل @ViewData["FilteredUserName"] ومتابعة حالتها</span>
                <br />
                <a href="@Url.Action("Manage")" class="btn btn-outline-light btn-sm mt-2">
                    <i class="bi bi-arrow-left"></i> العودة إلى جميع الطلبات
                </a>
            }
            else
            {
                <span>إدارة طلبات العملاء وتحديث حالتها</span>
            }
        </p>
    </div>
</div>

<div class="container py-5">
    @Html.AntiForgeryToken()
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i> @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i> @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <div class="form-card slide-in-right">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="form-title">قائمة الطلبات</h2>
            <div class="d-flex gap-2">
                <div class="order-filter">
                    <select id="customerFilter" class="form-select">
                        <option value="">جميع العملاء</option>
                        @{
                            var customers = Model.Select(o => new { o.UserId, UserName = o.User?.Name ?? o.FullName }).Distinct().ToList();
                            foreach (var customer in customers)
                            {
                                <option value="@customer.UserId">@customer.UserName</option>
                            }
                        }
                    </select>
                </div>
                <div class="order-filter">
                    <select id="statusFilter" class="form-select">
                        <option value="">جميع الحالات</option>
                        <option value="0">قيد الانتظار</option>
                        <option value="1">تم التأكيد</option>
                        <option value="2">قيد التجهيز</option>
                        <option value="3">تم الشحن</option>
                        <option value="4">تم التسليم</option>
                        <option value="5">تم الإلغاء</option>
                    </select>
                </div>
            </div>
        </div>

        @if (ViewData["NoOrders"] != null)
        {
            <div class="text-center py-5">
                <i class="bi bi-bag-x display-1 text-muted mb-3"></i>
                <h3>لا توجد طلبات</h3>
                <p class="text-muted mb-4">@ViewData["NoOrders"]</p>
                <div class="alert alert-info">
                    <i class="bi bi-info-circle me-2"></i> تأكد من أن الطلبات يتم حفظها في قاعدة البيانات عند إنشائها.
                </div>
            </div>
        }
        else if (Model.Any())
        {
            <div class="table-responsive">
                <table class="table table-hover" id="ordersTable">
                    <thead>
                        <tr>
                            <th>رقم الطلب</th>
                            <th>العميل</th>
                            <th>التاريخ</th>
                            <th>المبلغ الإجمالي</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var order in Model)
                        {
                            <tr data-status="@((int)order.Status)" data-user-id="@order.UserId">
                                <td>#@order.Id</td>
                                <td>
                                    <div class="d-flex flex-column">
                                        <span class="fw-bold">@(order.User?.Name ?? order.FullName)</span>
                                        <small class="text-muted">@order.FullName</small>
                                    </div>
                                </td>
                                <td>@order.OrderDate.ToString("yyyy/MM/dd HH:mm")</td>
                                <td><span class="currency">ر.ع</span> @order.TotalAmount.ToString("N0")</td>
                                <td class="order-status-cell" data-order-id="@order.Id">
                                    <span class="order-status-text status-@(order.Status.ToString().ToLower())">
                                        <i class="bi @GetStatusIcon(order.Status)"></i>
                                        <span>@GetStatusDisplayName(order.Status)</span>
                                    </span>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <div class="dropdown" data-bs-boundary="viewport">
                                            <button class="btn btn-sm btn-outline-primary dropdown-toggle action-btn" type="button" data-bs-toggle="dropdown" aria-expanded="false" data-bs-auto-close="true">
                                                <i class="bi bi-three-dots-vertical"></i> الإجراءات
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li>
                                                    <a href="#" class="dropdown-item status-details" data-order-id="@order.Id">
                                                        <i class="bi bi-info-circle"></i> عرض التفاصيل
                                                    </a>
                                                </li>
                                                @if (order.Status != OrderStatus.Cancelled)
                                                {
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <a href="#" class="dropdown-item @(order.Status == OrderStatus.Pending ? "active" : "")"
                                                           data-order-id="@order.Id" data-status="@((int)OrderStatus.Pending)" data-status-name="@GetStatusDisplayName(OrderStatus.Pending)">
                                                            <i class="bi bi-hourglass"></i> قيد الانتظار
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a href="#" class="dropdown-item @(order.Status == OrderStatus.Confirmed ? "active" : "")"
                                                           data-order-id="@order.Id" data-status="@((int)OrderStatus.Confirmed)" data-status-name="@GetStatusDisplayName(OrderStatus.Confirmed)">
                                                            <i class="bi bi-check-circle"></i> تم التأكيد
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a href="#" class="dropdown-item @(order.Status == OrderStatus.Processing ? "active" : "")"
                                                           data-order-id="@order.Id" data-status="@((int)OrderStatus.Processing)" data-status-name="@GetStatusDisplayName(OrderStatus.Processing)">
                                                            <i class="bi bi-gear"></i> قيد التجهيز
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a href="#" class="dropdown-item @(order.Status == OrderStatus.Shipped ? "active" : "")"
                                                           data-order-id="@order.Id" data-status="@((int)OrderStatus.Shipped)" data-status-name="@GetStatusDisplayName(OrderStatus.Shipped)">
                                                            <i class="bi bi-truck"></i> تم الشحن
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a href="#" class="dropdown-item @(order.Status == OrderStatus.Delivered ? "active" : "")"
                                                           data-order-id="@order.Id" data-status="@((int)OrderStatus.Delivered)" data-status-name="@GetStatusDisplayName(OrderStatus.Delivered)">
                                                            <i class="bi bi-check-all"></i> تم التسليم
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a href="#" class="dropdown-item @(order.Status == OrderStatus.Cancelled ? "active" : "")"
                                                           data-order-id="@order.Id" data-status="@((int)OrderStatus.Cancelled)" data-status-name="@GetStatusDisplayName(OrderStatus.Cancelled)">
                                                            <i class="bi bi-x-circle"></i> تم الإلغاء
                                                        </a>
                                                    </li>
                                                }
                                                @if (order.Status == OrderStatus.Cancelled)
                                                {
                                                    <li><hr class="dropdown-divider"></li>
                                                    <li>
                                                        <span class="dropdown-item disabled">
                                                            <i class="bi bi-lock"></i> لا يمكن تعديل الطلب الملغي
                                                        </span>
                                                    </li>
                                                }
                                            </ul>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="text-center py-5">
                <i class="bi bi-bag-x display-1 text-muted mb-3"></i>
                <h3>لا توجد طلبات</h3>
                <p class="text-muted mb-4">لا توجد طلبات في النظام حالياً.</p>
            </div>
        }
    </div>
</div>





@section Styles {
    <link rel="stylesheet" href="~/css/order-actions.css" asp-append-version="true" />
    <style>
        .order-filter {
            min-width: 200px;
        }

        .customer-info {
            display: flex;
            flex-direction: column;
        }

        .customer-name {
            font-weight: bold;
        }

        .customer-fullname {
            font-size: 0.85rem;
            color: #6c757d;
        }

        #ordersTable th {
            white-space: nowrap;
        }

        /* تنسيقات حالة الطلب */
        .order-status-cell {
            text-align: center;
        }

        .order-status-text {
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            padding: 6px 12px;
            border-radius: 4px;
            background-color: transparent;
            min-width: 120px;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .order-status-text i {
            margin-left: 8px;
            font-size: 1.1rem;
        }

        /* ألوان نصوص حالات الطلب - بدون حدود */
        .status-pending {
            color: #ff6b35;
        }

        .status-confirmed {
            color: #007bff;
        }

        .status-processing {
            color: #6f42c1;
        }

        .status-shipped {
            color: #8b4513;
        }

        .status-delivered {
            color: #198754;
        }

        .status-cancelled {
            color: #dc3545;
        }

        /* تنسيقات القائمة المنسدلة */
        .dropdown-menu {
            position: fixed !important;
            top: auto !important;
            left: auto !important;
            right: auto !important;
            bottom: auto !important;
            z-index: 9999 !important;
            background: white;
            min-width: 180px;
            padding: 0.5rem 0;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
            border: 1px solid #e9ecef;
            transform: none !important;
            margin: 0 !important;
            inset: auto !important;
        }

        /* إصلاح موضع القائمة المنسدلة */
        .dropdown {
            position: static !important;
        }

        .table-responsive {
            overflow: visible !important;
        }

        /* تأكد من أن الجدول لا يقطع القائمة */
        .table {
            overflow: visible !important;
        }

        .table td {
            overflow: visible !important;
        }

        /* تحسين عرض القائمة المنسدلة */
        .dropdown-menu.show {
            display: block !important;
            opacity: 1;
            transform: translateY(0) !important;
            animation: dropdownFadeIn 0.15s ease-in-out;
        }

        @@keyframes dropdownFadeIn {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* تنسيقات ملخص الطلب */
        .order-summary {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .order-summary .badge {
            font-size: 0.85rem;
            padding: 0.5rem 0.75rem;
        }

        .order-summary .order-items {
            max-height: 300px;
            overflow-y: auto;
        }

        .order-summary .bg-light {
            border: 1px solid #e9ecef;
            transition: all 0.2s ease;
        }

        .order-summary .bg-light:hover {
            background-color: #f8f9fa !important;
            border-color: #6a0dad;
        }

        /* انيميشن التحميل */
        @@keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .dropdown-item {
            padding: 8px 15px;
            display: flex;
            align-items: center;
            color: #212529;
            text-decoration: none;
            transition: all 0.2s;
        }

        .dropdown-item i {
            margin-left: 8px;
            width: 20px;
            text-align: center;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
            color: #212529;
            text-decoration: none;
        }

        .dropdown-item.active {
            background-color: #f8f9fa;
            color: #212529;
            font-weight: bold;
            position: relative;
        }

        .dropdown-item.active::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 10px;
            transform: translateY(-50%);
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #6c757d;
        }

        /* تنسيقات أزرار الإجراءات */
        .action-buttons {
            display: flex;
            justify-content: center;
        }

        .action-btn {
            width: auto;
            text-align: center;
            padding: 6px 12px;
        }

        /* مؤشر التحميل */
        .status-loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        /* تنسيقات النافذة المنبثقة */
        #orderSummaryOverlay {
            overflow: hidden !important;
            padding: 20px;
            box-sizing: border-box;
        }

        #orderSummaryModal {
            max-height: calc(100vh - 40px) !important;
            overflow: hidden !important;
            margin: auto;
        }

        #orderSummaryContent {
            overflow-y: auto !important;
            overflow-x: hidden !important;
            max-height: calc(100vh - 140px) !important;
            min-height: 200px;
            scrollbar-width: thin;
            scrollbar-color: #c1c1c1 #f1f1f1;
        }

        /* تنسيق السكرول بار للمتصفحات الحديثة */
        #orderSummaryContent::-webkit-scrollbar {
            width: 12px;
        }

        #orderSummaryContent::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 6px;
            margin: 5px 0;
        }

        #orderSummaryContent::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 6px;
            border: 2px solid #f1f1f1;
        }

        #orderSummaryContent::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        #orderSummaryContent::-webkit-scrollbar-thumb:active {
            background: #999;
        }

        /* تنسيقات modal عرض الوصل - أولوية عالية */
        #paymentReceiptModal {
            z-index: 99999 !important;
        }

        #paymentReceiptModal .modal-backdrop {
            z-index: 99998 !important;
        }

        #paymentReceiptModal .modal-dialog {
            z-index: 99999 !important;
        }

        #paymentReceiptModal .modal-content {
            z-index: 99999 !important;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5) !important;
        }

        /* التأكد من أن الصورة تظهر بوضوح */
        #paymentReceiptImage {
            max-width: 100%;
            height: auto;
            border: 2px solid #dee2e6;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }

        @@media (max-width: 768px) {
            .d-flex.gap-2 {
                flex-direction: column;
            }

            .order-filter {
                width: 100%;
                margin-bottom: 0.5rem;
            }

            .order-status-badge {
                min-width: auto;
                width: 100%;
            }

            #orderSummaryOverlay {
                padding: 10px;
            }

            #orderSummaryModal {
                width: 95% !important;
                max-height: calc(100vh - 20px) !important;
            }

            #orderSummaryContent {
                max-height: calc(100vh - 120px) !important;
            }

            /* تحسين عرض modal الوصل على الهواتف */
            #paymentReceiptModal .modal-dialog {
                margin: 10px;
                max-width: calc(100% - 20px);
            }

            #paymentReceiptImage {
                max-height: 60vh;
            }
        }
    </style>
}

@section Scripts {
    <script>
        $(document).ready(function () {
            // تصفية الطلبات حسب الحالة والعميل
            function applyFilters() {
                var status = $('#statusFilter').val();
                var userId = $('#customerFilter').val();

                // إعادة عرض جميع الصفوف
                $('#ordersTable tbody tr').hide();

                // تطبيق الفلتر
                var selector = '#ordersTable tbody tr';

                if (status !== '') {
                    selector += '[data-status="' + status + '"]';
                }

                if (userId !== '') {
                    selector += '[data-user-id="' + userId + '"]';
                }

                $(selector).show();

                // عرض رسالة إذا لم تكن هناك نتائج
                if ($(selector).length === 0) {
                    if ($('#no-results-message').length === 0) {
                        $('#ordersTable tbody').append('<tr id="no-results-message"><td colspan="6" class="text-center py-3">لا توجد طلبات تطابق معايير البحث</td></tr>');
                    }
                } else {
                    $('#no-results-message').remove();
                }
            }

            // تطبيق الفلتر عند تغيير الحالة
            $('#statusFilter').on('change', function() {
                applyFilters();
            });

            // تطبيق الفلتر عند تغيير العميل
            $('#customerFilter').on('change', function() {
                applyFilters();
            });







            // دالة للحصول على اسم الحالة بناءً على الرقم
            function getStatusName(statusId) {
                switch (parseInt(statusId)) {
                    case 0: return 'pending';
                    case 1: return 'confirmed';
                    case 2: return 'processing';
                    case 3: return 'shipped';
                    case 4: return 'delivered';
                    case 5: return 'cancelled';
                    default: return 'unknown';
                }
            }

            // دوال طرق الدفع


            // دالة للحصول على أيقونة الحالة بناءً على الرقم
            function getStatusIcon(statusId) {
                switch (parseInt(statusId)) {
                    case 0: return 'bi-hourglass';
                    case 1: return 'bi-check-circle';
                    case 2: return 'bi-gear';
                    case 3: return 'bi-truck';
                    case 4: return 'bi-check-all';
                    case 5: return 'bi-x-circle';
                    default: return 'bi-question-circle';
                }
            }

            // ضبط موضع القائمة المنسدلة
            $(document).on('show.bs.dropdown', '.dropdown', function () {
                var dropdown = $(this);
                var menu = dropdown.find('.dropdown-menu');
                var button = dropdown.find('.dropdown-toggle');

                // إزالة أي تنسيق سابق
                menu.removeAttr('style');

                // انتظار حتى يتم عرض القائمة
                setTimeout(function() {
                    // الحصول على موضع الزر
                    var buttonOffset = button.offset();
                    var buttonHeight = button.outerHeight();
                    var buttonWidth = button.outerWidth();
                    var menuWidth = menu.outerWidth();

                    // حساب الموضع الأفضل للقائمة
                    var topPosition = buttonOffset.top + buttonHeight + 5;
                    var leftPosition = buttonOffset.left;

                    // التأكد من أن القائمة لا تخرج من الشاشة
                    var windowWidth = $(window).width();
                    if (leftPosition + menuWidth > windowWidth) {
                        leftPosition = buttonOffset.left + buttonWidth - menuWidth;
                    }

                    // ضبط موضع القائمة
                    menu.css({
                        'position': 'fixed',
                        'top': topPosition + 'px',
                        'left': leftPosition + 'px',
                        'z-index': 9999,
                        'transform': 'none'
                    });
                }, 10);
            });

            // إخفاء القائمة عند النقر خارجها
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.dropdown').length) {
                    $('.dropdown-menu.show').removeClass('show');
                }
            });

            // إخفاء القائمة عند التمرير
            $(window).on('scroll', function() {
                $('.dropdown-menu.show').removeClass('show');
            });

            // إخفاء القائمة عند تغيير حجم النافذة
            $(window).on('resize', function() {
                $('.dropdown-menu.show').removeClass('show');
            });

            // دوال مساعدة
            function getStatusBadgeClass(status) {
                switch(status) {
                    case 0: return 'bg-warning text-dark';
                    case 1: return 'bg-info text-white';
                    case 2: return 'bg-primary text-white';
                    case 3: return 'bg-secondary text-white';
                    case 4: return 'bg-success text-white';
                    case 5: return 'bg-danger text-white';
                    default: return 'bg-secondary text-white';
                }
            }

            function getStatusDisplayName(status) {
                switch(status) {
                    case 0: return 'في الانتظار';
                    case 1: return 'مؤكد';
                    case 2: return 'قيد التحضير';
                    case 3: return 'جاهز للتسليم';
                    case 4: return 'تم التسليم';
                    case 5: return 'ملغي';
                    default: return 'غير معروف';
                }
            }

            function formatDate(dateString) {
                const date = new Date(dateString);
                return date.toLocaleDateString('ar-SA') + ' ' + date.toLocaleTimeString('ar-SA', {hour: '2-digit', minute: '2-digit'});
            }

            function getPaymentMethodName(method) {
                switch(method) {
                    case 0: return 'الدفع عند الاستلام';
                    case 1: return 'تحويل هاتفي';
                    case 2: return 'تحويل بنكي';
                    case 3: return 'بطاقة ائتمان';
                    default: return 'غير محدد';
                }
            }



            // دالة عرض ملخص الطلب
            function showOrderSummary(orderId) {
                console.log('showOrderSummary called with orderId:', orderId);

                // إنشاء نافذة منبثقة بسيطة
                var modalHtml = `
                    <div id="orderSummaryOverlay" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center; overflow: hidden;">
                        <div id="orderSummaryModal" style="background: white; padding: 0; border-radius: 10px; max-width: 600px; width: 90%; max-height: 80vh; position: relative; display: flex; flex-direction: column; box-shadow: 0 4px 20px rgba(0,0,0,0.3);">
                            <div style="padding: 20px 20px 10px 20px; border-bottom: 1px solid #eee; position: relative; flex-shrink: 0;">
                                <button onclick="closeOrderSummary()" style="position: absolute; top: 10px; right: 15px; background: none; border: none; font-size: 24px; cursor: pointer; color: #666; line-height: 1;">&times;</button>
                                <h3 style="margin: 0; text-align: center; color: #333;">ملخص الطلب #${orderId}</h3>
                            </div>
                            <div id="orderSummaryContent" style="padding: 20px; overflow-y: auto; flex: 1; min-height: 0;">
                                <div style="text-align: center; padding: 20px;">
                                    <div style="border: 4px solid #f3f3f3; border-top: 4px solid #6a0dad; border-radius: 50%; width: 40px; height: 40px; animation: spin 2s linear infinite; margin: 0 auto;"></div>
                                    <p style="margin-top: 10px;">جاري التحميل...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                $('body').append(modalHtml);

                // جلب بيانات الطلب
                $.ajax({
                    url: '/Orders/GetOrderSummary/' + orderId,
                    type: 'GET',
                    success: function (order) {
                        console.log('Order data received:', order);
                        console.log('Payment method:', order.paymentMethod);
                        console.log('Payment phone:', order.paymentPhoneNumber);
                        console.log('Payment notes:', order.paymentNotes);
                        console.log('Payment method badge class:', getPaymentMethodBadgeClass(order.paymentMethod));
                        console.log('Payment method display name:', getPaymentMethodDisplayName(order.paymentMethod));
                        console.log('Order items:', order.orderItems);
                        console.log('Order items length:', order.orderItems ? order.orderItems.length : 'undefined');
                        var summaryHtml = `
                            <div class="">
                                <div class="row mb-3" style="margin-bottom: 0rem!important;">
                                    <div class="col-6">
                                        <strong>رقم الطلب:</strong> #${order.id}
                                    </div>
                                    <div class="col-6 ">
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-6">
                                        <strong>تاريخ الطلب:</strong>
                                        <small class="text-muted">${formatDate(order.orderDate)}</small>
                                    </div>
                                    <div class="col-6 text-end">
                                        <strong>المبلغ الإجمالي:</strong>
                                        <span class="h5 text-primary">${order.totalAmount.toFixed(2)} ر.ع</span>
                                    </div>
                                </div>

                                <div class="row mb-3">
                                    <div class="col-12">
                                        <strong>اسم العميل:</strong> ${order.fullName || 'غير محدد'}<br>
                                        <strong>رقم الهاتف:</strong> ${order.phoneNumber || 'غير محدد'}<br>
                                        <strong>العنوان:</strong> ${order.address || 'غير محدد'}<br>
                                     
                                      <strong>طريقة الدفع:</strong> ${order.paymentMethod==0?'عند الاستلام': order.paymentMethod==1? 'تحويل عبر الهاتف': 'غير محدد'}
                                      ${(() => {
                                          const shouldShow = order.paymentMethod==1 && order.paymentReceiptPath;
                                          console.log('Template check - Should show icon:', shouldShow);
                                          console.log('Template check - Payment method:', order.paymentMethod);
                                          console.log('Template check - Receipt path:', order.paymentReceiptPath);
                                          return shouldShow ? '<i class="bi bi-image-fill text-primary ms-2" style="cursor: pointer; font-size: 1.1em;" onclick="showPaymentReceipt()" title="انقر لعرض وصل التحويل"></i>' : '';
                                      })()}<br>
                                        
                                    </div>
                                </div>

                                <hr>

                                <div class="order-items">
                                    <h6 class="mb-3"><i class="bi bi-bag me-2"></i>المنتجات (${order.orderItems ? order.orderItems.length : 0})</h6>
                                    ${order.orderItems && order.orderItems.length > 0 ?
                                        order.orderItems.map(item => `
                                            <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded">
                                                <div>
                                                    <strong>${item.productName}</strong><br>
                                                    <small class="text-muted">الكمية: ${item.quantity}</small>
                                                </div>
                                                <div class="text-end">
                                                    <strong>${item.totalPrice.toFixed(2)} ر.ع</strong><br>
                                                    <small class="text-muted">${item.unitPrice.toFixed(2)} ر.ع للقطعة</small>
                                                </div>
                                            </div>
                                        `).join('')
                                        : '<div class="text-center text-muted py-3">لا توجد عناصر في هذا الطلب</div>'
                                    }
                                </div>
                            </div>
                        `;

                        console.log('Generated HTML:', summaryHtml);
                        $('#orderSummaryContent').html(summaryHtml);

                        // حفظ مسار وصل التحويل للاستخدام لاحقاً
                        window.currentPaymentReceiptPath = order.paymentReceiptPath;

                        // تتبع بيانات الوصل
                        console.log('=== تتبع أيقونة الوصل ===');
                        console.log('Order ID:', order.id);
                        console.log('Payment Method:', order.paymentMethod, '(Type:', typeof order.paymentMethod, ')');
                        console.log('Payment Receipt Path:', order.paymentReceiptPath);
                        console.log('Should show icon:', order.paymentMethod == 1 && order.paymentReceiptPath);
                        console.log('Condition 1 (payment method == 1):', order.paymentMethod == 1);
                        console.log('Condition 2 (receipt path exists):', !!order.paymentReceiptPath);
                    },
                    error: function (xhr, status, error) {
                        console.log('AJAX Error:', xhr, status, error);
                        console.log('Response Text:', xhr.responseText);
                        $('#orderSummaryContent').html('<div style="color: red; text-align: center; padding: 20px;">حدث خطأ أثناء تحميل ملخص الطلب: ' + error + '</div>');
                    }
                });
            }





            // دالة إغلاق النافذة المنبثقة
            window.closeOrderSummary = function() {
                $('#orderSummaryOverlay').remove();
            }

            // دالة لعرض وصل التحويل - دالة عامة
            window.showPaymentReceipt = function() {
                console.log('showPaymentReceipt called');
                const receiptPath = window.currentPaymentReceiptPath;
                console.log('Receipt path:', receiptPath);

                if (!receiptPath) {
                    alert('لا يوجد وصل تحويل لهذا الطلب');
                    return;
                }

                // تحديث صورة الوصل في الـ modal
                document.getElementById('paymentReceiptImage').src = receiptPath;
                document.getElementById('downloadReceiptBtn').href = receiptPath;

                // التأكد من أن modal الوصل له أولوية عالية
                const receiptModal = document.getElementById('paymentReceiptModal');
                receiptModal.style.zIndex = '99999';

                // إظهار الـ modal
                const modal = new bootstrap.Modal(receiptModal, {
                    backdrop: 'static',
                    keyboard: true
                });
                modal.show();

                // التأكد من z-index بعد الإظهار
                setTimeout(() => {
                    receiptModal.style.zIndex = '99999';
                    const backdrop = document.querySelector('.modal-backdrop');
                    if (backdrop) {
                        backdrop.style.zIndex = '99998';
                    }
                }, 100);

                console.log('Showing payment receipt:', receiptPath);
            }

            // إغلاق النافذة عند النقر خارجها فقط
            $(document).on('click', '#orderSummaryOverlay', function(e) {
                if (e.target.id === 'orderSummaryOverlay') {
                    closeOrderSummary();
                }
            });

            // منع إغلاق النافذة عند النقر على المحتوى الداخلي
            $(document).on('click', '#orderSummaryModal', function(e) {
                e.stopPropagation();
            });

            // إغلاق النافذة بالضغط على مفتاح Escape
            $(document).on('keydown', function(e) {
                if (e.key === 'Escape' && $('#orderSummaryOverlay').length > 0) {
                    closeOrderSummary();
                }
            });

            // تحسين التمرير في النافذة المنبثقة
            $(document).on('wheel', '#orderSummaryContent', function(e) {
                e.stopPropagation();

                var scrollTop = this.scrollTop;
                var scrollHeight = this.scrollHeight;
                var height = this.clientHeight;
                var delta = e.originalEvent.deltaY;

                // منع التمرير خارج حدود المحتوى
                if ((delta < 0 && scrollTop === 0) || (delta > 0 && scrollTop + height >= scrollHeight)) {
                    e.preventDefault();
                }
            });

            // تحسين التمرير باللمس للأجهزة المحمولة
            $(document).on('touchstart touchmove', '#orderSummaryContent', function(e) {
                e.stopPropagation();
            });

            // دوال مساعدة للتنسيق
            function getStatusBadgeClass(status) {
                switch(status) {
                    case 0: return 'bg-warning text-dark'; // Pending
                    case 1: return 'bg-info text-white'; // Confirmed
                    case 2: return 'bg-primary text-white'; // Processing
                    case 3: return 'bg-info text-white'; // Shipped
                    case 4: return 'bg-success text-white'; // Delivered
                    case 5: return 'bg-danger text-white'; // Cancelled
                    default: return 'bg-secondary text-white';
                }
            }

            function getStatusDisplayName(status) {
                switch(status) {
                    case 0: return 'قيد الانتظار';
                    case 1: return 'تم التأكيد';
                    case 2: return 'قيد التجهيز';
                    case 3: return 'تم الشحن';
                    case 4: return 'تم التسليم';
                    case 5: return 'تم الإلغاء';
                    default: return 'غير معروف';
                }
            }

            function getPaymentMethodBadgeClass(paymentMethod) {
                switch(paymentMethod) {
                    case 0: return 'bg-success text-white'; // CashOnDelivery
                    case 1: return 'bg-primary text-white'; // PhoneTransfer
                    case 2: return 'bg-secondary text-white'; // BankTransfer
                    case 3: return 'bg-secondary text-white'; // DebitCard
                    default: return 'bg-secondary text-white';
                }
            }

            function getPaymentMethodDisplayName(paymentMethod) {
                switch(paymentMethod) {
                    case 0: return 'الدفع عند الاستلام';
                    case 1: return 'تحويل عبر رقم الهاتف';
                    case 2: return 'تحويل بنكي (غير متوفر حالياً)';
                    case 3: return 'بطاقة الخصم (غير متوفر حالياً)';
                    default: return 'غير معروف';
                }
            }

            function formatDate(dateString) {
                var date = new Date(dateString);
                return date.toLocaleDateString('ar-SA', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                });
            }

            // تحديث حالة الطلب باستخدام AJAX
            $(document).on('click', '.dropdown-item', function(e) {
                e.preventDefault();

                var link = $(this);
                var orderId = link.data('order-id');

                // إذا كان الرابط هو "عرض التفاصيل"، فقم بتنفيذ وظيفة عرض التفاصيل
                if (link.hasClass('status-details')) {
                    console.log('Details link clicked for order:', orderId);
                    showOrderSummary(orderId);
                    return;
                }
                var status = link.data('status');
                var statusName = link.data('status-name');

                // إظهار مؤشر التحميل
                var loadingOverlay = $('<div class="status-loading-overlay"><div class="spinner-border spinner-border-sm text-primary" role="status"><span class="visually-hidden">جاري التحديث...</span></div></div>');
                $('body').append(loadingOverlay);

                // تحديث حالة الطلب في واجهة المستخدم مباشرة
                var statusCell = $('.order-status-cell[data-order-id="' + orderId + '"]');
                var statusText = statusCell.find('.order-status-text');

                // حفظ الحالة السابقة للاستعادة في حالة الخطأ
                statusCell.data('original-status', statusText.find('span').text());
                statusCell.data('original-class', statusText.attr('class'));

                // إزالة جميع فئات الحالة السابقة
                statusText.removeClass(function (index, className) {
                    return (className.match(/(^|\s)status-\S+/g) || []).join(' ');
                });

                // إضافة فئة الحالة الجديدة (للحفاظ على التنسيق فقط)
                var statusClass = 'status-' + getStatusName(status).toLowerCase();
                statusText.addClass(statusClass);

                // تحديث الأيقونة والنص
                var iconClass = getStatusIcon(status);
                statusText.html('<i class="bi ' + iconClass + '"></i><span>' + statusName + '</span>');

                // تحديث الصف في الجدول
                var row = statusCell.closest('tr');
                row.attr('data-status', status);

                // إرسال طلب AJAX لتحديث الحالة في قاعدة البيانات
                $.ajax({
                    url: '/Orders/UpdateStatus',
                    type: 'POST',
                    data: {
                        id: orderId,
                        status: status,
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(response) {
                        // إزالة مؤشر التحميل
                        $('.status-loading-overlay').remove();

                        // تحديث الواجهة بعد نجاح العملية
                        // إزالة الفئة النشطة من جميع الروابط في نفس القائمة
                        link.closest('.dropdown-menu').find('.dropdown-item').removeClass('active');
                        // إضافة الفئة النشطة للزر المحدد
                        link.addClass('active');

                        // عرض رسالة نجاح
                        var alertHtml = '<div class="alert alert-success alert-dismissible fade show" role="alert">' +
                                        '<i class="bi bi-check-circle me-2"></i> تم تحديث حالة الطلب بنجاح' +
                                        '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                                        '</div>';

                        // إضافة التنبيه في بداية الحاوية
                        $('.container.py-5').prepend(alertHtml);

                        // إغلاق القائمة المنسدلة
                        $('.dropdown-menu.show').removeClass('show');

                        // تطبيق الفلتر الحالي
                        applyFilters();

                        // إخفاء التنبيه بعد 3 ثوان
                        setTimeout(function() {
                            $('.alert').alert('close');
                        }, 3000);
                    },
                    error: function(xhr, status, error) {
                        // إزالة مؤشر التحميل
                        $('.status-loading-overlay').remove();

                        // عرض رسالة خطأ
                        var alertHtml = '<div class="alert alert-danger alert-dismissible fade show" role="alert">' +
                                        '<i class="bi bi-exclamation-triangle me-2"></i> حدث خطأ أثناء تحديث حالة الطلب' +
                                        '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                                        '</div>';

                        // إضافة التنبيه في بداية الحاوية
                        $('.container.py-5').prepend(alertHtml);

                        // إعادة الحالة السابقة في واجهة المستخدم
                        var originalStatus = statusCell.data('original-status');
                        if (originalStatus) {
                            // إزالة جميع فئات الحالة
                            statusText.removeClass(function (index, className) {
                                return (className.match(/(^|\s)status-\S+/g) || []).join(' ');
                            });

                            // إعادة الفئة الأصلية
                            statusText.attr('class', statusCell.data('original-class'));

                            // إعادة المحتوى الأصلي
                            statusText.html('<i class="bi ' + getStatusIcon(row.attr('data-status')) + '"></i><span>' + originalStatus + '</span>');
                        }

                        // إخفاء التنبيه بعد 3 ثوان
                        setTimeout(function() {
                            $('.alert').alert('close');
                        }, 3000);
                    }
                });
            });
        });
    </script>
}

<!-- Modal لعرض وصل التحويل -->
<div class="modal fade" id="paymentReceiptModal" tabindex="-1" aria-labelledby="paymentReceiptModalLabel" aria-hidden="true" style="z-index: 99999 !important;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="z-index: 99999 !important;">
            <div class="modal-header">
                <h5 class="modal-title" id="paymentReceiptModalLabel">وصل التحويل</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="paymentReceiptImage" src="" alt="وصل التحويل" class="img-fluid" style="max-height: 70vh; border-radius: 8px; z-index: 99999 !important;">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <a id="downloadReceiptBtn" href="" download class="btn btn-primary">
                    <i class="bi bi-download"></i> تحميل الوصل
                </a>
            </div>
        </div>
    </div>
</div>

@functions {
    string GetStatusBadgeClass(OrderStatus status)
    {
        return status switch
        {
            OrderStatus.Pending => "bg-warning",
            OrderStatus.Confirmed => "bg-info",
            OrderStatus.Processing => "bg-primary",
            OrderStatus.Shipped => "bg-info",
            OrderStatus.Delivered => "bg-success",
            OrderStatus.Cancelled => "bg-danger",
            _ => "bg-secondary"
        };
    }

    string GetStatusDisplayName(OrderStatus status)
    {
        return status switch
        {
            OrderStatus.Pending => "قيد الانتظار",
            OrderStatus.Confirmed => "تم التأكيد",
            OrderStatus.Processing => "قيد التجهيز",
            OrderStatus.Shipped => "تم الشحن",
            OrderStatus.Delivered => "تم التسليم",
            OrderStatus.Cancelled => "تم الإلغاء",
            _ => "غير معروف"
        };
    }

    string GetStatusIcon(OrderStatus status)
    {
        return status switch
        {
            OrderStatus.Pending => "bi-hourglass",
            OrderStatus.Confirmed => "bi-check-circle",
            OrderStatus.Processing => "bi-gear",
            OrderStatus.Shipped => "bi-truck",
            OrderStatus.Delivered => "bi-check-all",
            OrderStatus.Cancelled => "bi-x-circle",
            _ => "bi-question-circle"
        };
    }
}
