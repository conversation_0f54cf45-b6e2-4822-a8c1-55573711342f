@{
    ViewData["Title"] = "تم إنشاء الطلب بنجاح";
}

<div class="products-header text-center">
    <div class="container">
        <h1 class="fade-in">@ViewData["Title"]</h1>
        <p class="lead mb-4">شكراً لطلبك! سنتواصل معك قريباً</p>
    </div>
</div>

<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-body text-center p-5">
                    <div class="mb-4">
                        <i class="bi bi-check-circle-fill text-success" style="font-size: 5rem;"></i>
                    </div>
                    <h2 class="mb-3">تم استلام طلبك بنجاح</h2>
                    <p class="lead mb-4">رقم الطلب: <strong>#@ViewData["OrderId"]</strong></p>
                    <p class="mb-4">سنقوم بمراجعة طلبك والتواصل معك قريباً على رقم الهاتف الذي قمت بإدخاله.</p>
                    <p class="mb-4">يرجى الاحتفاظ برقم الطلب للمتابعة.</p>
                    <p class="alert alert-success">تم حفظ الطلب في قاعدة البيانات بنجاح!</p>

                    <div class="mt-4">
                        <a asp-controller="Home" asp-action="Index" class="btn btn-primary">
                            <i class="bi bi-house me-2"></i> العودة للصفحة الرئيسية
                        </a>
                        <a asp-controller="Products" asp-action="Index" class="btn btn-outline-primary ms-2">
                            <i class="bi bi-bag me-2"></i> مواصلة التسوق
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
