@model IEnumerable<Abayat.Models.Order>

@{
    ViewData["Title"] = "طلباتي";

    // تقسيم الطلبات إلى حالية وسابقة
    var currentOrders = Model.Where(o => o.Status == OrderStatus.Pending || o.Status == OrderStatus.Confirmed || o.Status == OrderStatus.Processing || o.Status == OrderStatus.Shipped).ToList();
    var previousOrders = Model.Where(o => o.Status == OrderStatus.Delivered || o.Status == OrderStatus.Cancelled).ToList();

    // الحالة المحددة للتصفية
    var statusFilter = Context.Request.Query["status"].ToString();
}

<div class="products-header text-center">
    <div class="container">
        <h1 class="fade-in">@ViewData["Title"]</h1>
        <p class="lead mb-4">عرض طلباتك الحالية والسابقة</p>
    </div>
</div>

<div class="container py-5">
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i> @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i> @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <!-- فلتر الطلبات -->
    <div class="form-card slide-in-right mb-4">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h2 class="form-title mb-0">تصفية الطلبات</h2>
        </div>

        <div class="order-filter">
            <div class="btn-group w-100">
                <a asp-action="Index" class="btn @(string.IsNullOrEmpty(statusFilter) ? "btn-primary" : "btn-outline-primary")">
                    <i class="bi bi-grid-3x3"></i> جميع الطلبات
                </a>
                <a asp-action="Index" asp-route-status="current" class="btn @(statusFilter == "current" ? "btn-primary" : "btn-outline-primary")">
                    <i class="bi bi-hourglass-split"></i> الطلبات الحالية
                </a>
                <a asp-action="Index" asp-route-status="delivered" class="btn @(statusFilter == "delivered" ? "btn-primary" : "btn-outline-primary")">
                    <i class="bi bi-check2-circle"></i> الطلبات المسلمة
                </a>
                <a asp-action="Index" asp-route-status="cancelled" class="btn @(statusFilter == "cancelled" ? "btn-primary" : "btn-outline-primary")">
                    <i class="bi bi-x-circle"></i> الطلبات الملغاة
                </a>
            </div>
        </div>
    </div>

    @if (!Model.Any())
    {
        <div class="form-card slide-in-right">
            <div class="text-center py-5">
                <i class="bi bi-bag-x display-1 text-muted mb-3"></i>
                <h3>لا توجد طلبات</h3>
                <p class="text-muted mb-4">لم تقم بإنشاء أي طلبات بعد.</p>
                <a asp-controller="Products" asp-action="Index" class="btn btn-primary">
                    <i class="bi bi-bag me-2"></i> تصفح المنتجات
                </a>
            </div>
        </div>
    }
    else
    {
        @* عرض الطلبات الحالية *@
        @if ((string.IsNullOrEmpty(statusFilter) || statusFilter == "current") && currentOrders.Any())
        {
            <div class="form-card slide-in-right mb-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h2 class="form-title mb-0">الطلبات الحالية</h2>
                    <span class="badge bg-primary rounded-pill">@currentOrders.Count</span>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الطلب</th>
                                <th>التاريخ</th>
                                <th>المبلغ الإجمالي</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var order in currentOrders.OrderByDescending(o => o.OrderDate))
                            {
                                <tr>
                                    <td>#@order.Id</td>
                                    <td>@order.OrderDate.ToString("yyyy/MM/dd HH:mm")</td>
                                    <td><span class="currency">ر.ع</span> @order.TotalAmount.ToString("N0")</td>
                                    <td>
                                        <span class="badge @GetStatusBadgeClass(order.Status)">
                                            @GetStatusDisplayName(order.Status)
                                        </span>
                                    </td>
                                    <td>
                                        <a asp-action="Details" asp-route-id="@order.Id" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-info-circle"></i> التفاصيل
                                        </a>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        }

        @* عرض الطلبات المسلمة *@
        @if ((string.IsNullOrEmpty(statusFilter) || statusFilter == "delivered") && previousOrders.Where(o => o.Status == OrderStatus.Delivered).Any())
        {
            <div class="form-card slide-in-right mb-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h2 class="form-title mb-0">الطلبات المسلمة</h2>
                    <span class="badge bg-success rounded-pill">@previousOrders.Count(o => o.Status == OrderStatus.Delivered)</span>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الطلب</th>
                                <th>التاريخ</th>
                                <th>المبلغ الإجمالي</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var order in previousOrders.Where(o => o.Status == OrderStatus.Delivered).OrderByDescending(o => o.OrderDate))
                            {
                                <tr>
                                    <td>#@order.Id</td>
                                    <td>@order.OrderDate.ToString("yyyy/MM/dd HH:mm")</td>
                                    <td><span class="currency">ر.ع</span> @order.TotalAmount.ToString("N0")</td>
                                    <td>
                                        <span class="badge @GetStatusBadgeClass(order.Status)">
                                            @GetStatusDisplayName(order.Status)
                                        </span>
                                    </td>
                                    <td>
                                        <a asp-action="Details" asp-route-id="@order.Id" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-info-circle"></i> التفاصيل
                                        </a>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        }

        @* عرض الطلبات الملغاة *@
        @if ((string.IsNullOrEmpty(statusFilter) || statusFilter == "cancelled") && previousOrders.Where(o => o.Status == OrderStatus.Cancelled).Any())
        {
            <div class="form-card slide-in-right">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h2 class="form-title mb-0">الطلبات الملغاة</h2>
                    <span class="badge bg-danger rounded-pill">@previousOrders.Count(o => o.Status == OrderStatus.Cancelled)</span>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم الطلب</th>
                                <th>التاريخ</th>
                                <th>المبلغ الإجمالي</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var order in previousOrders.Where(o => o.Status == OrderStatus.Cancelled).OrderByDescending(o => o.OrderDate))
                            {
                                <tr>
                                    <td>#@order.Id</td>
                                    <td>@order.OrderDate.ToString("yyyy/MM/dd HH:mm")</td>
                                    <td><span class="currency">ر.ع</span> @order.TotalAmount.ToString("N0")</td>
                                    <td>
                                        <span class="badge @GetStatusBadgeClass(order.Status)">
                                            @GetStatusDisplayName(order.Status)
                                        </span>
                                    </td>
                                    <td>
                                        <a asp-action="Details" asp-route-id="@order.Id" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-info-circle"></i> التفاصيل
                                        </a>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        }
    }
</div>

@section Styles {
    <style>
        .order-filter {
            margin-bottom: 1rem;
        }

        .order-filter .btn-group {
            flex-wrap: wrap;
        }

        .order-filter .btn {
            margin-bottom: 0.5rem;
            border-radius: 0.25rem !important;
            margin-right: 0.25rem;
        }

        @@media (max-width: 768px) {
            .order-filter .btn {
                flex: 1 0 calc(50% - 0.5rem);
            }
        }
    </style>
}

@functions {
    string GetStatusBadgeClass(OrderStatus status)
    {
        return status switch
        {
            OrderStatus.Pending => "bg-warning",
            OrderStatus.Confirmed => "bg-info",
            OrderStatus.Processing => "bg-primary",
            OrderStatus.Shipped => "bg-info",
            OrderStatus.Delivered => "bg-success",
            OrderStatus.Cancelled => "bg-danger",
            _ => "bg-secondary"
        };
    }

    string GetStatusDisplayName(OrderStatus status)
    {
        return status switch
        {
            OrderStatus.Pending => "قيد الانتظار",
            OrderStatus.Confirmed => "تم التأكيد",
            OrderStatus.Processing => "قيد التجهيز",
            OrderStatus.Shipped => "تم الشحن",
            OrderStatus.Delivered => "تم التسليم",
            OrderStatus.Cancelled => "تم الإلغاء",
            _ => "غير معروف"
        };
    }
}
