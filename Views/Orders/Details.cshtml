@model Abayat.Models.Order

@{
    ViewData["Title"] = "تفاصيل الطلب";
}

<div class="products-header text-center">
    <div class="container">
        <h1 class="fade-in">تفاصيل الطلب #@Model.Id</h1>
        <p class="lead mb-4">عرض تفاصيل الطلب وحالته</p>
    </div>
</div>

<div class="container py-5">
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle me-2"></i> @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle me-2"></i> @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <div class="row g-0">
        <div class="col-md-8 border-end">
            <div class="p-4">
                <h3 class="section-title border-bottom pb-2 mb-3">المنتجات</h3>

                <div class="table-responsive">
                    <table class="table table-hover order-products-table">
                        <thead>
                            <tr>
                                <th>المنتج</th>
                                <th>السعر</th>
                                <th>الكمية</th>
                                <th>المجموع</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if (Model.OrderItems != null)
                            {
                                foreach (var item in Model.OrderItems)
                                {
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if (item.Product != null && !string.IsNullOrEmpty(item.Product.ImageUrl))
                                                {
                                                    <img src="@item.Product.ImageUrl" alt="@item.ProductName" class="order-item-img me-3">
                                                }
                                                else
                                                {
                                                    <img src="https://placehold.co/100x100/6a0dad/ffffff?text=@item.ProductName" alt="@item.ProductName" class="order-item-img me-3">
                                                }
                                                <div>
                                                    <h5 class="mb-0">@item.ProductName</h5>
                                                </div>
                                            </div>
                                        </td>
                                        <td><span class="currency">ر.ع</span> @item.UnitPrice.ToString("N0")</td>
                                        <td>@item.Quantity</td>
                                        <td><span class="currency">ر.ع</span> @item.TotalPrice.ToString("N0")</td>
                                    </tr>
                                }
                            }
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="3" class="text-end fw-bold">المجموع الكلي:</td>
                                <td class="fw-bold total-amount"><span class="currency">ر.ع</span> @Model.TotalAmount.ToString("N0")</td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="p-4">
                <h3 class="section-title border-bottom pb-2 mb-3">معلومات الطلب</h3>

                <div class="order-info">
                    <div class="info-item">
                        <div class="d-flex justify-content-between">
                            <span class="info-label">رقم الطلب</span>
                            <span class="info-value">#@Model.Id</span>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="d-flex justify-content-between">
                            <span class="info-label">تاريخ الطلب</span>
                            <span class="info-value">@Model.OrderDate.ToString("yyyy/MM/dd HH:mm")</span>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="d-flex justify-content-between">
                            <span class="info-label">حالة الطلب</span>
                            <span class="badge @GetStatusBadgeClass(Model.Status)">
                                @GetStatusDisplayName(Model.Status)
                            </span>
                        </div>
                    </div>

                    <hr class="my-3" />

                    <div class="info-item">
                        <span class="info-label d-block">الاسم</span>
                        <span class="info-value d-block">@Model.FullName</span>
                    </div>

                    <div class="info-item">
                        <span class="info-label d-block">رقم الهاتف</span>
                        <span class="info-value d-block">@Model.PhoneNumber</span>
                    </div>

                    <div class="info-item">
                        <span class="info-label d-block">العنوان</span>
                        <span class="info-value d-block">@Model.Address</span>
                    </div>

                    @if (!string.IsNullOrEmpty(Model.Notes))
                    {
                        <div class="info-item">
                            <span class="info-label d-block">ملاحظات</span>
                            <span class="info-value d-block">@Model.Notes</span>
                        </div>
                    }

                    <hr class="my-3" />

                    <h4 class="section-title mb-3">
                        <i class="bi bi-credit-card me-2"></i>معلومات الدفع
                    </h4>

                    <div class="info-item">
                        <div class="d-flex justify-content-between">
                            <span class="info-label">طريقة الدفع</span>
                            <span class="badge @GetPaymentMethodBadgeClass(Model.PaymentMethod)">
                                @GetPaymentMethodDisplayName(Model.PaymentMethod)
                            </span>
                        </div>
                    </div>

                    @if (Model.PaymentMethod == PaymentMethod.PhoneTransfer)
                    {
                        @if (!string.IsNullOrEmpty(Model.PaymentPhoneNumber))
                        {
                            <div class="info-item">
                                <span class="info-label d-block">رقم الهاتف للتحويل</span>
                                <span class="info-value d-block">@Model.PaymentPhoneNumber</span>
                            </div>
                        }

                        @if (!string.IsNullOrEmpty(Model.PaymentReceiptPath))
                        {
                            <div class="info-item">
                                <span class="info-label d-block">وصل التحويل</span>
                                <div class="mt-2">
                                    <img src="@Model.PaymentReceiptPath" alt="وصل التحويل" class="payment-receipt-img" onclick="showReceiptModal('@Model.PaymentReceiptPath')" style="cursor: pointer;">
                                    <br>
                                    <small class="text-muted">انقر على الصورة لعرضها بحجم أكبر</small>
                                </div>
                            </div>
                        }

                        @if (!string.IsNullOrEmpty(Model.PaymentNotes))
                        {
                            <div class="info-item">
                                <span class="info-label d-block">ملاحظات الدفع</span>
                                <span class="info-value d-block">@Model.PaymentNotes</span>
                            </div>
                        }
                    }

                    @if (Model.Status == OrderStatus.Pending || Model.Status == OrderStatus.Confirmed)
                    {
                        <div class="mt-4">
                            <form asp-action="Cancel" method="post">
                                <input type="hidden" name="id" value="@Model.Id" />
                                <button type="submit" class="btn btn-danger w-100" onclick="return confirm('هل أنت متأكد من إلغاء الطلب؟ لا يمكن التراجع عن هذا الإجراء')">
                                    <i class="bi bi-x-circle"></i> إلغاء الطلب
                                </button>
                            </form>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <div class="text-center mt-4">
        <a asp-action="Index" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right me-2"></i> العودة للطلبات
        </a>
    </div>
</div>

<!-- Modal لعرض وصل التحويل -->
<div class="modal fade" id="receiptModal" tabindex="-1" aria-labelledby="receiptModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="receiptModalLabel">وصل التحويل</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="receiptImage" src="" alt="وصل التحويل" class="img-fluid">
            </div>
        </div>
    </div>
</div>

@section Styles {
    <style>
        .order-item-img {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 8px;
        }

        .payment-receipt-img {
            max-width: 200px;
            max-height: 150px;
            object-fit: cover;
            border-radius: 8px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .payment-receipt-img:hover {
            border-color: #6a0dad;
            transform: scale(1.05);
        }

        .section-title {
            color: #6a0dad;
            font-size: 1.1rem;
            font-weight: 600;
        }

        /* تنسيقات شارات طرق الدفع */
        .badge.payment-cash {
            background-color: #28a745;
            color: white;
        }

        .badge.payment-phone {
            background-color: #007bff;
            color: white;
        }

        .badge.payment-bank {
            background-color: #6c757d;
            color: white;
        }

        .badge.payment-card {
            background-color: #6c757d;
            color: white;
        }
    </style>
}

@section Scripts {
    <script>
        function showReceiptModal(imageSrc) {
            document.getElementById('receiptImage').src = imageSrc;
            var receiptModal = new bootstrap.Modal(document.getElementById('receiptModal'));
            receiptModal.show();
        }
    </script>
}



@functions {
    string GetStatusBadgeClass(OrderStatus status)
    {
        return status switch
        {
            OrderStatus.Pending => "bg-warning",
            OrderStatus.Confirmed => "bg-info",
            OrderStatus.Processing => "bg-primary",
            OrderStatus.Shipped => "bg-info",
            OrderStatus.Delivered => "bg-success",
            OrderStatus.Cancelled => "bg-danger",
            _ => "bg-secondary"
        };
    }

    string GetStatusDisplayName(OrderStatus status)
    {
        return status switch
        {
            OrderStatus.Pending => "قيد الانتظار",
            OrderStatus.Confirmed => "تم التأكيد",
            OrderStatus.Processing => "قيد التجهيز",
            OrderStatus.Shipped => "تم الشحن",
            OrderStatus.Delivered => "تم التسليم",
            OrderStatus.Cancelled => "تم الإلغاء",
            _ => "غير معروف"
        };
    }

    string GetPaymentMethodBadgeClass(PaymentMethod paymentMethod)
    {
        return paymentMethod switch
        {
            PaymentMethod.CashOnDelivery => "payment-cash",
            PaymentMethod.PhoneTransfer => "payment-phone",
            PaymentMethod.BankTransfer => "payment-bank",
            PaymentMethod.DebitCard => "payment-card",
            _ => "bg-secondary"
        };
    }

    string GetPaymentMethodDisplayName(PaymentMethod paymentMethod)
    {
        return paymentMethod switch
        {
            PaymentMethod.CashOnDelivery => "الدفع عند الاستلام",
            PaymentMethod.PhoneTransfer => "تحويل عبر رقم الهاتف",
            PaymentMethod.BankTransfer => "تحويل بنكي (غير متوفر حالياً)",
            PaymentMethod.DebitCard => "بطاقة الخصم (غير متوفر حالياً)",
            _ => "غير معروف"
        };
    }
}
