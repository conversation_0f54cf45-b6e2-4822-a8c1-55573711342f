@model List<Abayat.Models.Product>
@{
    ViewData["Title"] = "مقارنة المنتجات";
}

@Html.AntiForgeryToken()

<div class="products-header text-center">
    <div class="container">
        <h1 class="fade-in">مقارنة المنتجات</h1>
        <p class="lead mb-4">قارن بين المنتجات لاختيار الأفضل لك</p>
       
    </div>
</div>

<div class="compare-container container">
   
    @if (Model != null && Model.Any())
    {
        <div class="compare-header mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <div class="products-count">
                    عدد المنتجات: <span>@Model.Count</span>
                </div>
                <div class="compare-actions">
                    <button class="btn btn-outline-danger" onclick="clearAllCompare()">
                        <i class="bi bi-trash me-1"></i> حذف الكل
                    </button>
                    <a asp-controller="Products" asp-action="Index" class="btn btn-outline-primary">
                        <i class="bi bi-arrow-right me-1"></i> العودة إلى المنتجات
                    </a>
                </div>
            </div>
        </div>

        <!-- نصائح للمستخدم -->
        <div class="compare-tips mb-3">
            <div class="alert alert-info d-flex align-items-center">
                <i class="bi bi-info-circle me-2"></i>
                <span>يمكنك التمرير أفقياً لمشاهدة جميع المنتجات والمقارنة بينها بسهولة</span>
            </div>
        </div>

        <!-- جدول المقارنة المحسن -->
        <div class="compare-table-container">
            <div class="compare-table-wrapper">
                <table class="compare-table">
                    <thead>
                        <tr>
                            <th class="property-header">الخصائص</th>
                            @foreach (var product in Model)
                            {
                                <th class="product-header">
                                    <div class="product-header-content">
                                        <div class="product-image-wrapper">
                                            @if (!string.IsNullOrEmpty(product.ImageUrl))
                                            {
                                                <img src="@product.ImageUrl" alt="@product.Name" class="product-header-img">
                                            }
                                            else
                                            {
                                                <img src="https://placehold.co/200x200/6a0dad/ffffff?text=@product.Name" alt="@product.Name" class="product-header-img">
                                            }
                                            <button class="remove-product-btn" onclick="removeFromCompare(@product.Id, this)" title="إزالة من المقارنة">
                                                <i class="bi bi-x-circle-fill"></i>
                                            </button>
                                        </div>
                                        <h4 class="product-header-title">@product.Name</h4>
                                    </div>
                                </th>
                            }
                        </tr>
                    </thead>
                    <tbody>
                        <!-- السعر -->
                        <tr class="compare-row price-row">
                            <td class="property-label">السعر</td>
                            @foreach (var product in Model)
                            {
                                <td class="property-value">
                                    <div class="price-container">
                                        @if (product.DiscountPercentage > 0)
                                        {
                                            var discountedPrice = product.Price * (1 - product.DiscountPercentage / 100);
                                            <div class="current-price">@discountedPrice.ToString("F2") ر.ع</div>
                                            <div class="original-price">@product.Price.ToString("F2") ر.ع</div>
                                            <div class="discount-badge">خصم @product.DiscountPercentage%</div>
                                        }
                                        else
                                        {
                                            <div class="current-price">@product.Price.ToString("F2") د.ل</div>
                                        }
                                    </div>
                                </td>
                            }
                        </tr>

                        <!-- الفئة -->
                        <tr class="compare-row">
                            <td class="property-label">الفئة</td>
                            @foreach (var product in Model)
                            {
                                <td class="property-value">
                                    <span class="category-badge">@(product.Category?.Name ?? "غير محدد")</span>
                                </td>
                            }
                        </tr>

                        <!-- الحالة -->
                        <tr class="compare-row">
                            <td class="property-label">الحالة</td>
                            @foreach (var product in Model)
                            {
                                <td class="property-value">
                                    @if (product.IsAvailable)
                                    {
                                        <span class="status-badge available">متوفر</span>
                                    }
                                    else
                                    {
                                        <span class="status-badge unavailable">غير متوفر</span>
                                    }
                                </td>
                            }
                        </tr>

                        <!-- النوع -->
                        <tr class="compare-row">
                            <td class="property-label">النوع</td>
                            @foreach (var product in Model)
                            {
                                <td class="property-value">
                                    <span class="type-badge">@(product.ProductType ?? "غير محدد")</span>
                                </td>
                            }
                        </tr>

                        <!-- الوصف -->
                        <tr class="compare-row description-row">
                            <td class="property-label">الوصف</td>
                            @foreach (var product in Model)
                            {
                                <td class="property-value">
                                    <div class="description-text">
                                        @if (!string.IsNullOrEmpty(product.Description))
                                        {
                                            @(product.Description.Length > 150 ? product.Description.Substring(0, 150) + "..." : product.Description)
                                        }
                                        else
                                        {
                                            <span class="text-muted">لا يوجد وصف</span>
                                        }
                                    </div>
                                </td>
                            }
                        </tr>

                        <!-- الإجراءات -->
                        <tr class="compare-row actions-row">
                            <td class="property-label">الإجراءات</td>
                            @foreach (var product in Model)
                            {
                                <td class="property-value">
                                    <div class="action-buttons">
                                        <button class="btn btn-primary btn-sm" onclick="addToCart(@product.Id, this)" title="إضافة للسلة">
                                            <i class="bi bi-cart-plus"></i>
                                        </button>
                                        <button class="btn btn-outline-danger btn-sm" onclick="addToWishlist(@product.Id, this)" title="إضافة للمفضلة">
                                            <i class="bi bi-heart"></i>
                                        </button>
                                        <a href="/Products/Details/@product.Id" class="btn btn-outline-info btn-sm" title="عرض التفاصيل">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                    </div>
                                </td>
                            }
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    }
    else
    {
        <div class="alert alert-info text-center">
            <div class="d-flex align-items-center justify-content-center">
                <i class="bi bi-info-circle-fill fs-3 me-3"></i>
                <div>
                    <h5 class="alert-heading mb-1">لا توجد منتجات للمقارنة</h5>
                    <p class="mb-0">لم تقم بإضافة أي منتجات للمقارنة بعد.</p>
                    <a asp-controller="Products" asp-action="Index" class="btn btn-primary mt-3">تصفح المنتجات</a>
                </div>
            </div>
        </div>
    }
</div>

<style>
    .compare-container {
        padding: 2rem 0;
    }

    .compare-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--primary-color);
        text-align: center;
        margin-bottom: 1rem;
    }

    .compare-subtitle {
        text-align: center;
        color: var(--text-muted);
        margin-bottom: 2rem;
    }

    .compare-header {
        background: white;
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }

    .products-count {
        font-weight: 600;
        color: #053a6e;
    }

    .products-count span {
        background: #053a6e;
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.9rem;
        margin-right: 0.5rem;
    }

    .compare-actions {
        display: flex;
        gap: 1rem;
        flex-wrap: wrap;
    }

    .compare-products-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 2rem;
    }

    .compare-product-card {
        background: white;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        transition: all 0.3s ease;
        position: relative;
    }

    .compare-product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(106, 13, 173, 0.15);
    }

    .compare-product-img-container {
        position: relative;
        height: 250px;
        overflow: hidden;
    }

    .compare-product-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .compare-product-img:hover {
        transform: scale(1.05);
    }

    .compare-remove-btn {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 35px;
        height: 35px;
        background: rgba(255, 255, 255, 0.9);
        border: none;
        border-radius: 50%;
        color: #dc3545;
        font-size: 1.2rem;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        z-index: 10;
    }

    .compare-remove-btn:hover {
        background: #dc3545;
        color: white;
        transform: scale(1.1);
    }

    .compare-product-info {
        padding: 1.5rem;
    }

    .compare-product-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--dark-color);
        margin-bottom: 1rem;
        line-height: 1.4;
    }

    .compare-product-price {
        margin-bottom: 1.5rem;
    }

    .current-price {
        font-size: 1.5rem;
        font-weight: 700;
        color: #00284f;
        margin-left: 0.5rem;
    }

    .original-price {
        font-size: 1rem;
        color: #00284f;
        text-decoration: line-through;
        margin-left: 0.5rem;
    }

    .discount-badge {
        background: var(--danger);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.8rem;
        font-weight: 600;
    }

    .compare-product-details {
        margin-bottom: 1.5rem;
    }

    .detail-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .detail-item:last-child {
        border-bottom: none;
    }

    .detail-label {
        font-weight: 600;
        color: var(--text-muted);
        flex: 0 0 auto;
    }

    .detail-value {
        text-align: left;
        flex: 1;
        margin-right: 1rem;
    }

    .compare-product-actions {
        display: flex;
        gap: 0.75rem;
        flex-direction: row;
    }

    .compare-product-actions .btn {
        flex: 1;
        padding: 0.75rem;
        font-weight: 600;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    .alert-info {
        background-color: rgba(106, 13, 173, 0.05);
        border: 1px solid rgba(106, 13, 173, 0.1);
        color: var(--dark-color);
        border-radius: 12px;
        padding: 3rem;
        margin: 2rem 0;
    }

    .alert-heading {
        color: var(--primary-color);
        font-weight: 600;
    }
</style>

@section Scripts {
    <script src="~/js/compare-actions.js" asp-append-version="true"></script>
    <script>
        function removeFromCompare(productId, button) {
            if (button) {
                button.disabled = true;
                button.innerHTML = '<i class="bi bi-hourglass-split"></i>';
            }

            const token = document.querySelector('input[name="__RequestVerificationToken"]')?.value;

            fetch('/Compare/AddToCompare', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `productId=${productId}&__RequestVerificationToken=${token}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.reload();
                } else {
                    alert('حدث خطأ أثناء إزالة المنتج');
                    if (button) {
                        button.disabled = false;
                        button.innerHTML = '<i class="bi bi-x"></i>';
                    }
                }
            })
            .catch(error => {
                console.error('خطأ:', error);
                alert('حدث خطأ أثناء إزالة المنتج');
                if (button) {
                    button.disabled = false;
                    button.innerHTML = '<i class="bi bi-x"></i>';
                }
            });
        }

        function clearAllCompare() {
            if (confirm('هل أنت متأكد من حذف جميع المنتجات من المقارنة؟')) {
                fetch('/Compare/ClearCompare', {
                    method: 'POST',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.reload();
                    } else {
                        alert('حدث خطأ أثناء حذف المقارنة');
                    }
                })
                .catch(error => {
                    console.error('خطأ:', error);
                    alert('حدث خطأ أثناء حذف المقارنة');
                });
            }
        }

        function addToCart(productId, button) {
            if (window.addToCart) {
                window.addToCart(productId, button);
            }
        }

        function addToWishlist(productId, button) {
            if (window.addToWishlist) {
                window.addToWishlist(productId, button);
            }
        }

        // تحسين تجربة المستخدم
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة تأثيرات بصرية للجدول
            const compareTable = document.querySelector('.compare-table');
            if (compareTable) {
                // تمييز الصف عند التمرير
                const rows = compareTable.querySelectorAll('.compare-row');
                rows.forEach(row => {
                    row.addEventListener('mouseenter', function() {
                        this.style.backgroundColor = 'rgba(106, 13, 173, 0.05)';
                    });
                    row.addEventListener('mouseleave', function() {
                        this.style.backgroundColor = '';
                    });
                });

                // تحسين التمرير الأفقي
                const tableWrapper = document.querySelector('.compare-table-wrapper');
                if (tableWrapper) {
                    let isScrolling = false;

                    tableWrapper.addEventListener('scroll', function() {
                        if (!isScrolling) {
                            tableWrapper.style.boxShadow = 'inset 0 0 10px rgba(0,0,0,0.1)';
                            isScrolling = true;
                        }

                        clearTimeout(tableWrapper.scrollTimeout);
                        tableWrapper.scrollTimeout = setTimeout(function() {
                            tableWrapper.style.boxShadow = '';
                            isScrolling = false;
                        }, 150);
                    });
                }
            }

            // إضافة تأثير للأزرار
            const actionButtons = document.querySelectorAll('.action-buttons .btn');
            actionButtons.forEach(button => {
                button.addEventListener('click', function() {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                });
            });

            // تحسين أزرار الإزالة
            const removeButtons = document.querySelectorAll('.remove-product-btn');
            removeButtons.forEach(button => {
                button.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.2) rotate(90deg)';
                });
                button.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1) rotate(0deg)';
                });
            });
        });
    </script>
}
