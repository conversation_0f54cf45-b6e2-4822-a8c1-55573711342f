@{
    ViewData["Title"] = "مقارنة المنتجات";
}

@Html.AntiForgeryToken()

<div class="products-header text-center">
    <div class="container">
        <h1 class="fade-in">مقارنة المنتجات</h1>
        <p class="lead mb-4">قارن بين المنتجات لاختيار الأفضل لك</p>
        <nav aria-label="breadcrumb" class="mb-4 d-flex justify-content-center">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index" class="text-white">الرئيسية</a></li>
                <li class="breadcrumb-item"><a asp-controller="Products" asp-action="Index" class="text-white">المنتجات</a></li>
                <li class="breadcrumb-item active text-white" aria-current="page">المقارنة</li>
            </ol>
        </nav>
    </div>
</div>

<div class="container py-5">
    <div class="compare-page-header">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h2 class="compare-title">مقارنة المنتجات</h2>
            <a asp-controller="Products" asp-action="Index" class="btn btn-outline-primary">
                <i class="bi bi-arrow-right me-1"></i> العودة إلى المنتجات
            </a>
        </div>
        <p class="compare-subtitle">قارن بين المنتجات لاختيار الأفضل لك</p>
    </div>

    <div id="compare-container">
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
        </div>
    </div>

    <div class="text-center mt-4">
        <a asp-controller="Products" asp-action="Index" class="btn btn-outline-primary">
            <i class="bi bi-arrow-right me-1"></i> العودة إلى المنتجات
        </a>
    </div>
</div>

<style>
    /* تنسيق صفحة المقارنة */
    .compare-page-header {
        text-align: center;
        margin-bottom: 2rem;
        padding-bottom: 1.5rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .compare-title {
        font-size: 1.8rem;
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }

    .compare-subtitle {
        color: var(--text-muted);
        font-size: 1rem;
    }

    .compare-container {
        position: relative;
        overflow: hidden;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        background-color: #fff;
        margin-bottom: 2rem;
    }

    .compare-table-wrapper {
        display: flex;
        overflow: hidden;
    }

    .compare-labels {
        flex: 0 0 200px;
        background-color: rgba(106, 13, 173, 0.05);
        border-right: 2px solid rgba(106, 13, 173, 0.1);
        z-index: 10;
    }

    .compare-products-scroll {
        flex: 1;
        overflow-x: auto;
        scroll-behavior: smooth;
        scrollbar-width: thin;
        scrollbar-color: rgba(106, 13, 173, 0.3) transparent;
    }

    .compare-products-scroll::-webkit-scrollbar {
        height: 8px;
    }

    .compare-products-scroll::-webkit-scrollbar-track {
        background: rgba(0, 0, 0, 0.05);
    }

    .compare-products-scroll::-webkit-scrollbar-thumb {
        background: rgba(106, 13, 173, 0.3);
        border-radius: 4px;
    }

    .compare-products-scroll::-webkit-scrollbar-thumb:hover {
        background: rgba(106, 13, 173, 0.5);
    }

    .compare-table {
        width: 100%;
        border-collapse: collapse;
        table-layout: fixed;
    }

    .compare-labels .compare-label-cell {
        padding: 1rem;
        text-align: right;
        font-weight: 600;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        background-color: rgba(106, 13, 173, 0.05);
        color: #6a0dad;
        min-height: 60px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
    }

    .compare-table th {
        padding: 1rem;
        text-align: center;
        font-weight: 600;
        border: 1px solid rgba(0, 0, 0, 0.05);
        background-color: #fff;
        min-width: 250px;
        width: 250px;
    }

    .compare-table td {
        padding: 1rem;
        text-align: center;
        border: 1px solid rgba(0, 0, 0, 0.05);
        min-width: 250px;
        width: 250px;
        vertical-align: middle;
    }

    .compare-table tr:nth-child(even) td {
        background-color: rgba(0, 0, 0, 0.01);
    }

    .compare-product-img {
        width: 150px;
        height: 150px;
        object-fit: cover;
        border-radius: 8px;
        margin: 0 auto 1rem auto;
        display: block;
        transition: transform 0.3s ease;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .compare-product-img:hover {
        transform: scale(1.05);
    }

    .compare-product-title {
        font-weight: 600;
        margin: 1rem 0;
        font-size: 1rem;
        color: var(--primary-color);
    }

    .compare-product-price {
        font-weight: 700;
        color: var(--primary-color);
        margin-bottom: 1rem;
    }

    .compare-product-price .currency {
        font-size: 0.8rem;
        margin-right: 0.25rem;
    }

    .compare-product-actions {
        display: flex;
        justify-content: center;
        gap: 0.5rem;
        margin-top: 1rem;
    }

    .compare-product-actions .btn {
        padding: 0.4rem 0.75rem;
        font-size: 0.85rem;
    }

    .compare-remove-btn {
        position: absolute;
        top: 0.5rem;
        right: 0.5rem;
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background-color: rgba(255, 0, 0, 0.1);
        color: #ff0000;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 0.8rem;
        transition: all 0.3s ease;
    }

    .compare-remove-btn:hover {
        background-color: rgba(255, 0, 0, 0.2);
        transform: scale(1.1);
    }

    .compare-product-cell {
        position: relative;
        min-width: 200px;
    }

    .fade-out {
        animation: fadeOut 0.3s forwards;
    }

    @@keyframes fadeOut {
        from { opacity: 1; }
        to { opacity: 0; }
    }

    /* رسالة عدم وجود منتجات */
    .alert-info {
        background-color: rgba(106, 13, 173, 0.05);
        border: 1px solid rgba(106, 13, 173, 0.1);
        color: var(--dark-color);
        border-radius: 8px;
        padding: 2rem;
    }

    .alert-warning {
        background-color: rgba(255, 193, 7, 0.1);
        border: 1px solid rgba(255, 193, 7, 0.2);
        color: var(--dark-color);
        border-radius: 8px;
        padding: 1rem;
        text-align: center;
    }

    .alert-heading {
        color: var(--primary-color);
        font-weight: 600;
    }

    .compare-products-count {
        font-size: 1.4rem;
        font-weight: 700;
        color: var(--primary-color);
    }

    .compare-products-count span {
        color: var(--danger);
        font-weight: 700;
    }

    /* تأثيرات زر الحذف */
    .compare-remove-btn {
        transition: all 0.3s ease;
    }

    .compare-remove-btn.removing {
        opacity: 0.5;
        transform: scale(0.8);
        pointer-events: none;
    }

    .compare-product-cell {
        transition: all 0.3s ease;
    }

    /* تحسينات السحب للجميع */
    .compare-products-scroll {
        cursor: grab;
        user-select: none;
    }

    .compare-products-scroll.dragging {
        cursor: grabbing;
    }

    .compare-products-scroll.dragging * {
        pointer-events: none;
    }

    /* مؤشر السحب */
    .scroll-indicator {
        position: absolute;
        bottom: 10px;
        right: 20px;
        background: rgba(106, 13, 173, 0.8);
        color: white;
        padding: 8px 12px;
        border-radius: 20px;
        font-size: 12px;
        opacity: 0;
        transition: opacity 0.3s ease;
        z-index: 20;
    }

    .scroll-indicator.show {
        opacity: 1;
    }

    /* تنسيقات السحب للهواتف */
    @@media (max-width: 768px) {
        .compare-container {
            display: none; /* إخفاء الجدول في الهواتف */
        }

        .compare-labels {
            flex: 0 0 150px;
        }

        .mobile-compare-container {
            display: block;
            position: relative;
            overflow: hidden;
            margin: 20px 0;
        }

        .mobile-compare-scroll {
            display: flex;
            overflow-x: auto;
            scroll-behavior: smooth;
            gap: 15px;
            padding: 10px 0;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
            cursor: grab;
            user-select: none;
        }

        .mobile-compare-scroll::-webkit-scrollbar {
            display: none; /* Chrome, Safari and Opera */
        }

        .mobile-compare-scroll.active {
            cursor: grabbing;
        }

        .mobile-compare-scroll.active .mobile-compare-item {
            pointer-events: none;
        }

        .mobile-compare-item {
            flex: 0 0 280px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            padding: 20px;
            position: relative;
            border: 2px solid #f0f0f0;
            transition: all 0.3s ease;
        }

        .mobile-compare-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(106, 13, 173, 0.15);
            border-color: #6a0dad;
        }

        .mobile-compare-remove {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 30px;
            height: 30px;
            background: rgba(255, 0, 0, 0.1);
            border: none;
            border-radius: 50%;
            color: #dc3545;
            font-size: 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            z-index: 10;
        }

        .mobile-compare-remove:hover {
            background: rgba(255, 0, 0, 0.2);
            transform: scale(1.1);
        }

        .mobile-compare-img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 15px;
        }

        .mobile-compare-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
            text-align: center;
            line-height: 1.4;
        }

        .mobile-compare-price {
            font-size: 18px;
            font-weight: 700;
            color: #6a0dad;
            text-align: center;
            margin-bottom: 15px;
        }

        .mobile-compare-details {
            border-top: 1px solid #f0f0f0;
            padding-top: 15px;
        }

        .mobile-compare-detail {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f8f9fa;
            font-size: 14px;
        }

        .mobile-compare-detail:last-child {
            border-bottom: none;
        }

        .mobile-compare-detail-label {
            font-weight: 600;
            color: #666;
        }

        .mobile-compare-detail-value {
            color: #333;
            text-align: left;
        }

        /* مؤشر السحب */
        .mobile-scroll-indicator {
            text-align: center;
            margin-top: 10px;
            color: #666;
            font-size: 14px;
        }

        .mobile-scroll-indicator i {
            margin: 0 5px;
            animation: bounce 2s infinite;
        }

        @@keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateX(0);
            }
            40% {
                transform: translateX(-5px);
            }
            60% {
                transform: translateX(5px);
            }
        }
    }

    /* إخفاء النسخة المحمولة في الشاشات الكبيرة */
    @@media (min-width: 769px) {
        .mobile-compare-container {
            display: none;
        }
    }
</style>

@section Scripts {
    <script src="~/js/compare-actions.js" asp-append-version="true"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            loadCompareProducts();
            // تم إزالة استدعاء وظائف غير معرفة
        });

        function loadCompareProducts() {
            const compareContainer = document.getElementById('compare-container');

            // جلب قائمة المقارنة من الخادم
            fetch('/Compare/GetCompareItems')
            .then(response => response.json())
            .then(data => {
                const compareList = data.items || [];

                if (compareList.length === 0) {
                    compareContainer.innerHTML = `
                        <div class="alert alert-info">
                            <div class="d-flex align-items-center justify-content-center">
                                <i class="bi bi-info-circle-fill fs-3 me-3"></i>
                                <div>
                                    <h5 class="alert-heading mb-1">لا توجد منتجات للمقارنة</h5>
                                    <p class="mb-0">لم تقم بإضافة أي منتجات للمقارنة بعد.</p>
                                    <a href="/Products/index" class="btn btn-primary mt-3">تصفح المنتجات</a>
                                </div>
                            </div>
                        </div>
                    `;
                    return;
                }

                // تحميل تفاصيل المنتجات
                loadProductDetails(compareList, compareContainer);
            })
            .catch(error => {
                console.error('خطأ في جلب قائمة المقارنة:', error);
                compareContainer.innerHTML = `
                    <div class="alert alert-danger">
                        <div class="d-flex align-items-center justify-content-center">
                            <i class="bi bi-exclamation-triangle-fill fs-3 me-3"></i>
                            <div>
                                <h5 class="alert-heading mb-1">خطأ في التحميل</h5>
                                <p class="mb-0">لم نتمكن من تحميل منتجات المقارنة. يرجى المحاولة مرة أخرى.</p>
                                <button class="btn btn-primary mt-3" onclick="loadCompareProducts()">إعادة المحاولة</button>
                            </div>
                        </div>
                    </div>
                `;
            });
        }

        function loadProductDetails(compareList, compareContainer) {

            // Obtener productos por IDs
            fetch('/Products/GetProductsByIds?ids=' + compareList.join(','))
                .then(response => response.json())
                .then(products => {
                    if (products.length === 0) {
                        compareContainer.innerHTML = `
                            <div class="alert alert-info">
                                <div class="d-flex align-items-center justify-content-center">
                                    <i class="bi bi-info-circle-fill fs-3 me-3"></i>
                                    <div>
                                        <h5 class="alert-heading mb-1">لا توجد منتجات للمقارنة</h5>
                                        <p class="mb-0">لم تقم بإضافة أي منتجات للمقارنة بعد.</p>
                                        <a href="/Products" class="btn btn-primary mt-3">تصفح المنتجات</a>
                                    </div>
                                </div>
                            </div>
                        `;
                        return;
                    }

                    // Crear tabla de comparación
                    let html = `
                        <div class="d-flex justify-content-between align-items-center mb-4">
                            <h3 class="compare-products-count">مقارنة المنتجات (<span>${products.length}</span>)</h3>
                            <button class="btn btn-outline-danger" onclick="clearCompare()">
                                <i class="bi bi-trash me-1"></i> حذف الكل
                            </button>
                        </div>
                        <div class="alert alert-warning mb-4">
                            <i class="bi bi-info-circle-fill me-2"></i>
                            قارن بين المنتجات لاختيار الأفضل لك
                        </div>
                        <div class="compare-container">
                            <div class="scroll-indicator">
                                <i class="bi bi-arrow-left-right"></i>
                                اسحب للتنقل بين المنتجات
                            </div>
                            <div class="compare-table-wrapper">
                                <div class="compare-labels">
                                    <div class="compare-label-cell">المنتج</div>
                                    <div class="compare-label-cell">السعر</div>
                                    <div class="compare-label-cell">الفئة</div>
                                    <div class="compare-label-cell">الحالة</div>
                                    <div class="compare-label-cell">الخصم</div>
                                    <div class="compare-label-cell">الوصف</div>
                                </div>
                                <div class="compare-products-scroll">
                                    <table class="compare-table">
                                        <tr>
                    `;

                    // Añadir encabezados de productos
                    products.forEach(product => {
                        const discountedPrice = product.discountPercentage > 0
                            ? (product.price * (1 - product.discountPercentage / 100)).toFixed(2)
                            : product.price.toFixed(2);

                        html += `
                            <th class="compare-product-cell" data-product-id="${product.id}">
                                <button class="compare-remove-btn" onclick="removeProductFromCompare(${product.id}, this)">
                                    <i class="bi bi-x"></i>
                                </button>
                                <img src="${product.imageUrl || `https://placehold.co/600x400/6a0dad/ffffff?text=${product.name}`}" class="compare-product-img" alt="${product.name}">
                                <h4 class="compare-product-title">${product.name}</h4>
                                <div class="compare-product-actions">
                                    <button class="btn btn-sm btn-primary add-to-cart-btn" data-product-id="${product.id}">
                                        <i class="bi bi-cart-plus me-1"></i> إضافة للسلة
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger add-to-wishlist-btn" data-product-id="${product.id}" onclick="addToWishlist(${product.id}, this)">
                                        <i class="bi bi-heart me-1"></i>
                                    </button>
                                </div>
                            </th>
                        `;
                    });

                    html += `</tr>`;

                    // Añadir filas de comparación
                    const attributes = [
                        { name: 'السعر', key: 'price', type: 'price' },
                        { name: 'الفئة', key: 'categoryName' },
                        { name: 'الحالة', key: 'isAvailable', type: 'boolean' },
                        { name: 'الخصم', key: 'discountPercentage', type: 'discount' },
                        { name: 'الوصف', key: 'description', type: 'text' }
                    ];

                    attributes.forEach(attr => {
                        html += `<tr>`;

                        products.forEach(product => {
                            let value = '';

                            if (attr.key.includes('.')) {
                                const keys = attr.key.split('.');
                                let obj = product;
                                for (const key of keys) {
                                    obj = obj?.[key];
                                    if (obj === undefined) break;
                                }
                                value = obj || '-';
                            } else {
                                value = product[attr.key] !== undefined ? product[attr.key] : '-';
                            }

                            if (attr.type === 'boolean') {
                                value = value === true ? '<span class="badge bg-success">متوفر</span>' :
                                       value === false ? '<span class="badge bg-danger">غير متوفر</span>' : '-';
                            } else if (attr.type === 'price') {
                                const discountedPrice = product.discountPercentage > 0
                                    ? (product.price * (1 - product.discountPercentage / 100)).toFixed(2)
                                    : product.price.toFixed(2);
                                value = `<div class="compare-product-price">${discountedPrice} د.ل</div>`;
                            } else if (attr.type === 'discount') {
                                value = value > 0 ? `<span class="badge bg-warning">${value}%</span>` : '<span class="text-muted">لا يوجد</span>';
                            } else if (attr.type === 'text') {
                                value = value && value !== '-' ? (value.length > 50 ? value.substring(0, 50) + '...' : value) : 'لا يوجد وصف';
                            }

                            html += `<td>${value}</td>`;
                        });

                        html += `</tr>`;
                    });

                    html += `</table></div></div></div>`;

                    // إنشاء النسخة المحمولة
                    let mobileHtml = `
                        <div class="mobile-compare-container">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h3 class="compare-products-count">مقارنة المنتجات (<span>${products.length}</span>)</h3>
                                <button class="btn btn-outline-danger btn-sm" onclick="clearCompare()">
                                    <i class="bi bi-trash me-1"></i> حذف الكل
                                </button>
                            </div>
                            <div class="alert alert-warning mb-3">
                                <i class="bi bi-info-circle-fill me-2"></i>
                                قارن بين المنتجات لاختيار الأفضل لك
                            </div>
                            <div class="mobile-compare-scroll">
                    `;

                    products.forEach(product => {
                        const discountedPrice = product.discountPercentage > 0
                            ? (product.price * (1 - product.discountPercentage / 100)).toFixed(2)
                            : product.price.toFixed(2);

                        mobileHtml += `
                            <div class="mobile-compare-item" data-product-id="${product.id}">
                                <button class="mobile-compare-remove" onclick="removeProductFromCompare(${product.id}, this)">
                                    <i class="bi bi-x"></i>
                                </button>
                                <img src="${product.imageUrl || `https://placehold.co/600x400/6a0dad/ffffff?text=${product.name}`}"
                                     class="mobile-compare-img" alt="${product.name}">
                                <h4 class="mobile-compare-title">${product.name}</h4>
                                <div class="mobile-compare-price">${discountedPrice} د.ل</div>
                                <div class="mobile-compare-details">
                                    <div class="mobile-compare-detail">
                                        <span class="mobile-compare-detail-label">الفئة:</span>
                                        <span class="mobile-compare-detail-value">${product.categoryName || 'غير محدد'}</span>
                                    </div>
                                    <div class="mobile-compare-detail">
                                        <span class="mobile-compare-detail-label">الحالة:</span>
                                        <span class="mobile-compare-detail-value">${product.isAvailable ? 'متوفر' : 'غير متوفر'}</span>
                                    </div>
                                    ${product.discountPercentage > 0 ? `
                                    <div class="mobile-compare-detail">
                                        <span class="mobile-compare-detail-label">الخصم:</span>
                                        <span class="mobile-compare-detail-value">${product.discountPercentage}%</span>
                                    </div>
                                    ` : ''}
                                    <div class="mobile-compare-detail">
                                        <span class="mobile-compare-detail-label">الوصف:</span>
                                        <span class="mobile-compare-detail-value">${product.description ? product.description.substring(0, 50) + '...' : 'لا يوجد وصف'}</span>
                                    </div>
                                </div>
                            </div>
                        `;
                    });

                    mobileHtml += `
                            </div>
                            <div class="mobile-scroll-indicator">
                                <i class="bi bi-arrow-left"></i>
                                اسحب للتنقل بين المنتجات
                                <i class="bi bi-arrow-right"></i>
                            </div>
                        </div>
                    `;

                    compareContainer.innerHTML = html + mobileHtml;

                    // Inicializar botones de favoritos
                    initWishlistButtons();

                    // تحسين تجربة السحب للهواتف
                    initMobileSwipe();

                    // تحسين تجربة السحب للجدول الرئيسي
                    initDesktopSwipe();
                })
                .catch(error => {
                    console.error('Error al cargar productos:', error);
                    compareContainer.innerHTML = `
                        <div class="alert alert-danger">
                            <div class="d-flex align-items-center justify-content-center">
                                <i class="bi bi-exclamation-triangle-fill fs-3 me-3"></i>
                                <div>
                                    <h5 class="alert-heading mb-1">حدث خطأ</h5>
                                    <p class="mb-0">لم نتمكن من تحميل منتجات المقارنة. يرجى المحاولة مرة أخرى.</p>
                                    <button class="btn btn-primary mt-3" onclick="loadCompareProducts()">إعادة المحاولة</button>
                                </div>
                            </div>
                        </div>
                    `;
                });
        }

        function removeProductFromCompare(productId, button) {
            console.log('محاولة إزالة المنتج:', productId);
            console.log('الزر:', button);

            // إضافة تأثير بصري فوري
            if (button) {
                button.classList.add('removing');
                button.disabled = true;
                console.log('تم تطبيق التأثير البصري');
            }

            // إرسال طلب إزالة المنتج
            const token = document.querySelector('input[name="__RequestVerificationToken"]')?.value;
            console.log('CSRF Token:', token);
            console.log('إرسال طلب إلى:', '/Compare/AddToCompare');

            fetch('/Compare/AddToCompare', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `productId=${productId}&__RequestVerificationToken=${encodeURIComponent(token || '')}`
            })
            .then(response => response.json())
            .then(data => {
                console.log('استجابة الخادم:', data);

                if (data.success) {
                    // إزالة المنتج من الواجهة
                    const productCell = button.closest('.compare-product-cell');
                    if (productCell) {
                        productCell.style.opacity = '0';
                        productCell.style.transform = 'scale(0.8)';

                        setTimeout(() => {
                            // إعادة تحميل المقارنة
                            loadCompareProducts();
                        }, 300);
                    }

                    // تحديث العداد
                    if (window.updateCompareCount) {
                        window.updateCompareCount();
                    }

                    // إظهار رسالة نجاح
                    showToast('تم حذف المنتج من المقارنة', 'success');
                } else {
                    console.error('فشل في إزالة المنتج:', data.message);
                    showToast('حدث خطأ أثناء حذف المنتج', 'error');

                    // إعادة تفعيل الزر
                    if (button) {
                        button.classList.remove('removing');
                        button.disabled = false;
                    }
                }
            })
            .catch(error => {
                console.error('خطأ في الشبكة:', error);
                showToast('حدث خطأ في الاتصال', 'error');

                // إعادة تفعيل الزر
                if (button) {
                    button.classList.remove('removing');
                    button.disabled = false;
                }
            });
        }

        function removeFromCompareLocal(productId, button) {
            // إزالة محلية كـ fallback
            const productCell = button.closest('.compare-product-cell');
            if (productCell) {
                productCell.style.opacity = '0.5';
                setTimeout(() => {
                    loadCompareProducts();
                }, 300);
            }
            showToast('تمت إزالة المنتج من المقارنة');
        }

        function clearCompare() {
            if (confirm('هل أنت متأكد من حذف جميع المنتجات من المقارنة؟')) {
                fetch('/Compare/ClearCompare', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        loadCompareProducts();
                        if (window.updateCompareCount) {
                            window.updateCompareCount();
                        }
                        showToast('تم حذف جميع المنتجات من المقارنة');
                    } else {
                        showToast('حدث خطأ أثناء حذف المقارنة', 'error');
                    }
                })
                .catch(error => {
                    console.error('خطأ في حذف المقارنة:', error);
                    showToast('حدث خطأ أثناء حذف المقارنة', 'error');
                });

                // إطلاق حدث تحديث المقارنة
                $(document).trigger('compare:updated');
            }
        }

        // ملاحظة: تم نقل دالة addToCart إلى ملف product-actions.js لتجنب التكرار

        function addToWishlist(productId, button) {
            // Obtener lista actual
            let wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];

            // Alternar estado
            if (wishlist.includes(productId.toString())) {
                wishlist = wishlist.filter(id => id != productId);
                button.classList.remove('active');
                showToast('تمت إزالة المنتج من المفضلة');
            } else {
                wishlist.push(productId.toString());
                button.classList.add('active');
                showToast('تمت إضافة المنتج إلى المفضلة');
            }

            // Guardar lista actualizada
            localStorage.setItem('wishlist', JSON.stringify(wishlist));
        }

        function initWishlistButtons() {
            const wishlistButtons = document.querySelectorAll('.add-to-wishlist-btn');
            const wishlist = JSON.parse(localStorage.getItem('wishlist')) || [];

            wishlistButtons.forEach(button => {
                const productId = button.getAttribute('data-product-id');
                if (wishlist.includes(productId)) {
                    button.classList.add('active');
                }
            });
        }

        // ملاحظة: تم نقل دالة updateCartCount إلى ملف product-actions.js لتجنب التكرار

        function showToast(message, type = 'success') {
            // استخدام دالة showToast من compare-actions.js إذا كانت متوفرة
            if (window.showToast && typeof window.showToast === 'function') {
                window.showToast(message, type);
                return;
            }

            // fallback: إنشاء toast محلي
            let toast = document.getElementById('toast-notification');
            if (!toast) {
                toast = document.createElement('div');
                toast.id = 'toast-notification';
                toast.style.position = 'fixed';
                toast.style.bottom = '20px';
                toast.style.right = '20px';
                toast.style.backgroundColor = type === 'error' ? 'rgba(220, 53, 69, 0.9)' : 'rgba(106, 13, 173, 0.9)';
                toast.style.color = 'white';
                toast.style.padding = '12px 20px';
                toast.style.borderRadius = '4px';
                toast.style.zIndex = '1000';
                toast.style.transition = 'opacity 0.5s ease-in-out';
                toast.style.opacity = '0';
                toast.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
                document.body.appendChild(toast);
            }

            // عرض الرسالة
            toast.textContent = message;
            toast.style.backgroundColor = type === 'error' ? 'rgba(220, 53, 69, 0.9)' : 'rgba(106, 13, 173, 0.9)';
            toast.style.opacity = '1';

            // إخفاء بعد 3 ثوان
            setTimeout(() => {
                toast.style.opacity = '0';
            }, 3000);
        }

        // تحسين تجربة السحب للجدول الرئيسي
        function initDesktopSwipe() {
            const scrollContainer = document.querySelector('.compare-products-scroll');
            const indicator = document.querySelector('.scroll-indicator');

            if (!scrollContainer) return;

            let isDown = false;
            let startX;
            let scrollLeft;

            // إظهار مؤشر السحب عند وجود محتوى قابل للسحب
            if (scrollContainer.scrollWidth > scrollContainer.clientWidth) {
                if (indicator) {
                    indicator.classList.add('show');
                    setTimeout(() => {
                        indicator.classList.remove('show');
                    }, 3000);
                }
            }

            // إضافة مؤشرات بصرية للسحب
            scrollContainer.addEventListener('mousedown', (e) => {
                isDown = true;
                scrollContainer.classList.add('dragging');
                startX = e.pageX - scrollContainer.offsetLeft;
                scrollLeft = scrollContainer.scrollLeft;
                e.preventDefault();
            });

            scrollContainer.addEventListener('mouseleave', () => {
                isDown = false;
                scrollContainer.classList.remove('dragging');
            });

            scrollContainer.addEventListener('mouseup', () => {
                isDown = false;
                scrollContainer.classList.remove('dragging');
            });

            scrollContainer.addEventListener('mousemove', (e) => {
                if (!isDown) return;
                e.preventDefault();
                const x = e.pageX - scrollContainer.offsetLeft;
                const walk = (x - startX) * 2;
                scrollContainer.scrollLeft = scrollLeft - walk;
            });

            // دعم اللمس للأجهزة اللوحية
            let touchStartX = 0;
            let touchScrollLeft = 0;

            scrollContainer.addEventListener('touchstart', (e) => {
                touchStartX = e.touches[0].clientX;
                touchScrollLeft = scrollContainer.scrollLeft;
            });

            scrollContainer.addEventListener('touchmove', (e) => {
                if (!touchStartX) return;
                const touchX = e.touches[0].clientX;
                const walk = (touchStartX - touchX) * 1.5;
                scrollContainer.scrollLeft = touchScrollLeft + walk;
            });

            scrollContainer.addEventListener('touchend', () => {
                touchStartX = 0;
            });
        }

        // تحسين تجربة السحب للهواتف
        function initMobileSwipe() {
            const scrollContainer = document.querySelector('.mobile-compare-scroll');
            if (!scrollContainer) return;

            let isDown = false;
            let startX;
            let scrollLeft;

            // إضافة مؤشرات بصرية للسحب
            scrollContainer.addEventListener('mousedown', (e) => {
                isDown = true;
                scrollContainer.classList.add('active');
                startX = e.pageX - scrollContainer.offsetLeft;
                scrollLeft = scrollContainer.scrollLeft;
            });

            scrollContainer.addEventListener('mouseleave', () => {
                isDown = false;
                scrollContainer.classList.remove('active');
            });

            scrollContainer.addEventListener('mouseup', () => {
                isDown = false;
                scrollContainer.classList.remove('active');
            });

            scrollContainer.addEventListener('mousemove', (e) => {
                if (!isDown) return;
                e.preventDefault();
                const x = e.pageX - scrollContainer.offsetLeft;
                const walk = (x - startX) * 2;
                scrollContainer.scrollLeft = scrollLeft - walk;
            });

            // دعم اللمس للهواتف
            let touchStartX = 0;
            let touchScrollLeft = 0;

            scrollContainer.addEventListener('touchstart', (e) => {
                touchStartX = e.touches[0].clientX;
                touchScrollLeft = scrollContainer.scrollLeft;
            });

            scrollContainer.addEventListener('touchmove', (e) => {
                if (!touchStartX) return;
                const touchX = e.touches[0].clientX;
                const walk = (touchStartX - touchX) * 1.5;
                scrollContainer.scrollLeft = touchScrollLeft + walk;
            });

            scrollContainer.addEventListener('touchend', () => {
                touchStartX = 0;
            });

            // إضافة مؤشرات التمرير
            const items = scrollContainer.querySelectorAll('.mobile-compare-item');
            if (items.length > 1) {
                // إظهار مؤشر السحب لثواني قليلة
                const indicator = document.querySelector('.mobile-scroll-indicator');
                if (indicator) {
                    setTimeout(() => {
                        indicator.style.opacity = '0.5';
                    }, 2000);

                    setTimeout(() => {
                        indicator.style.opacity = '0';
                    }, 5000);
                }
            }
        }
    </script>
}
