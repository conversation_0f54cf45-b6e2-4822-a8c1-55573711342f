@using Abayat.Models
@model List<Category>

@{
    ViewData["Title"] = "الصفحة الرئيسية";
}

@section Styles {
    <link rel="stylesheet" href="~/css/product-display.css" asp-append-version="true" />
}

<style>


    /* تنسيق قسم جميع المنتجات */
    .all-products {
        margin-top: 3rem;
        margin-bottom: 3rem;
    }

    .cta-section {
        background-color: #6a0dad;
        color: white;
        padding: 4rem 0;
        margin-top: 3rem;
        border-radius: 30px;
        box-shadow: 0 10px 30px rgba(106, 13, 173, 0.3);
    }

    .cta-title {
        font-size: 2.5rem;
        margin-bottom: 2rem;
        font-weight: bold;
    }

    .feature-list {
        list-style: none;
        padding: 0;
        margin-bottom: 2rem;
    }

    .feature-list li {
        margin-bottom: 1rem;
        font-size: 1.2rem;
    }

    .feature-list li i {
        color: white;
        margin-left: 0.5rem;
    }

    .btn-accent {
        background-color: white;
        color: #6a0dad;
        border: 2px solid white;
        padding: 0.75rem 2rem;
        font-weight: bold;
        transition: all 0.3s ease;
    }

    .btn-accent:hover {
        background-color: transparent;
        color: white;
    }

    .section-title {
        color: #6a0dad;
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 2rem;
        text-align: center;
        position: relative;
        padding-bottom: 1rem;
    }

    .section-title::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 100px;
        height: 4px;
        background-color: #6a0dad;
        border-radius: 2px;
    }

    .category-card {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        position: relative;
    }

    .category-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 30px rgba(106, 13, 173, 0.2);
    }

    .category-card-img {
        width: 100%;
        height: 250px;
        object-fit: cover;
    }

    .category-card-overlay {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        background: linear-gradient(to top, rgba(106, 13, 173, 0.9), transparent);
        padding: 1.5rem;
        color: white;
        transition: all 0.3s ease;
    }

    .category-card:hover .category-card-overlay {
        background: linear-gradient(to top, rgba(106, 13, 173, 1), rgba(106, 13, 173, 0.7));
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }

    .category-card-title {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }

    .category-card-btn {
        display: inline-block;
        background-color: white;
        color: #6a0dad;
        padding: 0.5rem 1.5rem;
        border-radius: 50px;
        text-decoration: none;
        font-weight: bold;
        margin-top: 1rem;
        transition: all 0.3s ease;
    }

    .category-card-btn:hover {
        background-color: transparent;
        color: white;
        border: 1px solid white;
    }

    .card {
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        border: none;
    }

    .card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 30px rgba(106, 13, 173, 0.2);
    }

    .card-img-container {
        position: relative;
        overflow: hidden;
    }

    .card-img-top {
        height: 200px;
        object-fit: cover;
        transition: all 0.5s ease;
    }

    .effect-zoom:hover .card-img-top {
        transform: scale(1.1);
    }

    .card-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        padding: 0.25rem 0.75rem;
        border-radius: 50px;
        font-size: 0.8rem;
        font-weight: bold;
        z-index: 2;
    }

    .bg-success {
        background-color: rgba(106, 13, 173, 0.1) !important;
        color: #6a0dad !important;
    }

    .card-body {
        padding: 1.5rem;
    }

    .card-category {
        display: inline-block;
        font-size: 0.8rem;
        background-color: rgba(106, 13, 173, 0.1);
        color: #6a0dad;
        padding: 0.25rem 0.75rem;
        border-radius: 50px;
        margin-bottom: 0.5rem;
    }

    .card-title {
        font-size: 1.25rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
        color: #333;
    }

    .card-price {
        font-size: 1.5rem;
        font-weight: bold;
        color: #6a0dad;
        margin-top: 0.5rem;
    }

    .card-footer {
        background-color: transparent;
        border-top: 1px solid rgba(0, 0, 0, 0.05);
        padding: 1rem 1.5rem;
    }

    .card-actions {
        display: flex;
        justify-content: space-between;
    }

    .card-btn {
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-size: 0.9rem;
    }

    .btn-primary {
        background-color: #6a0dad;
        border-color: #6a0dad;
    }

    .btn-primary:hover {
        background-color: #5a0b93;
        border-color: #5a0b93;
    }

    .btn-success {
        background-color: #28a745;
        border-color: #28a745;
    }

    .btn-success:hover {
        background-color: #218838;
        border-color: #1e7e34;
    }
</style>





<!-- شريط Carousel - Estilo Ameera boutique - فقط الصور الأساسية -->
<section class="carousel-section">
    <div class="carousel-container">
        <div id="productCarousel" class="carousel slide" data-bs-ride="carousel">
        @if (ViewBag.CarouselImages != null && ViewBag.CarouselImages.Count > 0)
        {
            <!-- عرض الصور الخارجية من جدول CarouselImages -->
            
            
            <div class="carousel-inner">
                @for (int i = 0; i < ViewBag.CarouselImages.Count; i++)
                {
                    var image = ViewBag.CarouselImages[i];
                    <div class=" @(i == 0 ? "active" : "")">
                        @if (!string.IsNullOrEmpty(image.LinkUrl))
                        {
                            <a href="@image.LinkUrl" target="_blank">
                                <img src="@image.ImageUrl" class="d-block w-100 carousel-img0" alt="@image.Title">
                                @if (!string.IsNullOrEmpty(image.Description))
                                {
                                    
                                }
                            </a>
                        }
                        else
                        {
                            <img src="@image.ImageUrl" class="d-block w-100 carousel-img" alt="@image.Title">
                            @if (!string.IsNullOrEmpty(image.Description))
                            {
                                 
                            }
                        }
                    </div>
                }
            </div>
        }
        else
        {
            <!-- عرض صورة افتراضية إذا لم تكن هناك صور -->
            <div class="carousel-inner">
                <div class="carousel-item active">
                    <img src="/images/default-carousel.jpg" class="d-block w-100 carousel-img" alt="راعي المخور" onerror="this.src='https://placehold.co/1200x400/e83e8c/ffffff?text=راعي المخور'">
                </div>
            </div>
        }

        <button class="carousel-control-prev" type="button" data-bs-target="#productCarousel" data-bs-slide="prev">
            <span class="carousel-control-prev-icon" aria-hidden="true"></span>
            <span class="visually-hidden">السابق</span>
        </button>
        <button class="carousel-control-next" type="button" data-bs-target="#productCarousel" data-bs-slide="next">
            <span class="carousel-control-next-icon" aria-hidden="true"></span>
            <span class="visually-hidden">التالي</span>
        </button>
        </div>
    </div>
</section>

<!-- قسم منتجات المكياج -->
<section class="all-products">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="section-title animate__animated animate__fadeIn mb-0">جميع المنتجات</h2>

            <!-- أزرار تحديد العرض -->
            <div class="view-options">
                <!-- زر تحديد العرض للهواتف -->
                

                <!-- أيقونات العرض للشاشات الكبيرة -->
                <div class="view-mode d-none d-lg-flex">
                    <div class="view-mode-option" data-view="grid-6" title="6 منتجات في الصف">
                        <i class="bi bi-grid-3x3"></i>
                    </div>
                    <div class="view-mode-option" data-view="grid-4" title="4 منتجات في الصف">
                        <i class="bi bi-grid-fill"></i>
                    </div>
                    <div class="view-mode-option active" data-view="grid-2" title="منتجان في الصف">
                        <i class="bi bi-layout-split"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="products-grid product-grid">
            @if (ViewBag.AllProducts != null && ViewBag.AllProducts.Count > 0)
            {
                foreach (var product in ViewBag.AllProducts)
                {
                    <div class="product-item animate__animated animate__fadeInUp">
                        <div class="product-item-img-container" data-product-id="@product.Id" style="cursor: pointer;">
                            @if (!string.IsNullOrEmpty(product.ImageUrl))
                            {
                                <img src="@product.ImageUrl" class="product-item-img" alt="@product.Name">
                            }
                            else
                            {
                                <img src="https://placehold.co/600x400/6a0dad/ffffff?text=@product.Name" class="product-item-img" alt="@product.Name">
                            }

                            <div class="product-item-discount">-@product.DiscountPercentage%</div>

                            <div class="product-item-availability @(product.IsAvailable ? "available" : "not-available")">
                                @(product.IsAvailable ? "متوفر" : "غير متوفر")
                            </div>

                            <!-- زر المفضلة في الزاوية العلوية -->
                            

                            <div class="product-item-actions">
                            <button class="add-to-wishlist-btn" data-product-id="@product.Id" title="إضافة للمفضلة">
                                <i class="bi bi-heart"></i>
                            </button>
                                <button class="product-item-action-btn add-to-cart-btn" data-product-id="@product.Id" title="إضافة للسلة">
                                    <i class="bi bi-cart-plus"></i>
                                </button>
                                <button class="product-item-action-btn add-to-compare-btn" data-product-id="@product.Id" title="إضافة للمقارنة">
                                    <i class="bi bi-arrow-left-right"></i>
                                </button>
                            </div>
                        </div>
                        <div class="product-item-info">
                            <h3 class="product-item-title">@product.Name</h3>
                            <div class="product-item-price">
                                <div class="product-item-price-current">@product.DiscountedPrice.ToString("N0") ر.ع</div>
                                <div class="product-item-price-original">@product.Price.ToString("N0") ر.ع</div>
                            </div>
                        </div>
                        <a href="/Products/Details/@product.Id" class="product-link"></a>
                    </div>
                }
            }
            else
            {
                <div class="col-12 text-center">
                    <p class="lead">لا توجد منتجات متاحة حالياً</p>
                </div>
            }
        </div>
    </div>
</section>

<section class="featured-products">
    <div class="container">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="section-title animate__animated animate__fadeIn mb-0">الأكثر مبيعاً</h2>

            <!-- أزرار تحديد العرض -->
            <div class="view-options">
                <!-- زر تحديد العرض للهواتف -->
                

                <!-- أيقونات العرض للشاشات الكبيرة -->
                <div class="view-mode d-none d-lg-flex">
                    <div class="view-mode-option" data-view="grid-6" title="6 منتجات في الصف">
                        <i class="bi bi-grid-3x3"></i>
                    </div>
                    <div class="view-mode-option" data-view="grid-4" title="4 منتجات في الصف">
                        <i class="bi bi-grid-fill"></i>
                    </div>
                    <div class="view-mode-option active" data-view="grid-2" title="منتجان في الصف">
                        <i class="bi bi-layout-split"></i>
                    </div>
                </div>
            </div>
        </div>

        <div class="products-grid product-grid">
            @if (ViewBag.FeaturedProducts != null)
            {
                foreach (var product in ViewBag.FeaturedProducts)
                {
                    <div class="product-item animate__animated animate__fadeInUp">
                        <div class="product-item-img-container" data-product-id="@product.Id" style="cursor: pointer;">
                            @if (!string.IsNullOrEmpty(product.ImageUrl))
                            {
                                <img src="@product.ImageUrl" class="product-item-img" alt="@product.Name">
                            }
                            else
                            {
                                <img src="https://placehold.co/600x400/6a0dad/ffffff?text=@product.Name" class="product-item-img" alt="@product.Name">
                            }

                            <div class="product-item-discount">-@product.DiscountPercentage%</div>

                            <div class="product-item-availability @(product.IsAvailable ? "available" : "not-available")">
                                @(product.IsAvailable ? "متوفر" : "غير متوفر")
                            </div>

                            <!-- زر المفضلة في الزاوية العلوية -->
                            

                            <div class="product-item-actions">
                                 <button class="product-item-action-btn add-to-wishlist-btn" data-product-id="@product.Id" title="إضافة للمفضلة">
                                      <i class="bi bi-heart"></i>
                                 </button>
                                <button class="product-item-action-btn add-to-cart-btn" data-product-id="@product.Id" title="إضافة للسلة">
                                    <i class="bi bi-cart-plus"></i>
                                </button>
                                <button class="product-item-action-btn add-to-compare-btn" data-product-id="@product.Id" title="إضافة للمقارنة">
                                    <i class="bi bi-arrow-left-right"></i>
                                </button>
                            </div>
                        </div>
                        <div class="product-item-info">
                            <h3 class="product-item-title">@product.Name</h3>
                            <div class="product-item-price">
                                <div class="product-item-price-current">@product.DiscountedPrice.ToString("N0") ر.ع</div>
                                <div class="product-item-price-original">@product.Price.ToString("N0") ر.ع</div>
                            </div>
                        </div>
                        <a href="/Products/Details/@product.Id" class="product-link"></a>
                    </div>
                }
            }
            else
            {
                <div class="col-12 text-center">
                    <p class="lead">لا توجد منتجات في قائمة الأكثر مبيعاً حالياً</p>
                    <p class="text-muted">يمكن للمدير إضافة منتجات إلى قائمة الأكثر مبيعاً من لوحة التحكم</p>
                </div>
            }
        </div>

        <div class="text-center mt-4">
            <a asp-controller="Products" asp-action="Index" class="btn btn-outline-primary btn-lg">
                <i class="bi bi-grid me-2"></i> عرض جميع المنتجات
            </a>
        </div>
    </div>
</section>

<section class="cta-section">
    <div class="container">
        <div class="cta-content">
            <h2 class="cta-title animate__animated animate__fadeIn">لماذا تختارين راعي المخور؟</h2>
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <ul class="feature-list animate__animated animate__fadeInUp">
                        <li><i class="bi bi-check-circle-fill"></i> تصاميم عصرية وأنيقة تناسب جميع الأذواق</li>
                        <li><i class="bi bi-check-circle-fill"></i> أقمشة عالية الجودة ومريحة للاستخدام اليومي</li>
                        <li><i class="bi bi-check-circle-fill"></i> خيارات متنوعة من الألوان والتصاميم</li>
                        <li><i class="bi bi-check-circle-fill"></i> شحن سريع لجميع مناطق المملكة</li>
                        <li><i class="bi bi-check-circle-fill"></i> إمكانية الاستبدال والاسترجاع خلال 14 يوم</li>
                    </ul>
                </div>
            </div>
            <div class="cta-buttons animate__animated animate__fadeInUp animate__delay-1s">
                <a asp-controller="Products" asp-action="Index" class="btn btn-accent btn-lg">تسوق الآن</a>
                <a asp-controller="Home" asp-action="Privacy" class="btn btn-outline-light btn-lg ms-2">المزيد عنا</a>
            </div>
        </div>
    </div>
</section>

@section Scripts {
    <script src="~/js/display-options.js" asp-append-version="true"></script>
    <script>
        $(document).ready(function () {
            // تحديث عداد السلة والمبلغ الإجمالي عند تحميل الصفحة الرئيسية
            if (typeof updateCartCount === 'function') {
                updateCartCount();
            }
            if (typeof updateCartTotal === 'function') {
                updateCartTotal();
            }

            // تفعيل أزرار تحديد العرض
            initHomeViewControls();
        });

        function initHomeViewControls() {
            const productGrids = document.querySelectorAll('.product-grid');
            const viewModeOptions = document.querySelectorAll('.view-mode-option');

            // أحداث أزرار العرض
            viewModeOptions.forEach(option => {
                option.addEventListener('click', function() {
                    const view = this.getAttribute('data-view');
                    console.log('Changing view to:', view);

                    // إزالة الفئات النشطة
                    viewModeOptions.forEach(o => o.classList.remove('active'));

                    // تطبيق النمط الجديد
                    this.classList.add('active');

                    // تطبيق التغيير على جميع الشبكات
                    productGrids.forEach(grid => {
                        // إزالة الفئات السابقة
                        grid.classList.remove('grid-6', 'grid-4', 'grid-2', 'items-per-row-6', 'items-per-row-4', 'items-per-row-2');

                        // إضافة الفئة الجديدة
                        grid.classList.add(view);

                        // إضافة فئة items-per-row أيضاً
                        if (view === 'grid-6') {
                            grid.classList.add('items-per-row-6');
                        } else if (view === 'grid-4') {
                            grid.classList.add('items-per-row-4');
                        } else if (view === 'grid-2') {
                            grid.classList.add('items-per-row-2');
                        }
                    });

                    // حفظ التفضيل
                    localStorage.setItem('homeViewMode', view);
                });
            });

            // تحميل التفضيل المحفوظ
            const savedView = localStorage.getItem('homeViewMode') || 'grid-2';
            const savedViewOption = document.querySelector(`.view-mode-option[data-view="${savedView}"]`);

            if (savedViewOption) {
                savedViewOption.click();
            }
        }

        // وظيفة النقر على صورة المنتج
        document.addEventListener('DOMContentLoaded', function() {
            // منع تداخل النقر على الصورة مع أزرار الإجراءات
            const actionButtons = document.querySelectorAll('.product-item-action-btn, .product-action-btn');
            actionButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.stopPropagation();
                });
            });

            // إضافة وظيفة النقر على صورة المنتج
            const productImages = document.querySelectorAll('.product-item-img-container');
            productImages.forEach(container => {
                container.addEventListener('click', function(e) {
                    // التأكد من عدم النقر على أزرار الإجراءات
                    if (!e.target.closest('.product-item-actions') &&
                        !e.target.closest('.product-actions-menu') &&
                        !e.target.closest('.product-item-action-btn') &&
                        !e.target.closest('.product-action-btn')) {

                        // الحصول على معرف المنتج
                        const productId = container.getAttribute('data-product-id');

                        if (productId) {
                            // إضافة تأثير بصري
                            container.style.transform = 'scale(0.95)';

                            // الانتقال لصفحة التفاصيل
                            setTimeout(() => {
                                window.location.href = '/Products/Details/' + productId;
                            }, 150);
                        }
                    }
                });
            });

            // إضافة تأثير hover للصور
            productImages.forEach(container => {
                container.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.02)';
                });

                container.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });
        });
    </script>
}


