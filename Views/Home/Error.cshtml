@model Abayat.Models.ErrorViewModel
@{
    ViewData["Title"] = "خطأ";
}

<div class="error-page">
    <div class="container py-5">
        <div class="error-container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="error-content slide-in-left">
                        <div class="error-icon">
                            <i class="bi bi-exclamation-triangle-fill"></i>
                        </div>
                        <h1 class="error-title">عذراً، حدث خطأ!</h1>
                        <p class="error-message">نعتذر عن هذا الخطأ. يبدو أن هناك مشكلة أثناء معالجة طلبك.</p>

                        @if (Model.ShowRequestId)
                        {
                            <div class="error-details">
                                <p><strong>رقم الطلب:</strong> <code>@Model.RequestId</code></p>
                            </div>
                        }

                        <div class="error-actions">
                            <a href="/" class="btn btn-primary error-btn">
                                <i class="bi bi-house-fill me-2"></i> العودة للصفحة الرئيسية
                            </a>
                            <a href="/Categories" class="btn btn-outline-primary error-btn">
                                <i class="bi bi-grid-fill me-2"></i> تصفح الفئات
                            </a>
                        </div>

                        <div class="error-support mt-4">
                            <h5><i class="bi bi-headset me-2"></i> هل تحتاج إلى مساعدة؟</h5>
                            <p>يمكنك التواصل مع فريق الدعم الفني على البريد الإلكتروني: <a href="mailto:<EMAIL>"><EMAIL></a></p>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="error-image zoom-in">
                        <img src="/images/error-illustration.svg" alt="خطأ" onerror="this.src='/images/error-fallback.png'" class="img-fluid">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
