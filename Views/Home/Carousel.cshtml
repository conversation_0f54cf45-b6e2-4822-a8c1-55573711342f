@model IEnumerable<Abayat.Models.Product>

@{
    ViewData["Title"] = "إدارة الشريط المتحرك";
}

<div class="products-header text-center">
    <div class="container">
        <h1 class="fade-in">@ViewData["Title"]</h1>
        <p class="lead mb-4">إدارة المنتجات التي تظهر في الشريط المتحرك في الصفحة الرئيسية</p>
    </div>
</div>

<div class="container py-5">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="alert alert-info">
                <h4 class="alert-heading"><i class="bi bi-info-circle"></i> معلومات عن الشريط المتحرك</h4>
                <p>يمكنك تحديد المنتجات التي تظهر في الشريط المتحرك من خلال تفعيل خيار "عرض في الشريط المتحرك" عند إضافة أو تعديل المنتج.</p>
                <p>المنتجات المعروضة أدناه هي المنتجات التي تم تفعيل خيار "عرض في الشريط المتحرك" لها.</p>
            </div>
        </div>
    </div>

    <div class="table-responsive">
        <table class="table table-striped table-hover">
            <thead class="table-dark">
                <tr>
                    <th>الصورة</th>
                    <th>اسم المنتج</th>
                    <th>الفئة</th>
                    <th>السعر</th>
                    <th>متوفر</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                @foreach (var product in Model)
                {
                    <tr>
                        <td>
                            @if (!string.IsNullOrEmpty(product.ImageUrl))
                            {
                                <img src="@product.ImageUrl" alt="@product.Name" style="max-width: 100px; max-height: 60px;" class="img-thumbnail" />
                            }
                            else
                            {
                                <span class="text-muted">لا توجد صورة</span>
                            }
                        </td>
                        <td>@product.Name</td>
                        <td>@product.Category.Name</td>
                        <td><span class="currency">ر.ع</span> @product.Price.ToString("N0")</td>
                        <td>
                            @if (product.IsAvailable)
                            {
                                <span class="badge bg-success">متوفر</span>
                            }
                            else
                            {
                                <span class="badge bg-danger">غير متوفر</span>
                            }
                        </td>
                        <td>
                            <div class="btn-group" role="group">
                                <a asp-controller="Products" asp-action="Edit" asp-route-id="@product.Id" class="btn btn-sm btn-primary">
                                    <i class="bi bi-pencil"></i> تعديل
                                </a>
                                <a asp-controller="Products" asp-action="Details" asp-route-id="@product.Id" class="btn btn-sm btn-info">
                                    <i class="bi bi-eye"></i> تفاصيل
                                </a>
                            </div>
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>

    @if (!Model.Any())
    {
        <div class="alert alert-warning text-center mt-4">
            <i class="bi bi-exclamation-triangle fs-4"></i>
            <p class="mb-0 mt-2">لا توجد منتجات معروضة في الشريط المتحرك حالياً. قم بتفعيل خيار "عرض في الشريط المتحرك" للمنتجات التي ترغب في عرضها.</p>
        </div>
    }

    <div class="mt-4">
        <a asp-controller="Products" asp-action="Index" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-right"></i> العودة إلى قائمة المنتجات
        </a>
    </div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
</div>
