@model Abayat.ViewModels.ManageFeaturedProductsViewModel

@{
    ViewData["Title"] = "إدارة المنتجات الأكثر مبيعاً";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h3 class="card-title mb-0">
                                <i class="fas fa-trophy me-2"></i>
                                المنتجات الأكثر مبيعاً
                            </h3>
                        </div>
                        <div class="col-md-6 text-end">
                            <div class="d-flex justify-content-end align-items-center gap-2">
                                <button type="button" class="btn btn-success btn-sm" id="addToBestSellersBtn"
                                        @(!Model.CanAddMore ? "disabled" : "")>
                                    <i class="fas fa-plus me-1"></i>
                                    @if (Model.CanAddMore)
                                    {
                                        <span>إضافة منتج للقائمة</span>
                                    }
                                    else
                                    {
                                        <span>تم الوصول للحد الأقصى (5)</span>
                                    }
                                </button>
                                <span class=" bg-light text-dark">@Model.FeaturedProducts.Count من @Model.MaxFeaturedProducts منتج</span>
                                <div class="input-group" style="width: 250px;">
                                    <input type="text" id="searchInput" class="form-control form-control-sm" placeholder="البحث في المنتجات...">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card-body">
                    @if (Model.FeaturedProducts.Any())
                    {
                        <!-- إحصائيات سريعة -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <div class="card bg-primary text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4 class="mb-0">@Model.FeaturedProducts.Sum(x => x.TotalQuantity)</h4>
                                                <p class="mb-0">إجمالي القطع المباعة</p>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="fas fa-shopping-cart fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4 class="mb-0">@Model.FeaturedProducts.Sum(x => x.TotalRevenue).ToString("N0") ر.ع</h4>
                                                <p class="mb-0">إجمالي الإيرادات</p>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="fas fa-dollar-sign fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-info text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4 class="mb-0">@Model.FeaturedProducts.Count(x => x.ShowInCarousel)</h4>
                                                <p class="mb-0">في الشريط المتحرك</p>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="fas fa-images fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning text-white">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <h4 class="mb-0">@Model.FeaturedProducts.Count(x => x.IsAvailable)</h4>
                                                <p class="mb-0">المنتجات المتوفرة</p>
                                            </div>
                                            <div class="align-self-center">
                                                <i class="fas fa-images fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار التصفية السريعة -->
                        <div class="row mb-3">
                            <div class="col-12">
                                <div class="btn-group" role="group" aria-label="تصفية المنتجات">
                                    <button type="button" class="btn btn-outline-primary active" data-filter="all">
                                        <i class="fas fa-list me-1"></i>الكل
                                    </button>
                                    <button type="button" class="btn btn-outline-success" data-filter="available">
                                        <i class="fas fa-check-circle me-1"></i>المتوفرة
                                    </button>
                                    <button type="button" class="btn btn-outline-info" data-filter="carousel">
                                        <i class="fas fa-images me-1"></i>في الشريط المتحرك
                                    </button>
                                    <button type="button" class="btn btn-outline-warning" data-filter="top3">
                                        <i class="fas fa-medal me-1"></i>أفضل 3
                                    </button>
                                </div>
                                <div class="ms-auto">
                                    <button type="button" class="btn btn-success" id="exportBtn">
                                        <i class="fas fa-file-excel me-1"></i>تصدير إلى Excel
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th width="5%">#</th>
                                        <th width="10%">الصورة</th>
                                        <th width="25%">اسم المنتج</th>
                                        <th width="10%">السعر</th>
                                        <th width="10%">الكمية المباعة</th>
                                        <th width="12%">إجمالي الإيرادات</th>
                                        <th width="8%">الحالة</th>
                                        <th width="22%" class="actions-column">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @for (int i = 0; i < Model.FeaturedProducts.Count; i++)
                                    {
                                        var item = Model.FeaturedProducts[i];
                                        <tr>
                                            <td>
                                                @if (i == 0)
                                                {
                                                    <span class="text-dark">🥇 #@(i + 1)</span>
                                                }
                                                else if (i == 1)
                                                {
                                                    <span class="">🥈 #@(i + 1)</span>
                                                }
                                                else if (i == 2)
                                                {
                                                    <span class="">🥉 #@(i + 1)</span>
                                                }
                                                else
                                                {
                                                    <span class="">#@(i + 1)</span>
                                                }
                                            </td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(item.ProductImageUrl))
                                                {
                                                    <img src="@item.ProductImageUrl" alt="@item.ProductName" 
                                                         class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover;">
                                                }
                                                else
                                                {
                                                    <div class="bg-light d-flex align-items-center justify-content-center" 
                                                         style="width: 60px; height: 60px; border-radius: 4px;">
                                                        <i class="fas fa-image text-muted"></i>
                                                    </div>
                                                }
                                            </td>
                                            <td>
                                                <strong>@item.ProductName</strong>
                                            </td>
                                            <td>
                                                <span class="text-success fw-bold">@item.ProductPrice.ToString("N0") ر.ع</span>
                                            </td>
                                            <td>
                                                <span class="">@item.TotalQuantity قطعة</span>
                                            </td>
                                            <td>
                                                <span class="revenue-highlight">@item.TotalRevenue.ToString("N0") ر.ع</span>
                                            </td>
                                            <td>
                                                @if (item.IsAvailable)
                                                {
                                                    <span>متوفر</span>
                                                }
                                                else
                                                {
                                                    <span>غير متوفر</span>
                                                }
                                            </td>
                                            <td>
                                                <div class="btn-group-vertical d-grid gap-1" role="group">
                                                    <!-- الصف الأول: أزرار الإدارة -->
                                                    <div class="btn-group" role="group">
                                                        <!-- زر إضافة/إزالة من الشريط المتحرك -->
                                                        <button type="button"
                                                                class="btn btn-sm toggle-carousel-btn @(item.ShowInCarousel ? "btn-info" : "btn-outline-info")"
                                                                data-product-id="@item.ProductId"
                                                                title="@(item.ShowInCarousel ? "إزالة من الشريط المتحرك" : "إضافة إلى الشريط المتحرك")">
                                                            <i class="fas fa-images me-1"></i>
                                                            @(item.ShowInCarousel ? "إزالة" : "إضافة")
                                                        </button>

                                                        <!-- زر حذف من قائمة المنتجات المميزة -->
                                                        <button type="button"
                                                                class="btn btn-sm btn-outline-danger remove-featured-btn"
                                                                data-product-id="@item.ProductId"
                                                                title="حذف من قائمة المنتجات المميزة">
                                                            <i class="fas fa-trash me-1"></i>
                                                            حذف
                                                        </button>
                                                    </div>

                                                    <!-- الصف الثاني: أزرار العرض والتعديل -->
                                                    <div class="btn-group" role="group">
                                                        <!-- زر عرض تفاصيل المنتج -->
                                                        <a href="/Products/Details/@item.ProductId"
                                                           class="btn btn-sm btn-outline-primary"
                                                           title="عرض تفاصيل المنتج">
                                                            <i class="fas fa-eye me-1"></i>
                                                            عرض
                                                        </a>

                                                        <!-- زر تعديل المنتج -->
                                                        <a href="/Products/Edit/@item.ProductId"
                                                           class="btn btn-sm btn-outline-secondary"
                                                           title="تعديل المنتج">
                                                            <i class="fas fa-edit me-1"></i>
                                                            تعديل
                                                        </a>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <div class="empty-state">
                                <i class="fas fa-chart-line fa-4x text-muted mb-4"></i>
                                <h3 class="text-muted mb-3">لا توجد مبيعات حتى الآن</h3>
                                <p class="text-muted mb-4">عندما يتم بيع المنتجات، ستظهر هنا مرتبة حسب الأكثر مبيعاً</p>
                                <div class="row justify-content-center">
                                    <div class="col-md-6">
                                        <div class="card border-primary">
                                            <div class="card-body text-center">
                                                <h5 class="card-title text-primary">ابدأ بإضافة منتجات</h5>
                                                <p class="card-text">أضف منتجات جديدة لتبدأ في تتبع المبيعات</p>
                                                <a href="/Products/Create" class="btn btn-primary">
                                                    <i class="fas fa-plus me-2"></i>إضافة منتج جديد
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة منبثقة لإضافة منتج إلى قائمة الأكثر مبيعاً -->
<div class="modal fade" id="addProductModal" tabindex="-1" aria-labelledby="addProductModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="addProductModalLabel">
                    <i class="fas fa-plus me-2"></i>
                    إضافة منتج إلى قائمة الأكثر مبيعاً
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="productSearch" class="form-label">البحث عن منتج:</label>
                    <input type="text" class="form-control" id="productSearch" placeholder="اكتب اسم المنتج للبحث...">
                </div>
                <div id="productsList" class="row">
                    <!-- سيتم تحميل المنتجات هنا -->
                </div>
                <div id="loadingProducts" class="text-center d-none">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <style>
        .empty-state {
            padding: 3rem 0;
        }

        .stats-card {
            transition: transform 0.2s ease-in-out;
        }

        .stats-card:hover {
            transform: translateY(-2px);
        }

        .badge.bg-warning.text-dark {
            background-color: #ffc107 !important;
            color: #000 !important;
            font-weight: bold;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(0, 123, 255, 0.05);
        }

        .btn-group .btn {
            margin: 0 1px;
        }

        .btn-group-vertical .btn-group {
            width: 100%;
        }

        .btn-group-vertical .btn-group .btn {
            flex: 1;
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }

        .actions-column {
            min-width: 180px;
        }

        .product-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .product-card:hover {
            border-color: #28a745;
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);
        }

        .product-card.selected {
            border-color: #28a745;
            background-color: #f8fff9;
        }

        .product-rank-1 {
            background: linear-gradient(45deg, #ffd700, #ffed4e);
            color: #000;
            font-weight: bold;
        }

        .product-rank-2 {
            background: linear-gradient(45deg, #c0c0c0, #e5e5e5);
            color: #000;
            font-weight: bold;
        }

        .product-rank-3 {
            background: linear-gradient(45deg, #cd7f32, #daa520);
            color: #fff;
            font-weight: bold;
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .table-dark {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        }

        .revenue-highlight {
            font-weight: bold;
            color: #28a745;
        }

        .quantity-badge {
            background: linear-gradient(45deg, #17a2b8, #20c997);
            border: none;
        }
    </style>
}

@section Scripts {
    <script>
        $(document).ready(function() {
            // وظيفة البحث
            $('#searchInput').on('keyup', function() {
                const searchTerm = $(this).val().toLowerCase();
                $('tbody tr').each(function() {
                    const productName = $(this).find('td:nth-child(3)').text().toLowerCase();
                    if (productName.includes(searchTerm)) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });

                // تحديث عداد النتائج
                const visibleRows = $('tbody tr:visible').length;
                $('.badge.bg-light.text-dark').text(visibleRows + ' منتج');
            });

            // وظائف التصفية
            $('[data-filter]').on('click', function() {
                const filter = $(this).data('filter');

                // تحديث حالة الأزرار
                $('[data-filter]').removeClass('active');
                $(this).addClass('active');

                // تطبيق التصفية
                $('tbody tr').each(function(index) {
                    const row = $(this);
                    let show = true;

                    switch(filter) {
                        case 'available':
                            show = row.find('.badge.bg-success').length > 0;
                            break;
                        case 'carousel':
                            show = row.find('.btn-info:not(.btn-outline-info)').length > 0;
                            break;
                        case 'top3':
                            show = index < 3;
                            break;
                        case 'all':
                        default:
                            show = true;
                            break;
                    }

                    if (show) {
                        row.show();
                    } else {
                        row.hide();
                    }
                });

                // تحديث عداد النتائج
                const visibleRows = $('tbody tr:visible').length;
                $('.badge.bg-light.text-dark').text(visibleRows + ' منتج');
            });

            // تبديل حالة المنتج المميز - معطل مؤقتاً
            /*
            $('.toggle-featured-btn').click(function() {
                const button = $(this);
                const productId = button.data('product-id');

                $.ajax({
                    url: '/Admin/ToggleFeatured',
                    type: 'POST',
                    data: {
                        productId: productId,
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            if (response.isFeatured) {
                                button.removeClass('btn-outline-warning').addClass('btn-warning');
                                button.attr('title', 'إزالة من المميزة');
                            } else {
                                button.removeClass('btn-warning').addClass('btn-outline-warning');
                                button.attr('title', 'إضافة إلى المميزة');
                            }
                            showToast(response.message, 'success');
                        } else {
                            showToast(response.message, 'error');
                        }
                    },
                    error: function() {
                        showToast('حدث خطأ أثناء تحديث المنتج', 'error');
                    }
                });
            });
            */

            // تصدير البيانات إلى Excel
            $('#exportBtn').on('click', function() {
                // إنشاء جدول مؤقت للتصدير
                const table = $('<table>');
                const thead = $('<thead>').append(
                    $('<tr>').append(
                        '<th>الترتيب</th>',
                        '<th>اسم المنتج</th>',
                        '<th>السعر</th>',
                        '<th>الكمية المباعة</th>',
                        '<th>إجمالي الإيرادات</th>',
                        '<th>الحالة</th>',
                        '<th>في الشريط المتحرك</th>'
                    )
                );

                const tbody = $('<tbody>');
                $('tbody tr:visible').each(function(index) {
                    const row = $(this);
                    const rank = row.find('td:first').text().replace(/[🥇🥈🥉]/g, '').trim();
                    const productName = row.find('td:nth-child(3)').text().trim();
                    const price = row.find('td:nth-child(4)').text().trim();
                    const quantity = row.find('td:nth-child(5)').text().trim();
                    const revenue = row.find('td:nth-child(6)').text().trim();
                    const status = row.find('td:nth-child(7) .badge').text().trim();
                    const inCarousel = row.find('.btn-info:not(.btn-outline-info)').length > 0 ? 'نعم' : 'لا';

                    tbody.append(
                        $('<tr>').append(
                            `<td>${rank}</td>`,
                            `<td>${productName}</td>`,
                            `<td>${price}</td>`,
                            `<td>${quantity}</td>`,
                            `<td>${revenue}</td>`,
                            `<td>${status}</td>`,
                            `<td>${inCarousel}</td>`
                        )
                    );
                });

                table.append(thead).append(tbody);

                // تحويل إلى CSV
                let csv = '';
                table.find('tr').each(function() {
                    const row = [];
                    $(this).find('td, th').each(function() {
                        row.push('"' + $(this).text().replace(/"/g, '""') + '"');
                    });
                    csv += row.join(',') + '\n';
                });

                // تنزيل الملف
                const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', 'المنتجات_الأكثر_مبيعا_' + new Date().toISOString().split('T')[0] + '.csv');
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                showToast('تم تصدير البيانات بنجاح', 'success');
            });

            // تحديث عداد المنتجات
            function updateProductCount() {
                const visibleRows = $('tbody tr:visible').length;
                $('.badge.bg-light.text-dark').text(visibleRows + ' منتج');
            }

            // تحميل المنتجات المتاحة
            function loadAvailableProducts(searchTerm = '') {
                $('#loadingProducts').removeClass('d-none');
                $('#productsList').empty();

                // استدعاء AJAX لجلب المنتجات المتاحة
                $.get('/Admin/GetAvailableProducts', { searchTerm: searchTerm })
                    .done(function(products) {
                        $('#loadingProducts').addClass('d-none');

                        if (products.length === 0) {
                            $('#productsList').html(`
                                <div class="col-12 text-center">
                                    <p class="text-muted">لا توجد منتجات متاحة للإضافة</p>
                                </div>
                            `);
                            return;
                        }

                        products.forEach(product => {
                            $('#productsList').append(`
                                <div class="col-md-6">
                                    <div class="product-card" data-product-id="${product.id}">
                                        <div class="row align-items-center">
                                            <div class="col-3">
                                                <img src="${product.imageUrl || '/images/placeholder.jpg'}" alt="${product.name}"
                                                     class="img-fluid rounded" style="height: 60px; object-fit: cover;"
                                                     onerror="this.src='/images/placeholder.jpg'">
                                            </div>
                                            <div class="col-9">
                                                <h6 class="mb-1">${product.name}</h6>
                                                <p class="text-muted mb-0 small">${product.categoryName}</p>
                                                <p class="text-success mb-0 fw-bold">${product.price} ر.ع</p>
                                                <button class="btn btn-sm btn-success mt-1 add-product-btn"
                                                        data-product-id="${product.id}">
                                                    <i class="fas fa-plus me-1"></i>إضافة
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            `);
                        });

                        // إضافة حدث النقر على أزرار الإضافة
                        $('.add-product-btn').click(function() {
                            const button = $(this);
                            const productId = button.data('product-id');
                            const productCard = button.closest('.product-card');
                            const productName = productCard.find('h6').text();

                            button.prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>جاري الإضافة...');

                            $.post('/Admin/AddToFeatured', { productId: productId })
                                .done(function(response) {
                                    if (response.success) {
                                        button.html('<i class="fas fa-check me-1"></i>تمت الإضافة');
                                        setTimeout(() => {
                                            $('#addProductModal').modal('hide');
                                            showToast(response.message, 'success');
                                            location.reload();
                                        }, 1000);
                                    } else {
                                        button.prop('disabled', false).html('<i class="fas fa-plus me-1"></i>إضافة');
                                        showToast(response.message, 'error');
                                    }
                                })
                                .fail(function() {
                                    button.prop('disabled', false).html('<i class="fas fa-plus me-1"></i>إضافة');
                                    showToast('حدث خطأ أثناء إضافة المنتج', 'error');
                                });
                        });
                    })
                    .fail(function() {
                        $('#loadingProducts').addClass('d-none');
                        $('#productsList').html(`
                            <div class="col-12 text-center">
                                <p class="text-danger">حدث خطأ أثناء تحميل المنتجات</p>
                            </div>
                        `);
                    });
            }

            // دالة عرض الرسائل المنبثقة
            function showToast(message, type = 'info') {
                const toastClass = type === 'success' ? 'bg-success' : type === 'error' ? 'bg-danger' : 'bg-info';
                const toast = $(`
                    <div class="toast align-items-center text-white ${toastClass} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                        <div class="d-flex">
                            <div class="toast-body">
                                ${message}
                            </div>
                            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                        </div>
                    </div>
                `);

                // إضافة التوست إلى الصفحة
                if ($('#toastContainer').length === 0) {
                    $('body').append('<div id="toastContainer" class="toast-container position-fixed top-0 end-0 p-3"></div>');
                }
                $('#toastContainer').append(toast);

                // عرض التوست
                const bsToast = new bootstrap.Toast(toast[0]);
                bsToast.show();

                // إزالة التوست بعد إخفاؤه
                toast[0].addEventListener('hidden.bs.toast', () => {
                    toast.remove();
                });
            }

            // فتح نافذة إضافة منتج
            $('#addToBestSellersBtn').click(function() {
                if (!$(this).prop('disabled')) {
                    $('#addProductModal').modal('show');
                    loadAvailableProducts();
                }
            });

            // البحث في المنتجات المتاحة
            $('#productSearch').on('keyup', function() {
                const searchTerm = $(this).val();
                loadAvailableProducts(searchTerm);
            });

            // حذف منتج من قائمة المنتجات المميزة
            $('.remove-featured-btn').click(function() {
                const button = $(this);
                const productId = button.data('product-id');
                const productName = button.closest('tr').find('td:nth-child(3)').text().trim();

                if (confirm(`هل أنت متأكد من حذف "${productName}" من قائمة المنتجات المميزة؟`)) {
                    $.post('/Admin/RemoveFromFeatured', { productId: productId })
                        .done(function(response) {
                            if (response.success) {
                                button.closest('tr').fadeOut(500, function() {
                                    $(this).remove();
                                    updateProductCount();
                                    // إعادة تمكين زر الإضافة إذا كان معطلاً
                                    $('#addToBestSellersBtn').prop('disabled', false)
                                        .find('span').text('إضافة منتج للقائمة');
                                });
                                showToast(response.message, 'success');
                            } else {
                                showToast(response.message, 'error');
                            }
                        })
                        .fail(function() {
                            showToast('حدث خطأ أثناء حذف المنتج', 'error');
                        });
                }
            });

            // تبديل حالة الشريط المتحرك
            $('.toggle-carousel-btn').click(function() {
                const button = $(this);
                const productId = button.data('product-id');
                
                $.ajax({
                    url: '/Admin/ToggleCarouselFromBestSellers',
                    type: 'POST',
                    data: {
                        productId: productId,
                        __RequestVerificationToken: $('input[name="__RequestVerificationToken"]').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            if (response.showInCarousel) {
                                button.removeClass('btn-outline-info').addClass('btn-info');
                                button.attr('title', 'إزالة من الشريط المتحرك');
                            } else {
                                button.removeClass('btn-info').addClass('btn-outline-info');
                                button.attr('title', 'إضافة إلى الشريط المتحرك');
                            }
                            showToast(response.message, 'success');
                        } else {
                            showToast(response.message, 'error');
                        }
                    },
                    error: function() {
                        showToast('حدث خطأ أثناء تحديث المنتج', 'error');
                    }
                });
            });
        });
        
        function showToast(message, type) {
            // إنشاء عنصر التوست
            const toast = $(`
                <div class="toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0" role="alert">
                    <div class="d-flex">
                        <div class="toast-body">
                            ${message}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                </div>
            `);
            
            // إضافة التوست إلى الصفحة
            if ($('.toast-container').length === 0) {
                $('body').append('<div class="toast-container position-fixed top-0 end-0 p-3"></div>');
            }
            
            $('.toast-container').append(toast);
            
            // إظهار التوست
            const bsToast = new bootstrap.Toast(toast[0]);
            bsToast.show();
            
            // إزالة التوست بعد إخفائه
            toast.on('hidden.bs.toast', function() {
                $(this).remove();
            });
        }
    </script>
}

<form method="post" style="display: none;">
    @Html.AntiForgeryToken()
</form>
