@model List<Abayat.Models.CarouselImage>
@{
    ViewData["Title"] = "إدارة صور الشريط المتحرك";
}

<div class="container py-5">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="admin-dashboard-title">إدارة صور الشريط المتحرك</h1>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCarouselImageModal">
            <i class="bi bi-plus-lg"></i> إضافة صورة جديدة
        </button>
    </div>

    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <div class="admin-card mb-4">
        <div class="admin-card-header">
            <h4 class="admin-card-title">صور الشريط المتحرك</h4>
        </div>
        <div class="admin-card-body">
            @if (Model.Any())
            {
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>العنوان</th>
                                <th>الوصف</th>
                                <th>الرابط</th>
                                <th>ترتيب العرض</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var image in Model)
                            {
                                <tr>
                                    <td>
                                        <div class="carousel-image-thumbnail">
                                            <img src="@image.ImageUrl" alt="@image.Title" />
                                        </div>
                                    </td>
                                    <td>@image.Title</td>
                                    <td>@(image.Description?.Length > 50 ? image.Description.Substring(0, 50) + "..." : image.Description)</td>
                                    <td>@(image.LinkUrl?.Length > 30 ? image.LinkUrl.Substring(0, 30) + "..." : image.LinkUrl)</td>
                                    <td>@image.DisplayOrder</td>
                                    <td>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-outline-primary edit-carousel-image" 
                                                    data-id="@image.Id" 
                                                    data-title="@image.Title" 
                                                    data-description="@image.Description" 
                                                    data-image-url="@image.ImageUrl" 
                                                    data-link-url="@image.LinkUrl" 
                                                    data-display-order="@image.DisplayOrder"
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#editCarouselImageModal">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger delete-carousel-image" 
                                                    data-id="@image.Id" 
                                                    data-title="@image.Title"
                                                    data-bs-toggle="modal" 
                                                    data-bs-target="#deleteCarouselImageModal">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            }
            else
            {
                <div class="text-center py-5">
                    <i class="bi bi-images display-1 text-muted"></i>
                    <h4 class="mt-3">لا توجد صور في الشريط المتحرك</h4>
                    <p class="text-muted">قم بإضافة صور جديدة للشريط المتحرك من خلال الزر أعلاه</p>
                </div>
            }
        </div>
    </div>
</div>

<!-- Modal إضافة صورة جديدة -->
<div class="modal fade" id="addCarouselImageModal" tabindex="-1" aria-labelledby="addCarouselImageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addCarouselImageModalLabel">إضافة صورة جديدة للشريط المتحرك</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form asp-controller="CarouselImages" asp-action="Create" method="post" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="title" class="form-label">العنوان</label>
                        <input type="text" class="form-control" id="title" name="Title" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description" name="Description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="imageFile" class="form-label">الصورة</label>
                        <input type="file" class="form-control" id="imageFile" name="ImageFile" required>
                        <small class="form-text text-muted">يفضل أن تكون الصورة بأبعاد 1200×500 بكسل</small>
                    </div>
                    <div class="mb-3">
                        <label for="linkUrl" class="form-label">رابط الصورة (اختياري)</label>
                        <input type="url" class="form-control" id="linkUrl" name="LinkUrl">
                        <small class="form-text text-muted">عند النقر على الصورة سيتم توجيه المستخدم إلى هذا الرابط</small>
                    </div>
                    <div class="mb-3">
                        <label for="displayOrder" class="form-label">ترتيب العرض</label>
                        <input type="number" class="form-control" id="displayOrder" name="DisplayOrder" value="0" min="0">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal تعديل صورة -->
<div class="modal fade" id="editCarouselImageModal" tabindex="-1" aria-labelledby="editCarouselImageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editCarouselImageModalLabel">تعديل صورة الشريط المتحرك</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form asp-controller="CarouselImages" asp-action="Edit" method="post" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" id="editId" name="Id">
                    <div class="mb-3">
                        <label for="editTitle" class="form-label">العنوان</label>
                        <input type="text" class="form-control" id="editTitle" name="Title" required>
                    </div>
                    <div class="mb-3">
                        <label for="editDescription" class="form-label">الوصف</label>
                        <textarea class="form-control" id="editDescription" name="Description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">الصورة الحالية</label>
                        <div class="current-image-preview mb-2">
                            <img id="currentImagePreview" src="" alt="الصورة الحالية" class="img-thumbnail">
                        </div>
                        <input type="hidden" id="editImageUrl" name="CurrentImageUrl">
                    </div>
                    <div class="mb-3">
                        <label for="editImageFile" class="form-label">تغيير الصورة (اختياري)</label>
                        <input type="file" class="form-control" id="editImageFile" name="ImageFile">
                        <small class="form-text text-muted">اترك هذا الحقل فارغًا إذا كنت لا ترغب في تغيير الصورة</small>
                    </div>
                    <div class="mb-3">
                        <label for="editLinkUrl" class="form-label">رابط الصورة (اختياري)</label>
                        <input type="url" class="form-control" id="editLinkUrl" name="LinkUrl">
                    </div>
                    <div class="mb-3">
                        <label for="editDisplayOrder" class="form-label">ترتيب العرض</label>
                        <input type="number" class="form-control" id="editDisplayOrder" name="DisplayOrder" min="0">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal حذف صورة -->
<div class="modal fade" id="deleteCarouselImageModal" tabindex="-1" aria-labelledby="deleteCarouselImageModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteCarouselImageModalLabel">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من رغبتك في حذف صورة "<span id="deleteImageTitle"></span>"؟</p>
                <p class="text-danger">لا يمكن التراجع عن هذا الإجراء.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form asp-controller="CarouselImages" asp-action="Delete" method="post">
                    <input type="hidden" id="deleteId" name="Id">
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // تحميل بيانات الصورة في نموذج التعديل
        $('.edit-carousel-image').click(function () {
            var id = $(this).data('id');
            var title = $(this).data('title');
            var description = $(this).data('description');
            var imageUrl = $(this).data('image-url');
            var linkUrl = $(this).data('link-url');
            var displayOrder = $(this).data('display-order');
            
            $('#editId').val(id);
            $('#editTitle').val(title);
            $('#editDescription').val(description);
            $('#editImageUrl').val(imageUrl);
            $('#currentImagePreview').attr('src', imageUrl);
            $('#editLinkUrl').val(linkUrl);
            $('#editDisplayOrder').val(displayOrder);
        });
        
        // تحميل بيانات الصورة في نموذج الحذف
        $('.delete-carousel-image').click(function () {
            var id = $(this).data('id');
            var title = $(this).data('title');
            
            $('#deleteId').val(id);
            $('#deleteImageTitle').text(title);
        });
    </script>
}
