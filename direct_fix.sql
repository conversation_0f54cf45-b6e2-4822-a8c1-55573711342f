-- التحقق من وجود جدول Products
IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'Products')
BEGIN
    -- التحقق من وجود عمود ShowInCarousel
    IF NOT EXISTS (
        SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_NAME = 'Products' AND COLUMN_NAME = 'ShowInCarousel'
    )
    BEGIN
        -- إضافة العمود إذا لم يكن موجودًا
        ALTER TABLE Products
        ADD ShowInCarousel BIT NOT NULL DEFAULT 0;
        
        PRINT 'تم إضافة عمود ShowInCarousel إلى جدول Products';
    END
    ELSE
    BEGIN
        PRINT 'العمود ShowInCarousel موجود بالفعل في جدول Products';
    END
END
ELSE
BEGIN
    PRINT 'جدول Products غير موجود في قاعدة البيانات';
END
