-- إضافة عمود DiscountPercentage إلى جدول Products
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[Products]') AND name = 'DiscountPercentage')
BEGIN
    ALTER TABLE Products ADD DiscountPercentage int NOT NULL DEFAULT 14;
    PRINT 'تم إضافة عمود DiscountPercentage بنجاح';
END
ELSE
BEGIN
    PRINT 'عمود DiscountPercentage موجود بالفعل';
END

-- تحديث المنتجات الموجودة لتحتوي على نسبة خصم افتراضية 14%
UPDATE Products SET DiscountPercentage = 14 WHERE DiscountPercentage = 0;
PRINT 'تم تحديث نسبة الخصم للمنتجات الموجودة';
