{"format": 1, "restore": {"/Users/<USER>/Desktop/almoukhawar/Abayat.csproj": {}}, "projects": {"/Users/<USER>/Desktop/almoukhawar/Abayat.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Desktop/almoukhawar/Abayat.csproj", "projectName": "A<PERSON>at", "projectPath": "/Users/<USER>/Desktop/almoukhawar/Abayat.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Desktop/almoukhawar/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net7.0"], "sources": {"/usr/local/share/dotnet/library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net7.0": {"targetAlias": "net7.0", "dependencies": {"Microsoft.AspNetCore.Identity.EntityFrameworkCore": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.AspNetCore.Identity.UI": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[7.0.0, )"}, "Microsoft.VisualStudio.Web.CodeGeneration.Design": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/9.0.202/RuntimeIdentifierGraph.json"}}}}}