# 🚀 تعليمات نشر مشروع راعي المخور (Abayat)

## 📦 **ملفات النشر**
- **ملف النشر**: `Abayat-Release.zip` (36 MB)
- **المجلد**: `publish/` يحتوي على جميع الملفات المطلوبة

## 🔧 **متطلبات الخادم**

### **1. متطلبات النظام:**
- **نظام التشغيل**: Windows Server 2016+ أو Linux
- **ASP.NET Core Runtime**: 7.0 أو أحدث
- **قاعدة البيانات**: SQL Server 2016+ أو Azure SQL Database
- **الذاكرة**: 2GB RAM كحد أدنى (4GB مُوصى به)
- **مساحة القرص**: 1GB مساحة فارغة

### **2. إعدادات IIS (للخوادم Windows):**
- تثبيت ASP.NET Core Hosting Bundle
- تفعيل IIS مع دعم ASP.NET Core Module V2

## 🗄️ **إعداد قاعدة البيانات**

### **معلومات الاتصال الحالية:**
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=sql.bsite.net\\MSSQL2016;Database=jamel_DBAbayat;User Id=jamel_DBAbayat;Password=*********;TrustServerCertificate=True;Connection Timeout=120;ConnectRetryCount=5;ConnectRetryInterval=15;MultipleActiveResultSets=true;Encrypt=False;"
  }
}
```

### **المستخدم الإداري الافتراضي:**
- **البريد الإلكتروني**: `<EMAIL>`
- **كلمة المرور**: `Admin@123`
- **الاسم**: مدير النظام

## 📋 **خطوات النشر**

### **الطريقة 1: النشر على IIS (Windows)**

1. **تحضير الخادم:**
   ```bash
   # تثبيت ASP.NET Core Hosting Bundle
   # تحميل من: https://dotnet.microsoft.com/download/dotnet/7.0
   ```

2. **إنشاء موقع في IIS:**
   - افتح IIS Manager
   - انقر بالزر الأيمن على "Sites" → "Add Website"
   - **Site name**: Abayat
   - **Physical path**: `C:\inetpub\wwwroot\abayat`
   - **Port**: 80 (أو المنفذ المطلوب)

3. **رفع الملفات:**
   ```bash
   # استخراج محتويات Abayat-Release.zip إلى:
   C:\inetpub\wwwroot\abayat\
   ```

4. **تعديل الصلاحيات:**
   - امنح صلاحيات القراءة والكتابة لـ IIS_IUSRS
   - امنح صلاحيات الكتابة لمجلد wwwroot/uploads

### **الطريقة 2: النشر على Linux (Ubuntu/CentOS)**

1. **تثبيت .NET Runtime:**
   ```bash
   # Ubuntu
   wget https://packages.microsoft.com/config/ubuntu/20.04/packages-microsoft-prod.deb
   sudo dpkg -i packages-microsoft-prod.deb
   sudo apt-get update
   sudo apt-get install -y aspnetcore-runtime-7.0

   # CentOS
   sudo rpm -Uvh https://packages.microsoft.com/config/centos/7/packages-microsoft-prod.rpm
   sudo yum install aspnetcore-runtime-7.0
   ```

2. **إنشاء مجلد التطبيق:**
   ```bash
   sudo mkdir -p /var/www/abayat
   sudo chown -R www-data:www-data /var/www/abayat
   ```

3. **رفع الملفات:**
   ```bash
   # استخراج محتويات Abayat-Release.zip إلى:
   /var/www/abayat/
   
   # تعديل الصلاحيات
   sudo chmod +x /var/www/abayat/Abayat
   sudo chown -R www-data:www-data /var/www/abayat
   ```

4. **إنشاء خدمة systemd:**
   ```bash
   sudo nano /etc/systemd/system/abayat.service
   ```
   
   محتوى الملف:
   ```ini
   [Unit]
   Description=Abayat ASP.NET Core App
   After=network.target

   [Service]
   Type=notify
   ExecStart=/usr/bin/dotnet /var/www/abayat/Abayat.dll
   Restart=always
   RestartSec=10
   KillSignal=SIGINT
   SyslogIdentifier=abayat
   User=www-data
   Environment=ASPNETCORE_ENVIRONMENT=Production
   Environment=DOTNET_PRINT_TELEMETRY_MESSAGE=false
   WorkingDirectory=/var/www/abayat

   [Install]
   WantedBy=multi-user.target
   ```

5. **تشغيل الخدمة:**
   ```bash
   sudo systemctl enable abayat.service
   sudo systemctl start abayat.service
   sudo systemctl status abayat.service
   ```

### **الطريقة 3: النشر على Azure App Service**

1. **إنشاء App Service:**
   - اذهب إلى Azure Portal
   - أنشئ App Service جديد
   - اختر .NET 7 كـ Runtime Stack

2. **رفع الملفات:**
   - استخدم Azure CLI أو Visual Studio أو FTP
   - ارفع محتويات مجلد `publish/`

3. **تكوين Connection String:**
   - اذهب إلى Configuration في App Service
   - أضف Connection String باسم "DefaultConnection"

## ⚙️ **إعدادات ما بعد النشر**

### **1. تحديث إعدادات قاعدة البيانات:**
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "YOUR_PRODUCTION_CONNECTION_STRING"
  }
}
```

### **2. تحديث إعدادات الأمان:**
```json
{
  "AdminCredentials": {
    "Email": "<EMAIL>",
    "Password": "YourSecurePassword123!",
    "Name": "اسم المدير"
  }
}
```

### **3. تكوين HTTPS:**
- تثبيت شهادة SSL
- إعادة توجيه HTTP إلى HTTPS
- تحديث URLs في التطبيق

## 🔍 **اختبار النشر**

### **1. التحقق من التشغيل:**
```bash
# اختبار الاتصال
curl http://your-domain.com

# التحقق من قاعدة البيانات
# تسجيل الدخول بحساب المدير
```

### **2. اختبار الوظائف:**
- ✅ تسجيل الدخول والخروج
- ✅ إضافة المنتجات
- ✅ عربة التسوق
- ✅ قائمة الأمنيات
- ✅ المقارنة
- ✅ إدارة الطلبات
- ✅ رفع الصور

## 🛠️ **استكشاف الأخطاء**

### **مشاكل شائعة:**

1. **خطأ 500.30:**
   ```bash
   # التحقق من تثبيت .NET Runtime
   dotnet --list-runtimes
   ```

2. **خطأ في قاعدة البيانات:**
   ```bash
   # التحقق من Connection String
   # التأكد من صلاحيات المستخدم
   ```

3. **مشاكل الصلاحيات:**
   ```bash
   # Linux
   sudo chown -R www-data:www-data /var/www/abayat
   sudo chmod -R 755 /var/www/abayat
   
   # Windows
   # إعطاء صلاحيات IIS_IUSRS
   ```

## 📞 **الدعم الفني**

للحصول على المساعدة:
- تحقق من ملفات السجل (logs)
- راجع إعدادات قاعدة البيانات
- تأكد من تثبيت جميع المتطلبات

---

## 📝 **ملاحظات مهمة**

- ✅ تم اختبار المشروع محلياً
- ✅ جميع الوظائف تعمل بشكل صحيح
- ✅ قاعدة البيانات محدثة ومهيأة
- ✅ ملفات النشر جاهزة ومضغوطة

**تاريخ النشر**: 6 يوليو 2025
**حجم الملف**: 36 MB
**إصدار .NET**: 7.0
