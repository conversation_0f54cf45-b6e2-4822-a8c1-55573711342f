# تعليمات إصلاح مشكلة عمود ShowInCarousel

## المشكلة
تظهر رسالة خطأ `SqlException: Invalid column name 'ShowInCarousel'` عند تشغيل التطبيق، مما يشير إلى أن عمود `ShowInCarousel` غير موجود في جدول `Products` في قاعدة البيانات.

## الحلول المقترحة

### الحل 1: إضافة العمود باستخدام SQL مباشرة
1. افتح SQL Server Management Studio (SSMS)
2. اتصل بقاعدة البيانات باستخدام بيانات الاتصال الموجودة في ملف `appsettings.json`
3. نفذ الاستعلام التالي:

```sql
USE DBAbayat;

IF NOT EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'Products' AND COLUMN_NAME = 'ShowInCarousel'
)
BEGIN
    ALTER TABLE Products
    ADD ShowInCarousel BIT NOT NULL DEFAULT 0;
    
    PRINT 'تم إضافة عمود ShowInCarousel إلى جدول Products';
END
ELSE
BEGIN
    PRINT 'العمود ShowInCarousel موجود بالفعل في جدول Products';
END
```

### الحل 2: تطبيق الهجرات (Migrations)
1. افتح موجه الأوامر (Command Prompt) أو PowerShell
2. انتقل إلى مجلد المشروع
3. نفذ الأمر التالي لتطبيق الهجرات:

```
dotnet ef database update
```

### الحل 3: إعادة إنشاء قاعدة البيانات
إذا لم تنجح الحلول السابقة، يمكنك إعادة إنشاء قاعدة البيانات بالكامل:

1. افتح SQL Server Management Studio (SSMS)
2. اتصل بقاعدة البيانات
3. نفذ الاستعلام الموجود في ملف `reset_database.sql`
4. قم بتشغيل التطبيق مرة أخرى، وسيقوم بإنشاء قاعدة البيانات وتطبيق الهجرات تلقائيًا

## ملاحظات إضافية
- تم تعديل ملف `Program.cs` لاستخدام `context.Database.Migrate()` بدلاً من `context.Database.EnsureCreated()` لضمان تطبيق جميع الهجرات
- تم إضافة ملف هجرة جديد `20240701000000_AddShowInCarouselColumn.cs` لإضافة العمود إذا لم يكن موجودًا

بعد تنفيذ أحد هذه الحلول، يجب أن يعمل التطبيق بشكل صحيح دون ظهور رسالة الخطأ.
