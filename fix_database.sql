-- إضافة عمود ShowInCarousel إلى جدول Products إذا لم يكن موجودًا
IF NOT EXISTS (
    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
    WHERE TABLE_NAME = 'Products' AND COLUMN_NAME = 'ShowInCarousel'
)
BEGIN
    ALTER TABLE Products
    ADD ShowInCarousel BIT NOT NULL DEFAULT 0;
    
    PRINT 'تم إضافة عمود ShowInCarousel إلى جدول Products';
END
ELSE
BEGIN
    PRINT 'العمود ShowInCarousel موجود بالفعل في جدول Products';
END

-- التأكد من وجود الأدوار
IF NOT EXISTS (SELECT * FROM AspNetRoles WHERE Name = 'Admin')
BEGIN
    INSERT INTO AspNetRoles (Id, Name, NormalizedName, ConcurrencyStamp)
    VALUES (NEWID(), 'Admin', 'ADMIN', NEWID());
    
    PRINT 'تم إضافة دور Admin';
END
ELSE
BEGIN
    PRINT 'دور Admin موجود بالفعل';
END

IF NOT EXISTS (SELECT * FROM AspNetRoles WHERE Name = 'Customer')
BEGIN
    INSERT INTO AspNetRoles (Id, Name, NormalizedName, ConcurrencyStamp)
    VALUES (NEWID(), 'Customer', 'CUSTOMER', NEWID());
    
    PRINT 'تم إضافة دور Customer';
END
ELSE
BEGIN
    PRINT 'دور Customer موجود بالفعل';
END

-- إنشاء مستخدم مدير جديد إذا لم يكن موجودًا
IF NOT EXISTS (SELECT * FROM Users WHERE Email = '<EMAIL>')
BEGIN
    DECLARE @AdminId NVARCHAR(450) = NEWID();
    DECLARE @AdminRoleId NVARCHAR(450) = (SELECT Id FROM AspNetRoles WHERE Name = 'Admin');
    
    INSERT INTO Users (Id, UserName, NormalizedUserName, Email, NormalizedEmail, EmailConfirmed, PasswordHash, SecurityStamp, ConcurrencyStamp, PhoneNumber, PhoneNumberConfirmed, TwoFactorEnabled, LockoutEnd, LockoutEnabled, AccessFailedCount, Name)
    VALUES (
        @AdminId,
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        1, -- EmailConfirmed
        'AQAAAAIAAYagAAAAELYuYGJQYKGDRQnkYGILnTJNw3Ck5qpK/ULHhvONKzh9DBKnL/Y7+qOxfvAXbXFbQQ==', -- PasswordHash for 'Admin@123'
        'VVPCRDJS3OMRXSVEJXKXNAET2B4IASRM', -- SecurityStamp
        NEWID(), -- ConcurrencyStamp
        NULL, -- PhoneNumber
        0, -- PhoneNumberConfirmed
        0, -- TwoFactorEnabled
        NULL, -- LockoutEnd
        1, -- LockoutEnabled
        0, -- AccessFailedCount
        N'مدير النظام' -- Name
    );
    
    -- إضافة المستخدم إلى دور المدير
    INSERT INTO AspNetUserRoles (UserId, RoleId)
    VALUES (@AdminId, @AdminRoleId);
    
    PRINT 'تم إنشاء مستخدم مدير جديد';
END
ELSE
BEGIN
    PRINT 'مستخدم المدير موجود بالفعل';
END
