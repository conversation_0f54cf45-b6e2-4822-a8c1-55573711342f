using System;
using System.ComponentModel.DataAnnotations;

namespace Abayat.Models
{
    public class CarouselImage
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "الرجاء إدخال عنوان الصورة")]
        [Display(Name = "العنوان")]
        public string Title { get; set; }

        [Display(Name = "الوصف")]
        public string? Description { get; set; }

        [Required(ErrorMessage = "الرجاء إدخال رابط الصورة")]
        [Display(Name = "رابط الصورة")]
        public string ImageUrl { get; set; }

        [Display(Name = "رابط التوجيه (اختياري)")]
        public string? LinkUrl { get; set; }

        [Display(Name = "ترتيب العرض")]
        public int DisplayOrder { get; set; } = 0;

        [Display(Name = "نشط")]
        public bool IsActive { get; set; } = true;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }
}
