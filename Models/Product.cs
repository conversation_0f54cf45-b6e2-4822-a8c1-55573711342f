using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Collections.Generic;

namespace Abayat.Models
{
    public class Product
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم المنتج مطلوب")]
        [Display(Name = "اسم المنتج")]
        [StringLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required(ErrorMessage = "وصف المنتج مطلوب")]
        [Display(Name = "وصف المنتج")]
        [StringLength(500)]
        public string Description { get; set; } = string.Empty;

        [Required(ErrorMessage = "السعر مطلوب")]
        [Display(Name = "السعر")]
        [Range(0.01, 100000, ErrorMessage = "يجب أن يكون السعر أكبر من صفر")]
        [Column(TypeName = "decimal(18, 2)")]
        public decimal Price { get; set; }

        [Display(Name = "صورة المنتج")]
        [StringLength(200)]
        public string? ImageUrl { get; set; }

        [Display(Name = "متوفر")]
        public bool IsAvailable { get; set; } = true;

        [Display(Name = "عرض في الشريط المتحرك")]
        public bool ShowInCarousel { get; set; } = false;

        // نسبة الخصم (قابلة للتعديل من قبل المدير)
        [Display(Name = "نسبة الخصم %")]
        [Range(0, 100, ErrorMessage = "نسبة الخصم يجب أن تكون بين 0 و 100")]
        public int DiscountPercentage { get; set; } = 14; // القيمة الافتراضية 14%

        // السعر بعد الخصم
        [NotMapped]
        public decimal DiscountedPrice => Math.Round(Price - (Price * DiscountPercentage / 100m), 2);

        [Display(Name = "نوع المنتج")]
        [StringLength(50)]
        public string? ProductType { get; set; }

        // Foreign key for Category
        [Display(Name = "الفئة")]
        public int CategoryId { get; set; }

        // Navigation property for Category
        [ForeignKey("CategoryId")]
        public virtual Category? Category { get; set; }

        [Display(Name = "تاريخ الإضافة")]
        [DataType(DataType.Date)]
        public DateTime CreatedAt { get; set; } = DateTime.Now;
    }
}
