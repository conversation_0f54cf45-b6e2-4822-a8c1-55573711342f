using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Abayat.Models
{
    public class ShoppingCart
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public string UserId { get; set; } = string.Empty;

        [ForeignKey("UserId")]
        public virtual ApplicationUser? User { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public DateTime? UpdatedAt { get; set; }

        // Navigation property for cart items
        public virtual ICollection<CartItem>? CartItems { get; set; }

        [NotMapped]
        public decimal TotalAmount 
        { 
            get 
            {
                decimal total = 0;
                if (CartItems != null)
                {
                    foreach (var item in CartItems)
                    {
                        total += item.Quantity * (item.Product?.DiscountedPrice ?? 0);
                    }
                }
                return total;
            } 
        }

        [NotMapped]
        public int TotalItems
        {
            get
            {
                int count = 0;
                if (CartItems != null)
                {
                    foreach (var item in CartItems)
                    {
                        count += item.Quantity;
                    }
                }
                return count;
            }
        }
    }
}
