using System.ComponentModel.DataAnnotations;

namespace Abayat.Models
{
    public class FeaturedProduct
    {
        [Key]
        public int Id { get; set; }

        [Required]
        public int ProductId { get; set; }

        [Required]
        public int DisplayOrder { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.Now;

        public DateTime UpdatedAt { get; set; } = DateTime.Now;

        // Navigation property
        public virtual Product Product { get; set; } = null!;
    }
}
