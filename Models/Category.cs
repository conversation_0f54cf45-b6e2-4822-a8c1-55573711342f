using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Abayat.Models
{
    public class Category
    {
        [Key]
        public int Id { get; set; }

        [Required(ErrorMessage = "اسم الفئة مطلوب")]
        [Display(Name = "اسم الفئة")]
        [StringLength(50)]
        public string Name { get; set; } = string.Empty;

        [Display(Name = "الوصف")]
        [StringLength(500)]
        public string? Description { get; set; }

        [Display(Name = "صورة الفئة")]
        [StringLength(200)]
        public string? ImageUrl { get; set; }

        [Display(Name = "رمز الفئة")]
        [StringLength(50)]
        public string? Slug { get; set; }

        [Display(Name = "ترتيب العرض")]
        public int DisplayOrder { get; set; } = 0;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation property for products in this category
        public virtual ICollection<Product>? Products { get; set; }
    }
}
