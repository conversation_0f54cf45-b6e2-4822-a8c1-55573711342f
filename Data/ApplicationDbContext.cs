using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Abayat.Models;

namespace Abayat.Data
{
    public class ApplicationDbContext : IdentityDbContext<ApplicationUser>
    {
        public ApplicationDbContext(DbContextOptions<ApplicationDbContext> options)
            : base(options)
        {
        }

        public DbSet<Product> Products { get; set; }
        public DbSet<Category> Categories { get; set; }
        public DbSet<ShoppingCart> ShoppingCarts { get; set; }
        public DbSet<CartItem> CartItems { get; set; }
        public DbSet<Order> Orders { get; set; }
        public DbSet<OrderItem> OrderItems { get; set; }
        public DbSet<CarouselImage> CarouselImages { get; set; }
    public DbSet<FeaturedProduct> FeaturedProducts { get; set; }

        protected override void OnModelCreating(ModelBuilder builder)
        {
            base.OnModelCreating(builder);

            // تخصيص أسماء الجداول
            builder.Entity<ApplicationUser>().ToTable("Users");
            builder.Entity<Product>().ToTable("Products");
            builder.Entity<Category>().ToTable("Categories");
            builder.Entity<ShoppingCart>().ToTable("ShoppingCarts");
            builder.Entity<CartItem>().ToTable("CartItems");
            builder.Entity<Order>().ToTable("Orders");
            builder.Entity<OrderItem>().ToTable("OrderItems");
            builder.Entity<CarouselImage>().ToTable("CarouselImages");
            builder.Entity<FeaturedProduct>().ToTable("FeaturedProducts");

            // تعديل خصائص جدول الطلبات
            builder.Entity<Order>()
                .Property(o => o.UserId)
                .IsRequired(false);

            // تكوين العلاقة بين FeaturedProduct و Product
            builder.Entity<FeaturedProduct>()
                .HasOne(fp => fp.Product)
                .WithMany()
                .HasForeignKey(fp => fp.ProductId)
                .OnDelete(DeleteBehavior.Cascade);

            // فهرس فريد لضمان عدم تكرار المنتج في القائمة المميزة
            builder.Entity<FeaturedProduct>()
                .HasIndex(fp => fp.ProductId)
                .IsUnique();

            // فهرس فريد لترتيب العرض
            builder.Entity<FeaturedProduct>()
                .HasIndex(fp => fp.DisplayOrder)
                .IsUnique();

            // إضافة بيانات تجريبية للفئات
            builder.Entity<Category>().HasData(
                new Category
                {
                    Id = 1,
                    Name = "عبايات",
                    Description = "تشكيلة متنوعة من العبايات العصرية والأنيقة",
                    Slug = "abayas",
                    DisplayOrder = 1,
                    ImageUrl = "/images/categories/abayas.jpg",
                    CreatedAt = DateTime.Now
                },
                new Category
                {
                    Id = 2,
                    Name = "الأقمشة",
                    Description = "أجود أنواع الأقمشة للعبايات والملابس",
                    Slug = "fabrics",
                    DisplayOrder = 2,
                    ImageUrl = "/images/categories/fabrics.jpg",
                    CreatedAt = DateTime.Now
                },
                new Category
                {
                    Id = 3,
                    Name = "الإكسسوارات",
                    Description = "إكسسوارات متنوعة لتكملي أناقتك",
                    Slug = "accessories",
                    DisplayOrder = 3,
                    ImageUrl = "/images/categories/accessories.jpg",
                    CreatedAt = DateTime.Now
                },
                new Category
                {
                    Id = 4,
                    Name = "المخور",
                    Description = "تشكيلة من المخور الفاخرة للمناسبات",
                    Slug = "mukhwar",
                    DisplayOrder = 4,
                    ImageUrl = "/images/categories/mukhwar.jpg",
                    CreatedAt = DateTime.Now
                }
            );

            // إضافة بيانات تجريبية للمنتجات
            builder.Entity<Product>().HasData(
                new Product
                {
                    Id = 1,
                    Name = "عباية كاجوال سوداء",
                    Description = "عباية كاجوال سوداء مناسبة للاستخدام اليومي، مصنوعة من قماش عالي الجودة",
                    Price = 350.00M,
                    ProductType = "كاجوال",
                    CategoryId = 1,
                    ImageUrl = "/images/products/abaya1.jpg",
                    IsAvailable = true,
                    CreatedAt = DateTime.Now
                },
                new Product
                {
                    Id = 2,
                    Name = "عباية رسمية مطرزة",
                    Description = "عباية رسمية مطرزة بتصميم أنيق مناسبة للمناسبات الخاصة",
                    Price = 500.00M,
                    ProductType = "رسمية",
                    CategoryId = 1,
                    ImageUrl = "/images/products/abaya2.jpg",
                    IsAvailable = true,
                    CreatedAt = DateTime.Now
                },
                new Product
                {
                    Id = 3,
                    Name = "عباية مطرزة فاخرة",
                    Description = "عباية مطرزة بتصميم فاخر وأنيق مناسبة للمناسبات الخاصة",
                    Price = 750.00M,
                    ProductType = "مطرزة",
                    CategoryId = 1,
                    ImageUrl = "/images/products/abaya3.jpg",
                    IsAvailable = true,
                    CreatedAt = DateTime.Now
                },
                new Product
                {
                    Id = 4,
                    Name = "قماش كريب فاخر",
                    Description = "قماش كريب فاخر للعبايات بجودة عالية",
                    Price = 120.00M,
                    ProductType = "كريب",
                    CategoryId = 2,
                    ImageUrl = "/images/products/fabric1.jpg",
                    IsAvailable = true,
                    CreatedAt = DateTime.Now
                },
                new Product
                {
                    Id = 5,
                    Name = "قماش نت مطرز",
                    Description = "قماش نت مطرز للعبايات الفاخرة",
                    Price = 150.00M,
                    ProductType = "نت",
                    CategoryId = 2,
                    ImageUrl = "/images/products/fabric2.jpg",
                    IsAvailable = true,
                    CreatedAt = DateTime.Now
                },
                new Product
                {
                    Id = 6,
                    Name = "إكسسوار ذهبي فاخر",
                    Description = "إكسسوار ذهبي فاخر للعبايات",
                    Price = 80.00M,
                    ProductType = "ذهبي",
                    CategoryId = 3,
                    ImageUrl = "/images/products/accessory1.jpg",
                    IsAvailable = true,
                    CreatedAt = DateTime.Now
                },
                new Product
                {
                    Id = 7,
                    Name = "إكسسوار فضي أنيق",
                    Description = "إكسسوار فضي أنيق للعبايات",
                    Price = 70.00M,
                    ProductType = "فضي",
                    CategoryId = 3,
                    ImageUrl = "/images/products/accessory2.jpg",
                    IsAvailable = true,
                    CreatedAt = DateTime.Now
                },
                new Product
                {
                    Id = 8,
                    Name = "مخور فاخر مطرز",
                    Description = "مخور فاخر مطرز للمناسبات",
                    Price = 1200.00M,
                    ProductType = "مطرز",
                    CategoryId = 4,
                    ImageUrl = "/images/products/mukhwar1.jpg",
                    IsAvailable = true,
                    CreatedAt = DateTime.Now
                },
                new Product
                {
                    Id = 9,
                    Name = "مخور أنيق للأعراس",
                    Description = "مخور أنيق للأعراس والمناسبات",
                    Price = 1500.00M,
                    ProductType = "فاخر",
                    CategoryId = 4,
                    ImageUrl = "/images/products/mukhwar2.jpg",
                    IsAvailable = true,
                    CreatedAt = DateTime.Now
                }
            );
        }
    }
}