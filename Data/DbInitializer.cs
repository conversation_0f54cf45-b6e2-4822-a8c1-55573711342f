using Microsoft.AspNetCore.Identity;
using Abayat.Models;
using Microsoft.Extensions.Logging;
using System.Text;

namespace Abayat.Data
{
    public static class DbInitializer
    {
        public static async Task Initialize(IServiceProvider serviceProvider, IConfiguration configuration)
        {
            var roleManager = serviceProvider.GetRequiredService<RoleManager<IdentityRole>>();
            var userManager = serviceProvider.GetRequiredService<UserManager<ApplicationUser>>();
            var logger = serviceProvider.GetRequiredService<ILogger<Program>>();

            logger.LogInformation("بدء تهيئة قاعدة البيانات والأدوار والمستخدمين");

            // إنشاء الأدوار إذا لم تكن موجودة
            string[] roleNames = { "Admin", "Customer" };
            foreach (var roleName in roleNames)
            {
                var roleExist = await roleManager.RoleExistsAsync(roleName);
                if (!roleExist)
                {
                    var result = await roleManager.CreateAsync(new IdentityRole(roleName));
                    if (result.Succeeded)
                    {
                        logger.LogInformation("تم إنشاء دور جديد: {RoleName}", roleName);
                    }
                    else
                    {
                        logger.LogError("فشل إنشاء دور: {RoleName}، الأخطاء: {Errors}",
                            roleName, string.Join(", ", result.Errors.Select(e => e.Description)));
                    }
                }
                else
                {
                    logger.LogInformation("الدور موجود بالفعل: {RoleName}", roleName);
                }
            }

            // إنشاء حساب المدير الافتراضي إذا لم يكن موجودًا
            var adminEmail = configuration["AdminCredentials:Email"];
            var adminPassword = configuration["AdminCredentials:Password"];
            var adminName = configuration["AdminCredentials:Name"];

            logger.LogInformation("معلومات المدير الافتراضي: البريد الإلكتروني: {Email}, الاسم: {Name}",
                adminEmail, adminName);

            if (!string.IsNullOrEmpty(adminEmail) && !string.IsNullOrEmpty(adminPassword))
            {
                var adminUser = await userManager.FindByEmailAsync(adminEmail);
                if (adminUser == null)
                {
                    var admin = new ApplicationUser
                    {
                        UserName = adminEmail,
                        Email = adminEmail,
                        Name = adminName ?? "مدير النظام",
                        EmailConfirmed = true
                    };

                    var result = await userManager.CreateAsync(admin, adminPassword);
                    if (result.Succeeded)
                    {
                        logger.LogInformation("تم إنشاء مستخدم مدير جديد: {Email}", adminEmail);

                        var roleResult = await userManager.AddToRoleAsync(admin, "Admin");
                        if (roleResult.Succeeded)
                        {
                            logger.LogInformation("تم إضافة المستخدم {Email} إلى دور Admin", adminEmail);
                        }
                        else
                        {
                            logger.LogError("فشل إضافة المستخدم {Email} إلى دور Admin، الأخطاء: {Errors}",
                                adminEmail, string.Join(", ", roleResult.Errors.Select(e => e.Description)));
                        }
                    }
                    else
                    {
                        logger.LogError("فشل إنشاء مستخدم مدير: {Email}، الأخطاء: {Errors}",
                            adminEmail, string.Join(", ", result.Errors.Select(e => e.Description)));
                    }
                }
                else
                {
                    logger.LogInformation("مستخدم المدير موجود بالفعل: {Email}", adminEmail);

                    // التحقق من أن المستخدم في دور المدير
                    if (!await userManager.IsInRoleAsync(adminUser, "Admin"))
                    {
                        var roleResult = await userManager.AddToRoleAsync(adminUser, "Admin");
                        if (roleResult.Succeeded)
                        {
                            logger.LogInformation("تم إضافة المستخدم الموجود {Email} إلى دور Admin", adminEmail);
                        }
                        else
                        {
                            logger.LogError("فشل إضافة المستخدم الموجود {Email} إلى دور Admin، الأخطاء: {Errors}",
                                adminEmail, string.Join(", ", roleResult.Errors.Select(e => e.Description)));
                        }
                    }
                }
            }

            // عرض معلومات المستخدمين والأدوار
            await LogUsersAndRoles(userManager, roleManager, logger);
        }

        private static async Task LogUsersAndRoles(UserManager<ApplicationUser> userManager,
            RoleManager<IdentityRole> roleManager, ILogger logger)
        {
            // عرض جميع الأدوار
            var roles = roleManager.Roles.ToList();
            var rolesInfo = new StringBuilder();
            rolesInfo.AppendLine($"عدد الأدوار: {roles.Count}");
            foreach (var role in roles)
            {
                rolesInfo.AppendLine($"- الدور: {role.Name}, المعرف: {role.Id}");
            }
            logger.LogInformation(rolesInfo.ToString());

            // عرض جميع المستخدمين وأدوارهم
            var users = userManager.Users.ToList();
            var usersInfo = new StringBuilder();
            usersInfo.AppendLine($"عدد المستخدمين: {users.Count}");
            foreach (var user in users)
            {
                var userRoles = await userManager.GetRolesAsync(user);
                usersInfo.AppendLine($"- المستخدم: {user.Email}, الاسم: {user.Name}, " +
                    $"المعرف: {user.Id}, الأدوار: {string.Join(", ", userRoles)}");
            }
            logger.LogInformation(usersInfo.ToString());
        }
    }
}
