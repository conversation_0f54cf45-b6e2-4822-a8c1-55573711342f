using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Abayat.Data
{
    public static class SqlScriptExecutor
    {
        public static async Task ExecuteScript(string scriptPath, IConfiguration configuration, ILogger logger)
        {
            try
            {
                var connectionString = configuration.GetConnectionString("DefaultConnection");
                var scriptContent = await File.ReadAllTextAsync(scriptPath);
                
                using var connection = new SqlConnection(connectionString);
                await connection.OpenAsync();
                
                // تقسيم النص إلى أوامر منفصلة
                var commands = scriptContent.Split(new[] { "GO", "go" }, StringSplitOptions.RemoveEmptyEntries);
                
                foreach (var commandText in commands)
                {
                    var trimmedCommand = commandText.Trim();
                    if (string.IsNullOrEmpty(trimmedCommand))
                        continue;
                        
                    using var command = new SqlCommand(trimmedCommand, connection);
                    command.CommandTimeout = 120;
                    await command.ExecuteNonQueryAsync();
                }
                
                logger.LogInformation("تم تنفيذ SQL script بنجاح: {ScriptPath}", scriptPath);
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "خطأ في تنفيذ SQL script: {ScriptPath}", scriptPath);
                throw;
            }
        }
    }
}
