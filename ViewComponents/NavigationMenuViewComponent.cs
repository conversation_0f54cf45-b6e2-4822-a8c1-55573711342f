using Abayat.Data;
using Abayat.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Abayat.ViewComponents
{
    public class NavigationMenuViewComponent : ViewComponent
    {
        private readonly ApplicationDbContext _context;

        public NavigationMenuViewComponent(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IViewComponentResult> InvokeAsync()
        {
            // استرجاع جميع الفئات من قاعدة البيانات مرتبة حسب ترتيب العرض
            var categories = await _context.Categories
                .OrderBy(c => c.DisplayOrder)
                .ToListAsync();

            // التحقق مما إذا كان الطلب من جهاز محمول
            bool isMobile = IsMobileDevice();

            // استخدام عرض مختلف للأجهزة المحمولة
            if (isMobile)
            {
                return View("MobileDefault", categories);
            }

            return View(categories);
        }

        // دالة للتحقق مما إذا كان الطلب من جهاز محمول
        private bool IsMobileDevice()
        {
            string userAgent = HttpContext.Request.Headers["User-Agent"].ToString();

            // التحقق من وجود كلمات دالة على الأجهزة المحمولة في User-Agent
            return userAgent.Contains("Android") ||
                   userAgent.Contains("iPhone") ||
                   userAgent.Contains("iPad") ||
                   userAgent.Contains("Windows Phone") ||
                   userAgent.Contains("Mobile");
        }
    }
}
