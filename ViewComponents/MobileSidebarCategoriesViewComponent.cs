using Abayat.Data;
using Abayat.Models;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace Abayat.ViewComponents
{
    public class MobileSidebarCategoriesViewComponent : ViewComponent
    {
        private readonly ApplicationDbContext _context;

        public MobileSidebarCategoriesViewComponent(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<IViewComponentResult> InvokeAsync()
        {
            // استرجاع جميع الفئات من قاعدة البيانات مرتبة حسب ترتيب العرض
            var categories = await _context.Categories
                .OrderBy(c => c.DisplayOrder)
                .ToListAsync();

            return View(categories);
        }
    }
}
