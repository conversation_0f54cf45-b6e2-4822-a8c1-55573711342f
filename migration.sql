﻿IF OBJECT_ID(N'[__EFMigrationsHistory]') IS NULL
BEGIN
    CREATE TABLE [__EFMigrationsHistory] (
        [MigrationId] nvarchar(150) NOT NULL,
        [ProductVersion] nvarchar(32) NOT NULL,
        CONSTRAINT [PK___EFMigrationsHistory] PRIMARY KEY ([MigrationId])
    );
END;
GO

BEGIN TRANSACTION;
GO

CREATE TABLE [AspNetRoles] (
    [Id] nvarchar(450) NOT NULL,
    [Name] nvarchar(256) NULL,
    [NormalizedName] nvarchar(256) NULL,
    [ConcurrencyStamp] nvarchar(max) NULL,
    CONSTRAINT [PK_AspNetRoles] PRIMARY KEY ([Id])
);
GO

CREATE TABLE [Users] (
    [Id] nvarchar(450) NOT NULL,
    [Name] nvarchar(100) NOT NULL,
    [UserName] nvarchar(256) NULL,
    [NormalizedUserName] nvarchar(256) NULL,
    [Email] nvarchar(256) NULL,
    [NormalizedEmail] nvarchar(256) NULL,
    [EmailConfirmed] bit NOT NULL,
    [PasswordHash] nvarchar(max) NULL,
    [SecurityStamp] nvarchar(max) NULL,
    [ConcurrencyStamp] nvarchar(max) NULL,
    [PhoneNumber] nvarchar(max) NULL,
    [PhoneNumberConfirmed] bit NOT NULL,
    [TwoFactorEnabled] bit NOT NULL,
    [LockoutEnd] datetimeoffset NULL,
    [LockoutEnabled] bit NOT NULL,
    [AccessFailedCount] int NOT NULL,
    CONSTRAINT [PK_Users] PRIMARY KEY ([Id])
);
GO

CREATE TABLE [AspNetRoleClaims] (
    [Id] int NOT NULL IDENTITY,
    [RoleId] nvarchar(450) NOT NULL,
    [ClaimType] nvarchar(max) NULL,
    [ClaimValue] nvarchar(max) NULL,
    CONSTRAINT [PK_AspNetRoleClaims] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_AspNetRoleClaims_AspNetRoles_RoleId] FOREIGN KEY ([RoleId]) REFERENCES [AspNetRoles] ([Id]) ON DELETE CASCADE
);
GO

CREATE TABLE [AspNetUserClaims] (
    [Id] int NOT NULL IDENTITY,
    [UserId] nvarchar(450) NOT NULL,
    [ClaimType] nvarchar(max) NULL,
    [ClaimValue] nvarchar(max) NULL,
    CONSTRAINT [PK_AspNetUserClaims] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_AspNetUserClaims_Users_UserId] FOREIGN KEY ([UserId]) REFERENCES [Users] ([Id]) ON DELETE CASCADE
);
GO

CREATE TABLE [AspNetUserLogins] (
    [LoginProvider] nvarchar(450) NOT NULL,
    [ProviderKey] nvarchar(450) NOT NULL,
    [ProviderDisplayName] nvarchar(max) NULL,
    [UserId] nvarchar(450) NOT NULL,
    CONSTRAINT [PK_AspNetUserLogins] PRIMARY KEY ([LoginProvider], [ProviderKey]),
    CONSTRAINT [FK_AspNetUserLogins_Users_UserId] FOREIGN KEY ([UserId]) REFERENCES [Users] ([Id]) ON DELETE CASCADE
);
GO

CREATE TABLE [AspNetUserRoles] (
    [UserId] nvarchar(450) NOT NULL,
    [RoleId] nvarchar(450) NOT NULL,
    CONSTRAINT [PK_AspNetUserRoles] PRIMARY KEY ([UserId], [RoleId]),
    CONSTRAINT [FK_AspNetUserRoles_AspNetRoles_RoleId] FOREIGN KEY ([RoleId]) REFERENCES [AspNetRoles] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_AspNetUserRoles_Users_UserId] FOREIGN KEY ([UserId]) REFERENCES [Users] ([Id]) ON DELETE CASCADE
);
GO

CREATE TABLE [AspNetUserTokens] (
    [UserId] nvarchar(450) NOT NULL,
    [LoginProvider] nvarchar(450) NOT NULL,
    [Name] nvarchar(450) NOT NULL,
    [Value] nvarchar(max) NULL,
    CONSTRAINT [PK_AspNetUserTokens] PRIMARY KEY ([UserId], [LoginProvider], [Name]),
    CONSTRAINT [FK_AspNetUserTokens_Users_UserId] FOREIGN KEY ([UserId]) REFERENCES [Users] ([Id]) ON DELETE CASCADE
);
GO

CREATE INDEX [IX_AspNetRoleClaims_RoleId] ON [AspNetRoleClaims] ([RoleId]);
GO

CREATE UNIQUE INDEX [RoleNameIndex] ON [AspNetRoles] ([NormalizedName]) WHERE [NormalizedName] IS NOT NULL;
GO

CREATE INDEX [IX_AspNetUserClaims_UserId] ON [AspNetUserClaims] ([UserId]);
GO

CREATE INDEX [IX_AspNetUserLogins_UserId] ON [AspNetUserLogins] ([UserId]);
GO

CREATE INDEX [IX_AspNetUserRoles_RoleId] ON [AspNetUserRoles] ([RoleId]);
GO

CREATE INDEX [EmailIndex] ON [Users] ([NormalizedEmail]);
GO

CREATE UNIQUE INDEX [UserNameIndex] ON [Users] ([NormalizedUserName]) WHERE [NormalizedUserName] IS NOT NULL;
GO

INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20240601000000_InitialCreate', N'7.0.0');
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO


                IF NOT EXISTS (
                    SELECT * FROM INFORMATION_SCHEMA.COLUMNS 
                    WHERE TABLE_NAME = 'Products' AND COLUMN_NAME = 'ShowInCarousel'
                )
                BEGIN
                    ALTER TABLE Products
                    ADD ShowInCarousel BIT NOT NULL DEFAULT 0;
                END
            
GO

INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20240701000000_AddShowInCarouselColumn', N'7.0.0');
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

ALTER TABLE [Orders] ADD [IsGuestOrder] bit NOT NULL DEFAULT CAST(0 AS bit);
GO

INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250510190600_AddIsGuestOrderColumnOnly', N'7.0.0');
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

CREATE TABLE [AspNetRoles] (
    [Id] nvarchar(450) NOT NULL,
    [Name] nvarchar(256) NULL,
    [NormalizedName] nvarchar(256) NULL,
    [ConcurrencyStamp] nvarchar(max) NULL,
    CONSTRAINT [PK_AspNetRoles] PRIMARY KEY ([Id])
);
GO

CREATE TABLE [Categories] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(50) NOT NULL,
    [Description] nvarchar(500) NULL,
    [ImageUrl] nvarchar(200) NULL,
    [Slug] nvarchar(50) NULL,
    [DisplayOrder] int NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    CONSTRAINT [PK_Categories] PRIMARY KEY ([Id])
);
GO

CREATE TABLE [Users] (
    [Id] nvarchar(450) NOT NULL,
    [Name] nvarchar(100) NOT NULL,
    [UserName] nvarchar(256) NULL,
    [NormalizedUserName] nvarchar(256) NULL,
    [Email] nvarchar(256) NULL,
    [NormalizedEmail] nvarchar(256) NULL,
    [EmailConfirmed] bit NOT NULL,
    [PasswordHash] nvarchar(max) NULL,
    [SecurityStamp] nvarchar(max) NULL,
    [ConcurrencyStamp] nvarchar(max) NULL,
    [PhoneNumber] nvarchar(max) NULL,
    [PhoneNumberConfirmed] bit NOT NULL,
    [TwoFactorEnabled] bit NOT NULL,
    [LockoutEnd] datetimeoffset NULL,
    [LockoutEnabled] bit NOT NULL,
    [AccessFailedCount] int NOT NULL,
    CONSTRAINT [PK_Users] PRIMARY KEY ([Id])
);
GO

CREATE TABLE [AspNetRoleClaims] (
    [Id] int NOT NULL IDENTITY,
    [RoleId] nvarchar(450) NOT NULL,
    [ClaimType] nvarchar(max) NULL,
    [ClaimValue] nvarchar(max) NULL,
    CONSTRAINT [PK_AspNetRoleClaims] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_AspNetRoleClaims_AspNetRoles_RoleId] FOREIGN KEY ([RoleId]) REFERENCES [AspNetRoles] ([Id]) ON DELETE CASCADE
);
GO

CREATE TABLE [Products] (
    [Id] int NOT NULL IDENTITY,
    [Name] nvarchar(100) NOT NULL,
    [Description] nvarchar(500) NOT NULL,
    [Price] decimal(18,2) NOT NULL,
    [ImageUrl] nvarchar(200) NULL,
    [IsAvailable] bit NOT NULL,
    [ShowInCarousel] bit NOT NULL,
    [ProductType] nvarchar(50) NULL,
    [CategoryId] int NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    CONSTRAINT [PK_Products] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Products_Categories_CategoryId] FOREIGN KEY ([CategoryId]) REFERENCES [Categories] ([Id]) ON DELETE CASCADE
);
GO

CREATE TABLE [AspNetUserClaims] (
    [Id] int NOT NULL IDENTITY,
    [UserId] nvarchar(450) NOT NULL,
    [ClaimType] nvarchar(max) NULL,
    [ClaimValue] nvarchar(max) NULL,
    CONSTRAINT [PK_AspNetUserClaims] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_AspNetUserClaims_Users_UserId] FOREIGN KEY ([UserId]) REFERENCES [Users] ([Id]) ON DELETE CASCADE
);
GO

CREATE TABLE [AspNetUserLogins] (
    [LoginProvider] nvarchar(450) NOT NULL,
    [ProviderKey] nvarchar(450) NOT NULL,
    [ProviderDisplayName] nvarchar(max) NULL,
    [UserId] nvarchar(450) NOT NULL,
    CONSTRAINT [PK_AspNetUserLogins] PRIMARY KEY ([LoginProvider], [ProviderKey]),
    CONSTRAINT [FK_AspNetUserLogins_Users_UserId] FOREIGN KEY ([UserId]) REFERENCES [Users] ([Id]) ON DELETE CASCADE
);
GO

CREATE TABLE [AspNetUserRoles] (
    [UserId] nvarchar(450) NOT NULL,
    [RoleId] nvarchar(450) NOT NULL,
    CONSTRAINT [PK_AspNetUserRoles] PRIMARY KEY ([UserId], [RoleId]),
    CONSTRAINT [FK_AspNetUserRoles_AspNetRoles_RoleId] FOREIGN KEY ([RoleId]) REFERENCES [AspNetRoles] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_AspNetUserRoles_Users_UserId] FOREIGN KEY ([UserId]) REFERENCES [Users] ([Id]) ON DELETE CASCADE
);
GO

CREATE TABLE [AspNetUserTokens] (
    [UserId] nvarchar(450) NOT NULL,
    [LoginProvider] nvarchar(450) NOT NULL,
    [Name] nvarchar(450) NOT NULL,
    [Value] nvarchar(max) NULL,
    CONSTRAINT [PK_AspNetUserTokens] PRIMARY KEY ([UserId], [LoginProvider], [Name]),
    CONSTRAINT [FK_AspNetUserTokens_Users_UserId] FOREIGN KEY ([UserId]) REFERENCES [Users] ([Id]) ON DELETE CASCADE
);
GO

CREATE TABLE [Orders] (
    [Id] int NOT NULL IDENTITY,
    [UserId] nvarchar(450) NULL,
    [IsGuestOrder] bit NOT NULL,
    [FullName] nvarchar(max) NOT NULL,
    [PhoneNumber] nvarchar(max) NOT NULL,
    [Address] nvarchar(max) NOT NULL,
    [Notes] nvarchar(max) NULL,
    [TotalAmount] decimal(18,2) NOT NULL,
    [Status] int NOT NULL,
    [OrderDate] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    CONSTRAINT [PK_Orders] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_Orders_Users_UserId] FOREIGN KEY ([UserId]) REFERENCES [Users] ([Id])
);
GO

CREATE TABLE [ShoppingCarts] (
    [Id] int NOT NULL IDENTITY,
    [UserId] nvarchar(450) NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    CONSTRAINT [PK_ShoppingCarts] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_ShoppingCarts_Users_UserId] FOREIGN KEY ([UserId]) REFERENCES [Users] ([Id]) ON DELETE CASCADE
);
GO

CREATE TABLE [OrderItems] (
    [Id] int NOT NULL IDENTITY,
    [OrderId] int NOT NULL,
    [ProductId] int NOT NULL,
    [ProductName] nvarchar(max) NOT NULL,
    [UnitPrice] decimal(18,2) NOT NULL,
    [Quantity] int NOT NULL,
    [TotalPrice] decimal(18,2) NOT NULL,
    CONSTRAINT [PK_OrderItems] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_OrderItems_Orders_OrderId] FOREIGN KEY ([OrderId]) REFERENCES [Orders] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_OrderItems_Products_ProductId] FOREIGN KEY ([ProductId]) REFERENCES [Products] ([Id]) ON DELETE CASCADE
);
GO

CREATE TABLE [CartItems] (
    [Id] int NOT NULL IDENTITY,
    [ShoppingCartId] int NOT NULL,
    [ProductId] int NOT NULL,
    [Quantity] int NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    [UpdatedAt] datetime2 NULL,
    CONSTRAINT [PK_CartItems] PRIMARY KEY ([Id]),
    CONSTRAINT [FK_CartItems_Products_ProductId] FOREIGN KEY ([ProductId]) REFERENCES [Products] ([Id]) ON DELETE CASCADE,
    CONSTRAINT [FK_CartItems_ShoppingCarts_ShoppingCartId] FOREIGN KEY ([ShoppingCartId]) REFERENCES [ShoppingCarts] ([Id]) ON DELETE CASCADE
);
GO

IF EXISTS (SELECT * FROM [sys].[identity_columns] WHERE [name] IN (N'Id', N'CreatedAt', N'Description', N'DisplayOrder', N'ImageUrl', N'Name', N'Slug') AND [object_id] = OBJECT_ID(N'[Categories]'))
    SET IDENTITY_INSERT [Categories] ON;
INSERT INTO [Categories] ([Id], [CreatedAt], [Description], [DisplayOrder], [ImageUrl], [Name], [Slug])
VALUES (1, '2025-05-10T23:54:51.1734170+04:00', N'تشكيلة متنوعة من العبايات العصرية والأنيقة', 1, N'/images/categories/abayas.jpg', N'عبايات', N'abayas'),
(2, '2025-05-10T23:54:51.1734170+04:00', N'أجود أنواع الأقمشة للعبايات والملابس', 2, N'/images/categories/fabrics.jpg', N'الأقمشة', N'fabrics'),
(3, '2025-05-10T23:54:51.1734170+04:00', N'إكسسوارات متنوعة لتكملي أناقتك', 3, N'/images/categories/accessories.jpg', N'الإكسسوارات', N'accessories'),
(4, '2025-05-10T23:54:51.1734180+04:00', N'تشكيلة من المخور الفاخرة للمناسبات', 4, N'/images/categories/mukhwar.jpg', N'المخور', N'mukhwar');
IF EXISTS (SELECT * FROM [sys].[identity_columns] WHERE [name] IN (N'Id', N'CreatedAt', N'Description', N'DisplayOrder', N'ImageUrl', N'Name', N'Slug') AND [object_id] = OBJECT_ID(N'[Categories]'))
    SET IDENTITY_INSERT [Categories] OFF;
GO

IF EXISTS (SELECT * FROM [sys].[identity_columns] WHERE [name] IN (N'Id', N'CategoryId', N'CreatedAt', N'Description', N'ImageUrl', N'IsAvailable', N'Name', N'Price', N'ProductType', N'ShowInCarousel') AND [object_id] = OBJECT_ID(N'[Products]'))
    SET IDENTITY_INSERT [Products] ON;
INSERT INTO [Products] ([Id], [CategoryId], [CreatedAt], [Description], [ImageUrl], [IsAvailable], [Name], [Price], [ProductType], [ShowInCarousel])
VALUES (1, 1, '2025-05-10T23:54:51.1734260+04:00', N'عباية كاجوال سوداء مناسبة للاستخدام اليومي، مصنوعة من قماش عالي الجودة', N'/images/products/abaya1.jpg', CAST(1 AS bit), N'عباية كاجوال سوداء', 350.0, N'كاجوال', CAST(0 AS bit)),
(2, 1, '2025-05-10T23:54:51.1734260+04:00', N'عباية رسمية مطرزة بتصميم أنيق مناسبة للمناسبات الخاصة', N'/images/products/abaya2.jpg', CAST(1 AS bit), N'عباية رسمية مطرزة', 500.0, N'رسمية', CAST(0 AS bit)),
(3, 1, '2025-05-10T23:54:51.1734260+04:00', N'عباية مطرزة بتصميم فاخر وأنيق مناسبة للمناسبات الخاصة', N'/images/products/abaya3.jpg', CAST(1 AS bit), N'عباية مطرزة فاخرة', 750.0, N'مطرزة', CAST(0 AS bit)),
(4, 2, '2025-05-10T23:54:51.1734260+04:00', N'قماش كريب فاخر للعبايات بجودة عالية', N'/images/products/fabric1.jpg', CAST(1 AS bit), N'قماش كريب فاخر', 120.0, N'كريب', CAST(0 AS bit)),
(5, 2, '2025-05-10T23:54:51.1734270+04:00', N'قماش نت مطرز للعبايات الفاخرة', N'/images/products/fabric2.jpg', CAST(1 AS bit), N'قماش نت مطرز', 150.0, N'نت', CAST(0 AS bit)),
(6, 3, '2025-05-10T23:54:51.1734270+04:00', N'إكسسوار ذهبي فاخر للعبايات', N'/images/products/accessory1.jpg', CAST(1 AS bit), N'إكسسوار ذهبي فاخر', 80.0, N'ذهبي', CAST(0 AS bit)),
(7, 3, '2025-05-10T23:54:51.1734270+04:00', N'إكسسوار فضي أنيق للعبايات', N'/images/products/accessory2.jpg', CAST(1 AS bit), N'إكسسوار فضي أنيق', 70.0, N'فضي', CAST(0 AS bit)),
(8, 4, '2025-05-10T23:54:51.1734280+04:00', N'مخور فاخر مطرز للمناسبات', N'/images/products/mukhwar1.jpg', CAST(1 AS bit), N'مخور فاخر مطرز', 1200.0, N'مطرز', CAST(0 AS bit)),
(9, 4, '2025-05-10T23:54:51.1734280+04:00', N'مخور أنيق للأعراس والمناسبات', N'/images/products/mukhwar2.jpg', CAST(1 AS bit), N'مخور أنيق للأعراس', 1500.0, N'فاخر', CAST(0 AS bit));
IF EXISTS (SELECT * FROM [sys].[identity_columns] WHERE [name] IN (N'Id', N'CategoryId', N'CreatedAt', N'Description', N'ImageUrl', N'IsAvailable', N'Name', N'Price', N'ProductType', N'ShowInCarousel') AND [object_id] = OBJECT_ID(N'[Products]'))
    SET IDENTITY_INSERT [Products] OFF;
GO

CREATE INDEX [IX_AspNetRoleClaims_RoleId] ON [AspNetRoleClaims] ([RoleId]);
GO

CREATE UNIQUE INDEX [RoleNameIndex] ON [AspNetRoles] ([NormalizedName]) WHERE [NormalizedName] IS NOT NULL;
GO

CREATE INDEX [IX_AspNetUserClaims_UserId] ON [AspNetUserClaims] ([UserId]);
GO

CREATE INDEX [IX_AspNetUserLogins_UserId] ON [AspNetUserLogins] ([UserId]);
GO

CREATE INDEX [IX_AspNetUserRoles_RoleId] ON [AspNetUserRoles] ([RoleId]);
GO

CREATE INDEX [IX_CartItems_ProductId] ON [CartItems] ([ProductId]);
GO

CREATE INDEX [IX_CartItems_ShoppingCartId] ON [CartItems] ([ShoppingCartId]);
GO

CREATE INDEX [IX_OrderItems_OrderId] ON [OrderItems] ([OrderId]);
GO

CREATE INDEX [IX_OrderItems_ProductId] ON [OrderItems] ([ProductId]);
GO

CREATE INDEX [IX_Orders_UserId] ON [Orders] ([UserId]);
GO

CREATE INDEX [IX_Products_CategoryId] ON [Products] ([CategoryId]);
GO

CREATE INDEX [IX_ShoppingCarts_UserId] ON [ShoppingCarts] ([UserId]);
GO

CREATE INDEX [EmailIndex] ON [Users] ([NormalizedEmail]);
GO

CREATE UNIQUE INDEX [UserNameIndex] ON [Users] ([NormalizedUserName]) WHERE [NormalizedUserName] IS NOT NULL;
GO

INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250510195451_AllowNullUserId', N'7.0.0');
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

UPDATE [Categories] SET [CreatedAt] = '2025-05-11T23:54:50.5876920+04:00'
WHERE [Id] = 1;
SELECT @@ROWCOUNT;

GO

UPDATE [Categories] SET [CreatedAt] = '2025-05-11T23:54:50.5876920+04:00'
WHERE [Id] = 2;
SELECT @@ROWCOUNT;

GO

UPDATE [Categories] SET [CreatedAt] = '2025-05-11T23:54:50.5876940+04:00'
WHERE [Id] = 3;
SELECT @@ROWCOUNT;

GO

UPDATE [Categories] SET [CreatedAt] = '2025-05-11T23:54:50.5876940+04:00'
WHERE [Id] = 4;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-11T23:54:50.5877020+04:00'
WHERE [Id] = 1;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-11T23:54:50.5877020+04:00'
WHERE [Id] = 2;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-11T23:54:50.5877020+04:00'
WHERE [Id] = 3;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-11T23:54:50.5877020+04:00'
WHERE [Id] = 4;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-11T23:54:50.5877030+04:00'
WHERE [Id] = 5;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-11T23:54:50.5877030+04:00'
WHERE [Id] = 6;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-11T23:54:50.5877030+04:00'
WHERE [Id] = 7;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-11T23:54:50.5877030+04:00'
WHERE [Id] = 8;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-11T23:54:50.5877040+04:00'
WHERE [Id] = 9;
SELECT @@ROWCOUNT;

GO

INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250511195450_AllowNullUserIdInOrders', N'7.0.0');
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

UPDATE [Categories] SET [CreatedAt] = '2025-05-11T23:55:42.5364770+04:00'
WHERE [Id] = 1;
SELECT @@ROWCOUNT;

GO

UPDATE [Categories] SET [CreatedAt] = '2025-05-11T23:55:42.5364780+04:00'
WHERE [Id] = 2;
SELECT @@ROWCOUNT;

GO

UPDATE [Categories] SET [CreatedAt] = '2025-05-11T23:55:42.5364780+04:00'
WHERE [Id] = 3;
SELECT @@ROWCOUNT;

GO

UPDATE [Categories] SET [CreatedAt] = '2025-05-11T23:55:42.5364780+04:00'
WHERE [Id] = 4;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-11T23:55:42.5364870+04:00'
WHERE [Id] = 1;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-11T23:55:42.5364870+04:00'
WHERE [Id] = 2;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-11T23:55:42.5364880+04:00'
WHERE [Id] = 3;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-11T23:55:42.5364880+04:00'
WHERE [Id] = 4;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-11T23:55:42.5364880+04:00'
WHERE [Id] = 5;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-11T23:55:42.5364880+04:00'
WHERE [Id] = 6;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-11T23:55:42.5364890+04:00'
WHERE [Id] = 7;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-11T23:55:42.5364890+04:00'
WHERE [Id] = 8;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-11T23:55:42.5364890+04:00'
WHERE [Id] = 9;
SELECT @@ROWCOUNT;

GO

INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250511195542_ModifyOrdersTableAllowNullUserId', N'7.0.0');
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

UPDATE [Categories] SET [CreatedAt] = '2025-05-11T23:57:16.0410790+04:00'
WHERE [Id] = 1;
SELECT @@ROWCOUNT;

GO

UPDATE [Categories] SET [CreatedAt] = '2025-05-11T23:57:16.0410800+04:00'
WHERE [Id] = 2;
SELECT @@ROWCOUNT;

GO

UPDATE [Categories] SET [CreatedAt] = '2025-05-11T23:57:16.0410800+04:00'
WHERE [Id] = 3;
SELECT @@ROWCOUNT;

GO

UPDATE [Categories] SET [CreatedAt] = '2025-05-11T23:57:16.0410800+04:00'
WHERE [Id] = 4;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-11T23:57:16.0410890+04:00'
WHERE [Id] = 1;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-11T23:57:16.0410890+04:00'
WHERE [Id] = 2;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-11T23:57:16.0410900+04:00'
WHERE [Id] = 3;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-11T23:57:16.0410900+04:00'
WHERE [Id] = 4;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-11T23:57:16.0410900+04:00'
WHERE [Id] = 5;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-11T23:57:16.0410900+04:00'
WHERE [Id] = 6;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-11T23:57:16.0410910+04:00'
WHERE [Id] = 7;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-11T23:57:16.0410910+04:00'
WHERE [Id] = 8;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-11T23:57:16.0410910+04:00'
WHERE [Id] = 9;
SELECT @@ROWCOUNT;

GO

INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250511195716_UpdateOrdersTableNullableUserId', N'7.0.0');
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

ALTER TABLE Orders ALTER COLUMN UserId nvarchar(450) NULL
GO

UPDATE [Categories] SET [CreatedAt] = '2025-05-12T00:00:23.7816180+04:00'
WHERE [Id] = 1;
SELECT @@ROWCOUNT;

GO

UPDATE [Categories] SET [CreatedAt] = '2025-05-12T00:00:23.7816180+04:00'
WHERE [Id] = 2;
SELECT @@ROWCOUNT;

GO

UPDATE [Categories] SET [CreatedAt] = '2025-05-12T00:00:23.7816180+04:00'
WHERE [Id] = 3;
SELECT @@ROWCOUNT;

GO

UPDATE [Categories] SET [CreatedAt] = '2025-05-12T00:00:23.7816180+04:00'
WHERE [Id] = 4;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-12T00:00:23.7816260+04:00'
WHERE [Id] = 1;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-12T00:00:23.7816260+04:00'
WHERE [Id] = 2;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-12T00:00:23.7816270+04:00'
WHERE [Id] = 3;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-12T00:00:23.7816270+04:00'
WHERE [Id] = 4;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-12T00:00:23.7816270+04:00'
WHERE [Id] = 5;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-12T00:00:23.7816270+04:00'
WHERE [Id] = 6;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-12T00:00:23.7816280+04:00'
WHERE [Id] = 7;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-12T00:00:23.7816280+04:00'
WHERE [Id] = 8;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-12T00:00:23.7816280+04:00'
WHERE [Id] = 9;
SELECT @@ROWCOUNT;

GO

INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250511200023_ManuallyUpdateOrdersTable', N'7.0.0');
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

ALTER TABLE Orders ALTER COLUMN UserId nvarchar(450) NULL
GO

UPDATE [Categories] SET [CreatedAt] = '2025-05-12T00:08:55.8350380+04:00'
WHERE [Id] = 1;
SELECT @@ROWCOUNT;

GO

UPDATE [Categories] SET [CreatedAt] = '2025-05-12T00:08:55.8350380+04:00'
WHERE [Id] = 2;
SELECT @@ROWCOUNT;

GO

UPDATE [Categories] SET [CreatedAt] = '2025-05-12T00:08:55.8350380+04:00'
WHERE [Id] = 3;
SELECT @@ROWCOUNT;

GO

UPDATE [Categories] SET [CreatedAt] = '2025-05-12T00:08:55.8350390+04:00'
WHERE [Id] = 4;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-12T00:08:55.8350470+04:00'
WHERE [Id] = 1;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-12T00:08:55.8350470+04:00'
WHERE [Id] = 2;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-12T00:08:55.8350470+04:00'
WHERE [Id] = 3;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-12T00:08:55.8350470+04:00'
WHERE [Id] = 4;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-12T00:08:55.8350480+04:00'
WHERE [Id] = 5;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-12T00:08:55.8350480+04:00'
WHERE [Id] = 6;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-12T00:08:55.8350480+04:00'
WHERE [Id] = 7;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-12T00:08:55.8350490+04:00'
WHERE [Id] = 8;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-12T00:08:55.8350490+04:00'
WHERE [Id] = 9;
SELECT @@ROWCOUNT;

GO

INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250511200855_FixOrdersTableNullableUserId', N'7.0.0');
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

CREATE TABLE [CarouselImages] (
    [Id] int NOT NULL IDENTITY,
    [Title] nvarchar(max) NOT NULL,
    [Description] nvarchar(max) NULL,
    [ImageUrl] nvarchar(max) NOT NULL,
    [LinkUrl] nvarchar(max) NULL,
    [DisplayOrder] int NOT NULL,
    [IsActive] bit NOT NULL,
    [CreatedAt] datetime2 NOT NULL,
    CONSTRAINT [PK_CarouselImages] PRIMARY KEY ([Id])
);
GO

UPDATE [Categories] SET [CreatedAt] = '2025-05-16T23:04:44.6887660+04:00'
WHERE [Id] = 1;
SELECT @@ROWCOUNT;

GO

UPDATE [Categories] SET [CreatedAt] = '2025-05-16T23:04:44.6887660+04:00'
WHERE [Id] = 2;
SELECT @@ROWCOUNT;

GO

UPDATE [Categories] SET [CreatedAt] = '2025-05-16T23:04:44.6887670+04:00'
WHERE [Id] = 3;
SELECT @@ROWCOUNT;

GO

UPDATE [Categories] SET [CreatedAt] = '2025-05-16T23:04:44.6887670+04:00'
WHERE [Id] = 4;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-16T23:04:44.6887760+04:00'
WHERE [Id] = 1;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-16T23:04:44.6887760+04:00'
WHERE [Id] = 2;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-16T23:04:44.6887760+04:00'
WHERE [Id] = 3;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-16T23:04:44.6887770+04:00'
WHERE [Id] = 4;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-16T23:04:44.6887770+04:00'
WHERE [Id] = 5;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-16T23:04:44.6887770+04:00'
WHERE [Id] = 6;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-16T23:04:44.6887780+04:00'
WHERE [Id] = 7;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-16T23:04:44.6887780+04:00'
WHERE [Id] = 8;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-16T23:04:44.6887780+04:00'
WHERE [Id] = 9;
SELECT @@ROWCOUNT;

GO

INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250516190444_AddCarouselImages', N'7.0.0');
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

UPDATE [Categories] SET [CreatedAt] = '2025-05-16T23:11:00.5700850+04:00'
WHERE [Id] = 1;
SELECT @@ROWCOUNT;

GO

UPDATE [Categories] SET [CreatedAt] = '2025-05-16T23:11:00.5700850+04:00'
WHERE [Id] = 2;
SELECT @@ROWCOUNT;

GO

UPDATE [Categories] SET [CreatedAt] = '2025-05-16T23:11:00.5700850+04:00'
WHERE [Id] = 3;
SELECT @@ROWCOUNT;

GO

UPDATE [Categories] SET [CreatedAt] = '2025-05-16T23:11:00.5700850+04:00'
WHERE [Id] = 4;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-16T23:11:00.5700930+04:00'
WHERE [Id] = 1;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-16T23:11:00.5700930+04:00'
WHERE [Id] = 2;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-16T23:11:00.5700940+04:00'
WHERE [Id] = 3;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-16T23:11:00.5700940+04:00'
WHERE [Id] = 4;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-16T23:11:00.5700940+04:00'
WHERE [Id] = 5;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-16T23:11:00.5700950+04:00'
WHERE [Id] = 6;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-16T23:11:00.5700950+04:00'
WHERE [Id] = 7;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-16T23:11:00.5700950+04:00'
WHERE [Id] = 8;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-16T23:11:00.5700950+04:00'
WHERE [Id] = 9;
SELECT @@ROWCOUNT;

GO

INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250516191100_AddCarouselImagesTable', N'7.0.0');
GO

COMMIT;
GO

BEGIN TRANSACTION;
GO

UPDATE [Categories] SET [CreatedAt] = '2025-05-16T23:23:24.1652040+04:00'
WHERE [Id] = 1;
SELECT @@ROWCOUNT;

GO

UPDATE [Categories] SET [CreatedAt] = '2025-05-16T23:23:24.1652040+04:00'
WHERE [Id] = 2;
SELECT @@ROWCOUNT;

GO

UPDATE [Categories] SET [CreatedAt] = '2025-05-16T23:23:24.1652040+04:00'
WHERE [Id] = 3;
SELECT @@ROWCOUNT;

GO

UPDATE [Categories] SET [CreatedAt] = '2025-05-16T23:23:24.1652050+04:00'
WHERE [Id] = 4;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-16T23:23:24.1652120+04:00'
WHERE [Id] = 1;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-16T23:23:24.1652130+04:00'
WHERE [Id] = 2;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-16T23:23:24.1652130+04:00'
WHERE [Id] = 3;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-16T23:23:24.1652130+04:00'
WHERE [Id] = 4;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-16T23:23:24.1652130+04:00'
WHERE [Id] = 5;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-16T23:23:24.1652140+04:00'
WHERE [Id] = 6;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-16T23:23:24.1652140+04:00'
WHERE [Id] = 7;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-16T23:23:24.1652140+04:00'
WHERE [Id] = 8;
SELECT @@ROWCOUNT;

GO

UPDATE [Products] SET [CreatedAt] = '2025-05-16T23:23:24.1652140+04:00'
WHERE [Id] = 9;
SELECT @@ROWCOUNT;

GO

INSERT INTO [__EFMigrationsHistory] ([MigrationId], [ProductVersion])
VALUES (N'20250516192324_AddCarouselImagesTable2', N'7.0.0');
GO

COMMIT;
GO

