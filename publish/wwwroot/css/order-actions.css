/* تنسيقات قائمة الإجراءات في صفحة إدارة الطلبات */

/* تنسيق زر الإجراءات */
.btn-outline-primary.dropdown-toggle {
    color: #6a0dad;
    border-color: #6a0dad;
    background-color: transparent;
}

.btn-outline-primary.dropdown-toggle:hover,
.btn-outline-primary.dropdown-toggle:focus,
.btn-outline-primary.dropdown-toggle:active,
.btn-outline-primary.dropdown-toggle.show {
    color: white;
    background-color: #6a0dad;
    border-color: #6a0dad;
}

/* تنسيق قائمة الإجراءات */
.dropdown-menu {
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.05);
    padding: 0.5rem 0;
    min-width: 200px;
    z-index: 1000;
    background-color: white;
    direction: rtl;
    text-align: right;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    color: #333;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background-color: rgba(106, 13, 173, 0.05);
    color: #6a0dad;
}

.dropdown-item.active {
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
}

.dropdown-item i {
    margin-left: 0.5rem;
    font-size: 1rem;
    width: 20px;
    text-align: center;
}

.dropdown-header {
    color: #6a0dad;
    font-weight: 600;
    font-size: 0.85rem;
    padding: 0.5rem 1rem;
}

.dropdown-divider {
    margin: 0.25rem 0;
    border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* تنسيق نموذج تحديث الحالة */
.dropdown-item button {
    background: none;
    border: none;
    padding: 0;
    margin: 0;
    width: 100%;
    text-align: right;
    color: inherit;
    font-size: inherit;
    cursor: pointer;
    display: flex;
    align-items: center;
}

.dropdown-item button i {
    margin-left: 0.5rem;
    font-size: 1rem;
    width: 20px;
    text-align: center;
}

.dropdown-item.disabled {
    color: #aaa;
    pointer-events: none;
}

/* تنسيق حالة الطلب */
.badge {
    padding: 0.4rem 0.6rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 30px;
}

/* تنسيق عنوان الحالة */
.status-header {
    background-color: #f8f9fa;
    color: #6a0dad;
    padding: 0.75rem 1rem;
    font-weight: 600;
    font-size: 1rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    margin-bottom: 0.5rem;
}

/* تنسيق قائمة الإجراءات في الشاشات الصغيرة */
@media (max-width: 768px) {
    .dropdown-menu {
        min-width: 180px;
    }
    
    .dropdown-item {
        padding: 0.4rem 0.75rem;
        font-size: 0.85rem;
    }
    
    .dropdown-header {
        padding: 0.4rem 0.75rem;
        font-size: 0.8rem;
    }
}
