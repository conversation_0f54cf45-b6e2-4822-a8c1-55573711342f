/* تنسيقات النافذة الجانبية للتصفية والمنتجات الأكثر مبيعاً */

/* زر فتح النافذة الجانبية */
.filter-button {
    position: fixed;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    background-color: #6a0dad;
    border: 1px solid #6a0dad;
    border-left: none;
    border-radius: 0 8px 8px 0;
    padding: 12px 8px;
    cursor: pointer;
    z-index: 999;
    box-shadow: 2px 0 10px rgba(106, 13, 173, 0.3);
    display: block;
    transition: all 0.3s ease;
}

.filter-button:hover {
    background-color: #5a0b9a;
    box-shadow: 2px 0 15px rgba(106, 13, 173, 0.5);
    transform: translateY(-50%) translateX(2px);
}

.filter-button i {
    font-size: 1.2rem;
    color: white;
}

/* إظهار الزر على الشاشات الصغيرة والمتوسطة فقط */
@media (max-width: 1199px) {
    .filter-button {
        display: block !important;
    }
}

/* إخفاء الزر على الشاشات الكبيرة */
@media (min-width: 1200px) {
    .filter-button {
        display: none !important;
    }
}

/* النافذة الجانبية */
.side-filter-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: none;
}

.side-filter-panel {
    position: fixed;
    top: 0;
    left: -350px;
    width: 320px;
    height: 100%;
    background-color: #fff;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    z-index: 1001;
    overflow-y: auto;
    transition: left 0.3s ease;
}

.side-filter-panel.active {
    left: 0;
}

/* رأس النافذة الجانبية */
.side-filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #eee;
}

.side-filter-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
}

.side-filter-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #333;
    padding: 0;
}

/* محتوى النافذة الجانبية */
.side-filter-content {
    padding: 15px;
}

/* رسالة ترحيبية */
.welcome-message {
    background: linear-gradient(135deg, #6a0dad, #8a2be2);
    color: white;
    padding: 12px 15px;
    border-radius: 10px;
    margin-bottom: 20px;
    text-align: center;
    font-size: 0.9rem;
    box-shadow: 0 2px 8px rgba(106, 13, 173, 0.2);
}

.welcome-message i {
    margin-left: 8px;
    font-size: 1rem;
}

/* قسم تصفية السعر */
.price-filter-container {
    margin-bottom: 20px;
}

.price-filter-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
}

.price-range-slider {
    width: 100%;
    margin-bottom: 15px;
}

.price-inputs {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
}

.price-input {
    width: 45%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
}

.price-filter-button {
    width: 100%;
    padding: 10px;
    background-color: #ffc0cb;
    color: #333;
    border: none;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.price-filter-button:hover {
    background-color: #ffb6c1;
}

/* قسم المنتجات الأكثر مبيعاً */
.best-sellers-container {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.best-sellers-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
}

.products-count {
    font-size: 0.9rem;
    color: #6a0dad;
    font-weight: 500;
    background: rgba(106, 13, 173, 0.1);
    padding: 2px 8px;
    border-radius: 12px;
    margin-right: 8px;
}

.best-seller-product, .best-seller-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    padding: 12px;
    border: 1px solid #f0f0f0;
    border-radius: 10px;
    transition: all 0.3s ease;
    background: #fafafa;
}

.best-seller-product:last-child {
    margin-bottom: 0;
}

.best-seller-product:hover, .best-seller-item:hover {
    background: white;
    border-color: #6a0dad;
    box-shadow: 0 4px 12px rgba(106, 13, 173, 0.15);
    transform: translateY(-2px);
}

.best-seller-img, .best-seller-image {
    width: 65px;
    height: 65px;
    object-fit: cover;
    border-radius: 10px;
    border: 2px solid #f0f0f0;
    transition: all 0.3s ease;
}

.best-seller-product:hover .best-seller-img,
.best-seller-product:hover .best-seller-image {
    border-color: #6a0dad;
    transform: scale(1.05);
}

.best-seller-image-container {
    position: relative;
    margin-left: 12px;
}

.best-seller-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #333;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    border: 2px solid white;
}

.best-seller-info {
    flex: 1;
}

.best-seller-name {
    font-size: 0.9rem;
    font-weight: 600;
    margin: 0 0 5px 0;
    color: #333;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.best-seller-price {
    font-size: 0.9rem;
    font-weight: 600;
    color: #6a0dad;
}

.best-seller-price .currency {
    font-size: 0.8rem;
    margin-right: 2px;
}

/* أزرار المفضلة */
.best-seller-actions {
    margin-right: 8px;
}

.wishlist-btn {
    background: white;
    border: 2px solid #f0f0f0;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #999;
}

.wishlist-btn:hover {
    border-color: #6a0dad;
    color: #6a0dad;
    transform: scale(1.1);
}

.wishlist-btn.active {
    background: #6a0dad;
    border-color: #6a0dad;
    color: white;
}

.wishlist-btn i {
    font-size: 14px;
}

/* رسائل التحميل والخطأ */
.loading-spinner, .loading {
    text-align: center;
    padding: 30px 20px;
    color: #6a0dad;
    font-size: 0.9rem;
    position: relative;
}

.loading-spinner::before, .loading::before {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #6a0dad;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.no-products-message,
.error-message {
    text-align: center;
    padding: 20px;
    color: #666;
    font-size: 0.9rem;
}

.error-message {
    color: #dc3545;
}

/* توست النافذة الجانبية */
.sidebar-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #6a0dad;
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 0.9rem;
    z-index: 10000;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(106, 13, 173, 0.3);
}

.sidebar-toast.show {
    transform: translateX(0);
    opacity: 1;
}

/* تنسيقات للشاشات المختلفة */
@media (max-width: 767px) {
    .side-filter-panel {
        width: 280px;
    }
}

@media (min-width: 768px) {
    .side-filter-panel {
        width: 350px;
    }

    .filter-button {
        padding: 15px 10px;
    }

    .filter-button i {
        font-size: 1.4rem;
    }
}

/* تنسيقات النافذة الجانبية الجديدة للتصفية */
.side-filter-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: none;
}

.side-filter-panel {
    position: fixed;
    top: 0;
    left: 0;
    width: 400px;
    height: 100%;
    background-color: white;
    z-index: 1001;
    overflow-y: auto;
    transform: translateX(-100%);
    transition: transform 0.3s ease-in-out;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.side-filter-panel.active {
    transform: translateX(0);
}

.side-filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eee;
    flex-direction: row-reverse;
    background: #f8f9fa;
}

.side-filter-header h2 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
    text-align: right;
}

.side-filter-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.side-filter-close:hover {
    background-color: rgba(0, 0, 0, 0.1);
    color: #333;
}

.side-filter-body {
    padding: 20px;
}

/* أقسام التصفية */
.filter-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.filter-section:last-child {
    border-bottom: none;
}

.filter-section-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    text-align: right;
}

.filter-section-title i {
    color: #6a0dad;
}

/* تصفية السعر */
.price-filter-container {
    text-align: right;
}

.price-range-slider {
    margin-bottom: 20px;
}

.price-range-slider .form-range {
    width: 100%;
    margin-bottom: 10px;
}

.price-range-labels {
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: #666;
}

.price-input-group {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
}

.price-input {
    flex: 1;
}

.price-input label {
    display: block;
    font-size: 0.9rem;
    font-weight: 500;
    color: #333;
    margin-bottom: 5px;
    text-align: right;
}

.price-input .form-control {
    text-align: center;
    font-size: 0.9rem;
}

.apply-price-filter {
    width: 100%;
    padding: 10px;
    font-size: 0.9rem;
    font-weight: 500;
    background-color: #6a0dad;
    border-color: #6a0dad;
}

.apply-price-filter:hover {
    background-color: #5a0b9a;
    border-color: #5a0b9a;
}

/* تصفية التوفر */
.availability-filters {
    text-align: right;
}

.availability-filters .form-check {
    margin-bottom: 10px;
}

.availability-filters .form-check-label {
    font-size: 0.95rem;
    color: #333;
    cursor: pointer;
}

.availability-filters .form-check-input {
    margin-left: 10px;
}

/* أزرار التحكم */
.filter-actions {
    display: flex;
    gap: 10px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #f0f0f0;
}

.filter-actions .btn {
    flex: 1;
    padding: 12px;
    font-size: 0.9rem;
    font-weight: 500;
}

.filter-actions .btn-primary {
    background-color: #6a0dad;
    border-color: #6a0dad;
}

.filter-actions .btn-primary:hover {
    background-color: #5a0b9a;
    border-color: #5a0b9a;
}

/* الزر الثابت للتصفية */
.fixed-filter-button {
    position: fixed;
    bottom: 20px;
    left: 20px;
    width: 50px;
    height: 50px;
    background: #6a0dad;
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    z-index: 999;
    box-shadow: 0 4px 12px rgba(106, 13, 173, 0.4);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.fixed-filter-button:hover {
    background: #5a0b9a;
    transform: scale(1.1);
    box-shadow: 0 6px 16px rgba(106, 13, 173, 0.6);
}

.fixed-filter-button:active {
    transform: scale(0.95);
}

/* تنسيق أزرار التحكم في الهاتف */
.mobile-controls-row {
    display: flex;
    align-items: center;
    width: 100%;
}

.mobile-grid-controls-horizontal {
    width: 100%;
}

/* تحديث تنسيق أزرار العرض */
.mobile-controls-top {
    
    border-radius: 8px;
    padding: 0px;
     
    border: 0px solid #f0f0f0;
    margin-bottom: 0px;
}

/* تنسيق للشاشات الصغيرة */
@media (max-width: 576px) {
    .side-filter-panel {
        width: 100%;
    }

    .fixed-filter-button {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
        bottom: 15px;
        left: 15px;
    }
}

/* توست التصفية للهاتف */
.mobile-filter-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #6a0dad;
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 0.9rem;
    z-index: 10000;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(106, 13, 173, 0.3);
}

.mobile-filter-toast.show {
    transform: translateX(0);
    opacity: 1;
}

/* تنسيق عناصر الترتيب وعدد المنتجات في الشاشات الصغيرة */
@media (max-width: 991px) {
    .controls-row {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 8px;
        flex-wrap: nowrap;
        background: white;
        padding: 10px;
        border-radius: 6px;
        border: 1px solid #e9ecef;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        margin-bottom: 10px;
    }

    .sort-options,
    .items-per-page {
        display: flex;
        align-items: center;
        gap: 5px;
        flex: 1;
        min-width: 0;
    }

    .sort-label,
    .items-per-page-label {
        font-size: 0.75rem;
        font-weight: 500;
        color: #333;
        margin: 0;
        flex-shrink: 0;
        white-space: nowrap;
    }

    .sort-select,
    .items-per-page-select {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 4px 8px;
        font-size: 0.75rem;
        background: white;
        color: #333;
        flex: 1;
        min-width: 80px;
        height: 32px;
    }

    .sort-select:focus,
    .items-per-page-select:focus {
        border-color: #6a0dad;
        outline: none;
        box-shadow: 0 0 0 1px rgba(106, 13, 173, 0.2);
    }

    .compare-button-container {
        flex-shrink: 0;
    }

    .compare-button-container .btn {
        padding: 4px 10px;
        font-size: 0.75rem;
        white-space: nowrap;
        height: 32px;
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .mobile-controls-top {
        margin-bottom: 8px;
    }

    .mobile-controls-top .mobile-grid-controls-horizontal {
        gap: 6px;
    }

    .mobile-controls-top .mobile-grid-option {
        padding: 8px 6px;
        min-width: auto;
        flex: 1;
    }

    .mobile-controls-top .mobile-grid-option i {
        font-size: 1rem;
        margin-bottom: 2px;
    }

    .mobile-controls-top .mobile-grid-option span {
        font-size: 0.65rem;
    }
}

/* تحسين التنسيق للشاشات الصغيرة جداً */
@media (max-width: 576px) {
    .controls-row {
        flex-direction: row;
        gap: 6px;
        padding: 8px;
        flex-wrap: nowrap;
    }

    .sort-options,
    .items-per-page {
        flex-direction: row;
        gap: 3px;
        flex: 1;
    }

    .sort-label,
    .items-per-page-label {
        font-size: 0.7rem;
        display: none; /* إخفاء النصوص في الشاشات الصغيرة جداً */
    }

    .sort-select,
    .items-per-page-select {
        min-width: 70px;
        font-size: 0.7rem;
        padding: 3px 6px;
        height: 28px;
    }

    .compare-button-container .btn {
        padding: 3px 8px;
        font-size: 0.7rem;
        height: 28px;
    }

    .compare-button-container .btn .badge {
        font-size: 0.6rem;
        padding: 1px 4px;
    }

    .mobile-controls-top .mobile-grid-option {
        padding: 6px 4px;
    }

    .mobile-controls-top .mobile-grid-option i {
        font-size: 0.9rem;
    }

    .mobile-controls-top .mobile-grid-option span {
        font-size: 0.6rem;
    }
}
