/* تنسيقات النافذة الجانبية للتصفية والمنتجات الأكثر مبيعاً */

/* زر فتح النافذة الجانبية */
.filter-button {
    position: fixed;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    background-color: #6a0dad;
    border: 1px solid #6a0dad;
    border-left: none;
    border-radius: 0 8px 8px 0;
    padding: 12px 8px;
    cursor: pointer;
    z-index: 999;
    box-shadow: 2px 0 10px rgba(106, 13, 173, 0.3);
    display: block;
    transition: all 0.3s ease;
}

.filter-button:hover {
    background-color: #5a0b9a;
    box-shadow: 2px 0 15px rgba(106, 13, 173, 0.5);
    transform: translateY(-50%) translateX(2px);
}

.filter-button i {
    font-size: 1.2rem;
    color: white;
}

/* إظهار الزر على الشاشات الصغيرة والمتوسطة فقط */
@media (max-width: 1199px) {
    .filter-button {
        display: block !important;
    }
}

/* إخفاء الزر على الشاشات الكبيرة */
@media (min-width: 1200px) {
    .filter-button {
        display: none !important;
    }
}

/* النافذة الجانبية */
.side-filter-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: none;
}

.side-filter-panel {
    position: fixed;
    top: 0;
    left: -350px;
    width: 320px;
    height: 100%;
    background-color: #fff;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    z-index: 1001;
    overflow-y: auto;
    transition: left 0.3s ease;
}

.side-filter-panel.active {
    left: 0;
}

/* رأس النافذة الجانبية */
.side-filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #eee;
}

.side-filter-title {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
}

.side-filter-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #333;
    padding: 0;
}

/* محتوى النافذة الجانبية */
.side-filter-content {
    padding: 15px;
}

/* رسالة ترحيبية */
.welcome-message {
    background: linear-gradient(135deg, #6a0dad, #8a2be2);
    color: white;
    padding: 12px 15px;
    border-radius: 10px;
    margin-bottom: 20px;
    text-align: center;
    font-size: 0.9rem;
    box-shadow: 0 2px 8px rgba(106, 13, 173, 0.2);
}

.welcome-message i {
    margin-left: 8px;
    font-size: 1rem;
}

/* قسم تصفية السعر */
.price-filter-container {
    margin-bottom: 20px;
}

.price-filter-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
}

.price-range-slider {
    width: 100%;
    margin-bottom: 15px;
}

.price-inputs {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
}

.price-input {
    width: 45%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    text-align: center;
}

.price-filter-button {
    width: 100%;
    padding: 10px;
    background-color: #ffc0cb;
    color: #333;
    border: none;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.price-filter-button:hover {
    background-color: #ffb6c1;
}

/* قسم المنتجات الأكثر مبيعاً */
.best-sellers-container {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.best-sellers-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
}

.products-count {
    font-size: 0.9rem;
    color: #6a0dad;
    font-weight: 500;
    background: rgba(106, 13, 173, 0.1);
    padding: 2px 8px;
    border-radius: 12px;
    margin-right: 8px;
}

.best-seller-product, .best-seller-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    padding: 12px;
    border: 1px solid #f0f0f0;
    border-radius: 10px;
    transition: all 0.3s ease;
    background: #fafafa;
}

.best-seller-product:last-child {
    margin-bottom: 0;
}

.best-seller-product:hover, .best-seller-item:hover {
    background: white;
    border-color: #6a0dad;
    box-shadow: 0 4px 12px rgba(106, 13, 173, 0.15);
    transform: translateY(-2px);
}

.best-seller-img, .best-seller-image {
    width: 65px;
    height: 65px;
    object-fit: cover;
    border-radius: 10px;
    border: 2px solid #f0f0f0;
    transition: all 0.3s ease;
}

.best-seller-product:hover .best-seller-img,
.best-seller-product:hover .best-seller-image {
    border-color: #6a0dad;
    transform: scale(1.05);
}

.best-seller-image-container {
    position: relative;
    margin-left: 12px;
}

.best-seller-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #333;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    border: 2px solid white;
}

.best-seller-info {
    flex: 1;
}

.best-seller-name {
    font-size: 0.9rem;
    font-weight: 600;
    margin: 0 0 5px 0;
    color: #333;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.best-seller-price {
    font-size: 0.9rem;
    font-weight: 600;
    color: #6a0dad;
}

.best-seller-price .currency {
    font-size: 0.8rem;
    margin-right: 2px;
}

/* أزرار المفضلة */
.best-seller-actions {
    margin-right: 8px;
}

.wishlist-btn {
    background: white;
    border: 2px solid #f0f0f0;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #999;
}

.wishlist-btn:hover {
    border-color: #6a0dad;
    color: #6a0dad;
    transform: scale(1.1);
}

.wishlist-btn.active {
    background: #6a0dad;
    border-color: #6a0dad;
    color: white;
}

.wishlist-btn i {
    font-size: 14px;
}

/* رسائل التحميل والخطأ */
.loading-spinner, .loading {
    text-align: center;
    padding: 30px 20px;
    color: #6a0dad;
    font-size: 0.9rem;
    position: relative;
}

.loading-spinner::before, .loading::before {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #6a0dad;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.no-products-message,
.error-message {
    text-align: center;
    padding: 20px;
    color: #666;
    font-size: 0.9rem;
}

.error-message {
    color: #dc3545;
}

/* توست النافذة الجانبية */
.sidebar-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #6a0dad;
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    font-size: 0.9rem;
    z-index: 10000;
    transform: translateX(100%);
    opacity: 0;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(106, 13, 173, 0.3);
}

.sidebar-toast.show {
    transform: translateX(0);
    opacity: 1;
}

/* تنسيقات للشاشات المختلفة */
@media (max-width: 767px) {
    .side-filter-panel {
        width: 280px;
    }
}

@media (min-width: 768px) {
    .side-filter-panel {
        width: 350px;
    }

    .filter-button {
        padding: 15px 10px;
    }

    .filter-button i {
        font-size: 1.4rem;
    }
}
