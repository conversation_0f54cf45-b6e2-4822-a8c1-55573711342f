/* Estilos para las opciones de visualización */

/* Panel de opciones de visualización */
.display-options-panel {
    position: fixed;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    background-color: var(--card-bg);
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    box-shadow: var(--box-shadow-lg);
    padding: 1rem;
    z-index: 1000;
    transition: transform 0.3s ease;
    transform: translateX(-100%);
    max-width: 300px;
}

.display-options-panel.show {
    transform: translateX(0);
}

.display-options-toggle {
    position: absolute;
    top: 50%;
    right: -40px;
    transform: translateY(-50%);
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--box-shadow);
}

.display-options-toggle:hover {
    background-color: var(--accent-color);
}

.display-options-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.display-options-title {
    font-weight: 700;
    color: var(--primary-color);
    margin: 0;
    font-size: 1rem;
}

.display-options-close {
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    font-size: 1.25rem;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.display-options-close:hover {
    color: var(--danger-color);
}

.display-options-section {
    margin-bottom: 1rem;
}

.display-options-section-title {
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.5rem;
    font-size: 0.85rem;
}

.display-controls {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.display-control {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    background-color: rgba(0, 0, 0, 0.05);
    color: var(--text-color);
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.75rem;
    min-width: 30px;
    text-align: center;
}

.display-control:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.display-control.active {
    background-color: var(--primary-color);
    color: white;
}

.display-control i {
    font-size: 0.9rem;
}

.display-switch {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.display-switch-label {
    font-size: 0.85rem;
    color: var(--text-color);
}

/* Estilos para diferentes modos de visualización */
.product-grid.display-grid {
    display: grid;
    gap: 1.5rem;
}

.product-grid.display-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.product-grid.display-list .product-card {
    display: flex;
    flex-direction: row;
    height: auto;
}

.product-grid.display-list .product-img-container {
    width: 200px;
    min-width: 200px;
    height: 200px;
    border-radius: var(--border-radius) 0 0 var(--border-radius);
}

.product-grid.display-list .product-content {
    flex: 1;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

/* Efectos de imagen */
.product-card.effect-zoom:hover .product-img {
    transform: scale(1.1);
}

.product-card.effect-fade .product-img {
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.product-card.effect-fade:hover .product-img {
    opacity: 1;
}

.product-card.effect-none .product-img {
    transform: none;
}

.product-card.effect-none:hover .product-img {
    transform: none;
}

/* Estilos para diferentes tamaños de imagen */
.image-size-tiny .product-img-container,
.image-size-tiny .card-img-container {
    height: 120px;
}

.image-size-small .product-img-container,
.image-size-small .card-img-container {
    height: 160px;
}

.image-size-medium .product-img-container,
.image-size-medium .card-img-container {
    height: 200px;
}

.image-size-large .product-img-container,
.image-size-large .card-img-container {
    height: 260px;
}

/* Estilos para diferentes números de elementos por fila */
.items-per-row-2 {
    grid-template-columns: repeat(auto-fill, minmax(calc(50% - 1rem), 1fr)) !important;
}

.items-per-row-3 {
    grid-template-columns: repeat(auto-fill, minmax(calc(33.333% - 1rem), 1fr)) !important;
}

.items-per-row-4 {
    grid-template-columns: repeat(auto-fill, minmax(calc(25% - 1rem), 1fr)) !important;
}

.items-per-row-6 {
    grid-template-columns: repeat(auto-fill, minmax(calc(16.666% - 1rem), 1fr)) !important;
}

/* تنسيقات متجاوبة للشاشات الصغيرة */
@media (max-width: 576px) {
    .items-per-row-6 {
        grid-template-columns: repeat(2, 1fr) !important;
    }

    .items-per-row-4 {
        grid-template-columns: repeat(2, 1fr) !important;
    }

    .items-per-row-3 {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

@media (min-width: 577px) and (max-width: 768px) {
    .items-per-row-6 {
        grid-template-columns: repeat(3, 1fr) !important;
    }

    .items-per-row-4 {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

@media (min-width: 769px) and (max-width: 992px) {
    .items-per-row-6 {
        grid-template-columns: repeat(4, 1fr) !important;
    }
}

/* دعم للفئات القديمة للتوافق - تنسيقات أساسية */
.product-grid {
    display: grid !important;
    gap: 1rem !important;
    width: 100% !important;
}

.grid-6,
.products-grid.grid-6,
.product-grid.grid-6 {
    grid-template-columns: repeat(6, 1fr) !important;
}

.grid-4,
.products-grid.grid-4,
.product-grid.grid-4 {
    grid-template-columns: repeat(4, 1fr) !important;
}

.grid-3,
.products-grid.grid-3,
.product-grid.grid-3 {
    grid-template-columns: repeat(3, 1fr) !important;
}

.grid-2,
.products-grid.grid-2,
.product-grid.grid-2 {
    grid-template-columns: repeat(2, 1fr) !important;
}

/* تأكيد أن العناصر تظهر بشكل صحيح */
.product-grid .product-card,
.product-grid .product-item {
    width: 100% !important;
    height: auto !important;
}

/* تنسيقات متجاوبة للفئات القديمة */
@media (max-width: 576px) {
    .grid-6, .grid-4, .grid-3, .grid-2,
    .products-grid.grid-6, .products-grid.grid-4, .products-grid.grid-3, .products-grid.grid-2,
    .product-grid.grid-6, .product-grid.grid-4, .product-grid.grid-3, .product-grid.grid-2 {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

@media (min-width: 577px) and (max-width: 768px) {
    .grid-6,
    .products-grid.grid-6,
    .product-grid.grid-6 {
        grid-template-columns: repeat(3, 1fr) !important;
    }

    .grid-4,
    .products-grid.grid-4,
    .product-grid.grid-4 {
        grid-template-columns: repeat(3, 1fr) !important;
    }

    .grid-3,
    .products-grid.grid-3,
    .product-grid.grid-3 {
        grid-template-columns: repeat(3, 1fr) !important;
    }

    .grid-2,
    .products-grid.grid-2,
    .product-grid.grid-2 {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

@media (min-width: 769px) and (max-width: 992px) {
    .grid-6,
    .products-grid.grid-6,
    .product-grid.grid-6 {
        grid-template-columns: repeat(4, 1fr) !important;
    }

    .grid-4,
    .products-grid.grid-4,
    .product-grid.grid-4 {
        grid-template-columns: repeat(3, 1fr) !important;
    }

    .grid-3,
    .products-grid.grid-3,
    .product-grid.grid-3 {
        grid-template-columns: repeat(3, 1fr) !important;
    }
}

@media (min-width: 993px) {
    .grid-6,
    .products-grid.grid-6,
    .product-grid.grid-6 {
        grid-template-columns: repeat(6, 1fr) !important;
    }

    .grid-4,
    .products-grid.grid-4,
    .product-grid.grid-4 {
        grid-template-columns: repeat(4, 1fr) !important;
    }

    .grid-3,
    .products-grid.grid-3,
    .product-grid.grid-3 {
        grid-template-columns: repeat(3, 1fr) !important;
    }

    .grid-2,
    .products-grid.grid-2,
    .product-grid.grid-2 {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

/* تنسيقات أزرار الهاتف */
.mobile-grid-1,
.products-grid.mobile-grid-1,
.product-grid.mobile-grid-1 {
    grid-template-columns: 1fr !important;
}

.mobile-grid-2,
.products-grid.mobile-grid-2,
.product-grid.mobile-grid-2 {
    grid-template-columns: repeat(2, 1fr) !important;
}

.mobile-grid-3,
.products-grid.mobile-grid-3,
.product-grid.mobile-grid-3 {
    grid-template-columns: repeat(3, 1fr) !important;
}

/* تنسيقات أزرار العرض الأفقية للهواتف */
.mobile-view-controls,
.mobile-view-controls-top {
    background: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #f0f0f0;
    margin-bottom: 15px;
}

.mobile-grid-controls-horizontal {
    display: flex !important;
    gap: 8px !important;
    justify-content: center !important;
    align-items: center !important;
    flex-wrap: nowrap !important;
}

.mobile-grid-controls-horizontal .mobile-grid-option {
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    padding: 12px 8px !important;
    background: #f8f9fa !important;
    border: 1px solid #e9ecef !important;
    border-radius: 8px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    flex: 1 !important;
    max-width: 100px !important;
    text-align: center !important;
}

.mobile-grid-controls-horizontal .mobile-grid-option:hover {
    background: rgba(106, 13, 173, 0.1) !important;
    border-color: #6a0dad !important;
    color: #6a0dad !important;
}

.mobile-grid-controls-horizontal .mobile-grid-option.active {
    background: #6a0dad !important;
    border-color: #6a0dad !important;
    color: white !important;
}

.mobile-grid-controls-horizontal .mobile-grid-option i {
    font-size: 1.2rem !important;
    margin-bottom: 4px !important;
}

.mobile-grid-controls-horizontal .mobile-grid-option span {
    font-size: 0.75rem !important;
    font-weight: 500 !important;
    line-height: 1.2 !important;
}

/* تنسيقات خاصة للمنتجات في الصفحة الرئيسية */
.featured-products .product-item,
.all-products .product-item {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
}

.featured-products .product-item:hover,
.all-products .product-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

/* CSS إضافي لضمان التجاوب الصحيح */
@media (max-width: 480px) {
    .product-grid,
    .products-grid {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 8px !important;
    }

    .product-card {
        min-height: auto !important;
    }

    .product-img-container,
    .product-item-img-container {
        height: 150px !important;
    }
}

/* Estilos para el botón flotante de opciones de visualización en móvil */
.display-options-fab {
    position: fixed;
    bottom: 20px;
    right: 20px;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--box-shadow-lg);
    cursor: pointer;
    z-index: 1000;
    transition: var(--transition);
}

.display-options-fab:hover {
    background-color: var(--accent-color);
    transform: translateY(-5px);
}

.display-options-fab i {
    font-size: 1.25rem;
}

/* Estilos para la galería de imágenes mejorada - Completamente rediseñado */
.category-card {
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    height: 280px;
    transition: all 0.5s cubic-bezier(0.165, 0.84, 0.44, 1);
    border: 1px solid rgba(142, 36, 170, 0.05);
}

.category-card:hover {
    transform: translateY(-15px);
    box-shadow: 0 15px 30px rgba(142, 36, 170, 0.15);
}

.category-card-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.7s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.category-card:hover .category-card-img {
    transform: scale(1.1);
}

.category-card-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 2rem;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.85), transparent);
    color: white;
    transition: all 0.5s ease;
}

.category-card:hover .category-card-overlay {
    background: linear-gradient(to top, rgba(142, 36, 170, 0.85), transparent);
    padding-bottom: 2.5rem;
}

.category-card-title {
    font-weight: 700;
    margin-bottom: 0.75rem;
    font-size: 1.4rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.5px;
}

.category-card-count {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-bottom: 1.25rem;
    background-color: rgba(255, 255, 255, 0.2);
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 30px;
}

.category-card-btn {
    display: inline-flex;
    align-items: center;
    padding: 0.6rem 1.2rem;
    background-color: white;
    color: var(--primary-color);
    border-radius: 30px;
    font-size: 0.9rem;
    font-weight: 700;
    text-decoration: none;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.category-card-btn:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

/* تنسيقات عامة لأزرار العرض */
.view-options {
    display: flex !important;
    align-items: center;
    gap: 10px;
}

.view-mode {
    display: flex !important;
    gap: 5px;
}

.view-mode-option {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #fff;
    color: #666;
    display: flex !important;
    align-items: center;
    justify-content: center;
}

.view-mode-option:hover {
    background-color: #f8f9fa;
    border-color: #6a0dad;
    color: #6a0dad;
}

.view-mode-option.active {
    background-color: #6a0dad !important;
    border-color: #6a0dad !important;
    color: white !important;
}

.mobile-display-btn {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: #fff;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
}

.mobile-display-btn:hover {
    background-color: #6a0dad;
    border-color: #6a0dad;
    color: white;
}

/* إظهار أزرار العرض في جميع الصفحات */
.view-mode.d-none.d-lg-flex {
    display: flex !important;
}

.mobile-display-toggle.d-block.d-lg-none {
    display: block !important;
}

/* تأكيد إظهار أزرار العرض */
@media (min-width: 992px) {
    .view-mode {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    .view-mode-option {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        min-width: 40px;
        min-height: 40px;
    }

    .mobile-display-toggle {
        display: none !important;
    }
}

@media (max-width: 991.98px) {
    .mobile-display-toggle {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    .view-mode {
        display: none !important;
    }
}

/* إصلاح مشكلة Bootstrap classes */
.d-none.d-lg-flex {
    display: none !important;
}

@media (min-width: 992px) {
    .d-none.d-lg-flex {
        display: flex !important;
    }
}

.d-block.d-lg-none {
    display: block !important;
}

@media (min-width: 992px) {
    .d-block.d-lg-none {
        display: none !important;
    }
}

/* Estilos responsivos */
@media (max-width: 767.98px) {
    .display-options-panel {
        top: auto;
        bottom: 0;
        left: 0;
        right: 0;
        transform: translateY(100%);
        border-radius: var(--border-radius) var(--border-radius) 0 0;
        max-width: 100%;
    }

    .display-options-panel.show {
        transform: translateY(0);
    }

    .display-options-toggle {
        top: -40px;
        right: 20px;
        transform: none;
        border-radius: var(--border-radius) var(--border-radius) 0 0;
    }

    .product-grid.display-list .product-card {
        flex-direction: column;
    }

    .product-grid.display-list .product-img-container {
        width: 100%;
        border-radius: var(--border-radius) var(--border-radius) 0 0;
    }

    .items-per-row-2,
    .items-per-row-3,
    .items-per-row-4,
    .items-per-row-6 {
        grid-template-columns: repeat(auto-fill, minmax(calc(50% - 0.5rem), 1fr)) !important;
    }
}
