/* نافذة جانبية للموبايل - CSS منفصل */

/* النافذة الجانبية الأساسية - نهج جديد مطابق لنافذة التسجيل */
.mobile-sidebar-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
    z-index: 1000 !important;
    display: none !important;
}

.mobile-sidebar {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 400px !important;
    height: 100% !important;
    background-color: white !important;
    z-index: 1001 !important;
    overflow-y: auto !important;
    transform: translateX(-100%) !important;
    transition: transform 0.3s ease-in-out !important;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1) !important;
}

.mobile-sidebar.active {
    transform: translateX(0) !important;
}

/* رأس النافذة */
.mobile-sidebar-header {
    padding: 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
}

.mobile-sidebar-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
    display: flex;
    align-items: center;
}

.mobile-sidebar-close {
    background: none;
    border: none;
    font-size: 20px;
    color: #666;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.2s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mobile-sidebar-close:hover {
    background: #e9ecef;
    color: #333;
}

/* محتوى النافذة */
.mobile-sidebar-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    background: #ffffff !important;
    min-height: 500px !important;
    border: 2px solid red !important; /* للاختبار */
}

/* أقسام المحتوى */
.mobile-sidebar-section {
    margin-bottom: 30px !important;
    padding: 20px !important;
    background: #f8f9fa !important;
    border: 2px solid green !important; /* للاختبار */
    border-radius: 8px !important;
    min-height: 80px !important;
}

.mobile-sidebar-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.mobile-sidebar-section-title {
    font-size: 18px !important;
    font-weight: 700 !important;
    color: #000 !important;
    margin-bottom: 15px !important;
    display: flex !important;
    align-items: center !important;
    background: yellow !important; /* للاختبار */
    padding: 10px !important;
}

.mobile-sidebar-section-title i {
    color: #007bff;
    margin-left: 8px;
}

/* خيارات العرض */
.mobile-grid-controls {
    display: flex !important;
    gap: 15px !important;
    justify-content: center !important;
    flex-wrap: wrap !important;
    background: lightblue !important; /* للاختبار */
    padding: 15px !important;
    border-radius: 8px !important;
}

.mobile-grid-option {
    width: 40px;
    height: 30px;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    padding: 8px;
    text-align: center;
}

.mobile-grid-option:hover {
    border-color: #007bff;
    background: #f8f9ff;
}

.mobile-grid-option.active {
    border-color: #007bff;
    background: #007bff;
    color: white;
}

.mobile-grid-option i {
    font-size: 18px;
    margin-bottom: 5px;
}

.mobile-grid-option span {
    font-size: 11px;
    line-height: 1.2;
    color: inherit;
}

.mobile-grid-option.active span {
    color: white;
}

/* القوائم المنسدلة */
.mobile-sort-select,
.mobile-items-select {
    width: 100%;
    padding: 12px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background: white;
    font-size: 14px;
    color: #333;
    cursor: pointer;
}

.mobile-sort-select:focus,
.mobile-items-select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

/* تصفية السعر */
.mobile-price-filter {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.mobile-price-slider {
    width: 100%;
    margin: 15px 0;
    height: 6px;
    border-radius: 3px;
    background: #dee2e6;
    outline: none;
    cursor: pointer;
}

.mobile-price-inputs {
    display: flex;
    gap: 10px;
    margin: 15px 0;
}

.mobile-price-input {
    flex: 1;
    padding: 10px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    text-align: center;
    font-size: 13px;
    background: white;
    color: #666;
}

.mobile-price-apply-btn {
    width: 100%;
    padding: 12px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: background 0.2s ease;
}

.mobile-price-apply-btn:hover {
    background: #0056b3;
}

/* تطبيق فقط على الشاشات الصغيرة */
@media (max-width: 768px) {
    .mobile-sidebar-overlay {
        display: block;
    }
}

@media (min-width: 769px) {
    .mobile-sidebar-overlay {
        display: none !important;
    }
}
