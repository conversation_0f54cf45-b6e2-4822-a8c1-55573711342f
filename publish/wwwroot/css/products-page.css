/* Nuevo estilo para la página de productos - inspirado en ameeraa.online */

/* Encabezado de la página de productos */
.products-header {
    background-color: #ffffff;
    padding: 1rem 0;
    margin-bottom: -0.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.products-header h1 {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
    position: relative;
    display: inline-block;
}

.products-header h1::after {
    content: "→";
    margin-right: 0.5rem;
    font-size: 1.5rem;
    opacity: 0.7;
}

.products-title-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
}

.breadcrumb-container {
    display: flex;
    align-items: center;
}

.breadcrumb-item {
    color: var(--text-muted);
    font-size: 0.85rem;
    text-decoration: none;
}

.breadcrumb-item:hover {
    color: var(--primary-color);
}

.breadcrumb-separator {
    margin: 0 0.5rem;
    color: var(--text-muted);
    font-size: 0.7rem;
}

/* Filtro de precio */
.price-filter-container {
    background-color: #ffffff;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 2rem;
}

.price-filter-title {
    font-size: 1rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--dark-color);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.price-range-slider {
    width: 100%;
    margin-bottom: 1rem;
}

.price-inputs {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.price-input {
    width: 45%;
    padding: 0.5rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    text-align: center;
}

.price-filter-button {
    width: 100%;
    padding: 0.6rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.price-filter-button:hover {
    background-color: var(--secondary-color);
}

/* Opciones de visualización */
.view-options {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.view-mode {
    display: flex;
    align-items: center;
}

.view-mode-option {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0.5rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.1rem;
}

.view-mode-option:hover {
    background-color: rgba(106, 13, 173, 0.1);
    color: var(--primary-color);
    border-color: rgba(106, 13, 173, 0.2);
}

.view-mode-option.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.products-count {
    font-size: 0.9rem;
    color: var(--text-muted);
    margin-right: 1rem;
    margin-left: 1rem;
    background-color: rgba(106, 13, 173, 0.1);
    padding: 0.5rem 1rem;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
}

.products-count span {
    font-weight: 700;
    color: #0d4379;
    margin: 0 0.25rem;
}

.sort-options {
    display: flex;
    align-items: center;
}

.sort-label {
    font-size: 0.85rem;
    color: var(--text-muted);
    margin-left: 0.5rem;
}

.sort-select, .items-per-page-select {
    padding: 0.4rem 0.8rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    font-size: 0.85rem;
    color: var(--dark-color);
    background-color: #ffffff;
}

.items-per-page {
    display: flex;
    align-items: center;
    margin-right: 1rem;
}

.items-per-page-label {
    font-size: 0.85rem;
    color: var(--text-muted);
    margin-left: 0.5rem;
}

.compare-button-container {
    margin-right: 1rem;
}

.compare-count {
    margin-right: 0.25rem;
}

/* Nuevo diseño de la cuadrícula de productos */
.products-container {
    display: flex;
    flex-wrap: wrap;
}

.products-sidebar {
    width: 25%;
    padding-left: 1.5rem;
}

.products-main {
    width: 75%;
}

.product-grid {
    display: grid;
    gap: 1rem;
}

.product-grid.grid-16 {
    grid-template-columns: repeat(16, 1fr);
}

.product-grid.grid-6 {
    grid-template-columns: repeat(6, 1fr);
}

.product-grid.grid-4 {
    grid-template-columns: repeat(4, 1fr);
}

.product-grid.grid-2 {
    grid-template-columns: repeat(2, 1fr);
}

/* Nuevo diseño de tarjeta de producto */
.product-card {
    background-color: #ffffff;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(0, 0, 0, 0.03);
}

.product-card:hover {
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transform: translateY(-5px);
}

.product-img-container {
    position: relative;
    overflow: hidden;
    height: 200px;
}

.product-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.product-card:hover .product-img {
    transform: scale(1.05);
}

/* Menú flotante de acciones */
.product-actions-menu {
    position: absolute;
    bottom: -50px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 8px 0;
    transition: all 0.3s ease;
    z-index: 3;
}

.product-card:hover .product-actions-menu {
    bottom: 0;
}

.product-action-btn {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 5px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #333;
}

.product-action-btn:hover {
    background-color: var(--primary-color);
    color: white;
}

.product-action-btn.active {
    background-color: var(--primary-color);
    color: white;
}

.toggle-carousel-btn {
    background-color: #f8f9fa;
    color: #6c757d;
}

.toggle-carousel-btn.active {
    background-color: #ffc107;
    color: #212529;
}

.toggle-carousel-btn:hover {
    background-color: #ffc107;
    color: #212529;
}

.toggle-availability-btn.available {
    background-color: #28a745;
    color: white;
}

.toggle-availability-btn.not-available {
    background-color: #dc3545;
    color: white;
}

.toggle-availability-btn:hover {
    opacity: 0.8;
}

/* مؤشر توفر المنتج */
.availability-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 700;
    z-index: 2;
}

.availability-badge.available {
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
    border: 1px solid rgba(106, 13, 173, 0.3);
}

.availability-badge.not-available {
    background-color: rgba(220, 53, 69, 0.2);
    color: #dc3545;
    border: 1px solid #dc3545;
}

/* Etiqueta de descuento */
.product-discount {
    position: absolute;
    top: 10px;
    left: 10px;
    background-color: #ff6b6b;
    color: white;
    font-size: 0.8rem;
    font-weight: 700;
    padding: 5px 10px;
    border-radius: 20px;
    z-index: 2;
}

/* Etiqueta de comparación */
.compare-badge {
    position: absolute;
    top: 10px;
    left: 10px;
    background-color: #333;
    color: white;
    font-size: 0.8rem;
    padding: 5px 10px;
    border-radius: 20px;
    z-index: 2;
    opacity: 0;
    transition: opacity 0.3s ease;
    display: none; /* إخفاء كلمة "للمقارنة" */
}

.product-card.in-compare .compare-badge {
    opacity: 1;
}

.wishlist-btn {
    position: absolute;
    top: 10px;
    left: 10px; /* تغيير من right إلى left لوضع زر المفضلة في الزاوية العلوية اليسرى */
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background-color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 3; /* زيادة z-index لضمان ظهور الزر فوق العناصر الأخرى */
    font-size: 1.1rem;
}

.wishlist-btn:hover {
    background-color: #ff6b6b;
    color: white;
}

.wishlist-btn.active {
    background-color: #ff6b6b;
    color: white;
}

.wishlist-btn.active i::before {
    content: "\F415";
}

.product-content {
    padding: 1.25rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    text-align: center;
}

.product-category {
    color: var(--text-muted);
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
}

.product-title {
    font-weight: 700;
    font-size: 1rem;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
    height: 2.4rem;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
}

.product-price {
    font-weight: 700;
    font-size: 1.1rem;
    color: var(--primary-color);
    margin-top: auto;
    padding-top: 0.5rem;
}

.product-price .currency {
    font-size: 0.8rem;
    margin-left: 0.25rem;
}



/* Responsive */
@media (max-width: 991px) {
    .products-sidebar {
        width: 30%;
    }

    .products-main {
        width: 70%;
    }

    .product-grid.grid-16,
    .product-grid.grid-6,
    .product-grid.grid-4 {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 767px) {
    .products-container {
        flex-direction: column;
    }

    .products-sidebar {
        display: none; /* إخفاء الشريط الجانبي في وضع الهاتف */
    }

    .products-main {
        width: 100%;
    }

    .product-grid.grid-16,
    .product-grid.grid-6,
    .product-grid.grid-4 {
        grid-template-columns: repeat(2, 1fr);
    }

    .view-options {
        flex-direction: row-reverse;
        align-items: flex-start;
    }

    .sort-options {
        margin-top: 0rem;
    }
}

@media (max-width: 575px) {
    .product-grid.grid-16,
    .product-grid.grid-6,
    .product-grid.grid-4,
    .product-grid.grid-2 {
        grid-template-columns: 1fr;
    }
}

/* تنسيق أزرار التنقل بين الصفحات */
.pagination-container {
    margin: 2rem 0;
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.25rem;
}

.page-item {
    margin: 0 0.1rem;
}

.page-link {
    color: var(--available-text);
    border-radius: 4px;
    border: 1px solid rgba(106, 13, 173, 0.2);
    padding: 0.5rem 0.75rem;
    font-size: 0.85rem;
    transition: all 0.3s ease;
}

.page-link:hover {
    background-color: rgba(106, 13, 173, 0.1);
    color: var(--primary-color);
    border-color: rgba(106, 13, 173, 0.3);
}

.page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--available-text);
}

.page-item.disabled .page-link {
    color: #6c757d;
    pointer-events: none;
    background-color: #fff;
    border-color: #dee2e6;
}
