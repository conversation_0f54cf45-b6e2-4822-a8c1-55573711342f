/* إصلاح مشكلة حركة القائمة المنسدلة */

/* إزالة جميع التأثيرات الحركية من القوائم المنسدلة */
.dropdown-menu {
    animation: none !important;
    transition: none !important;
    transform: none !important;
}

/* تثبيت موضع القائمة المنسدلة في الهيدر */
.header-icons-container .dropdown,
.login-register .dropdown {
    position: relative !important;
}

.header-icons-container .dropdown-menu,
.login-register .dropdown-menu {
    position: absolute !important;
    top: 100% !important;
    right: 0 !important;
    left: auto !important;
    transform: none !important;
    animation: none !important;
    transition: none !important;
    margin-top: 5px !important;
    opacity: 1 !important;
}

/* إزالة تأثيرات Bootstrap الافتراضية */
.dropdown-menu.show {
    animation: none !important;
    transition: none !important;
    transform: none !important;
}

/* تثبيت الموضع عند الظهور */
.dropdown-menu[data-bs-popper] {
    position: absolute !important;
    top: 100% !important;
    right: 0 !important;
    left: auto !important;
    transform: none !important;
    margin-top: 5px !important;
}
