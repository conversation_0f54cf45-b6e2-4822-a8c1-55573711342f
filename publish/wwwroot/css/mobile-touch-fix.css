/* إصلاح مشاكل التمرير باللمس في الهواتف */

/* تحسين التمرير العام */
html {
    -webkit-overflow-scrolling: touch !important;
    touch-action: manipulation !important;
}

body {
    -webkit-overflow-scrolling: touch !important;
    touch-action: pan-y !important;
    overflow-x: hidden !important;
}

/* تحسين التمرير للحاويات الرئيسية */
.container,
.container-fluid,
main,
.main-content {
    -webkit-overflow-scrolling: touch !important;
    touch-action: pan-y !important;
}

/* تحسين التمرير للمحتوى */
.content,
.page-content,
.product-grid,
.products-container {
    -webkit-overflow-scrolling: touch !important;
    touch-action: pan-y !important;
}

/* منع التمرير الأفقي غير المرغوب فيه */
* {
    -webkit-overflow-scrolling: touch;
}

/* تحسين التمرير في الشاشات الصغيرة */
@media (max-width: 767.98px) {
    html, body {
        -webkit-overflow-scrolling: touch !important;
        touch-action: pan-y !important;
        overflow-x: hidden !important;
        position: relative !important;
    }
    
    /* تحسين التمرير للعناصر التفاعلية */
    .btn,
    .card,
    .product-item,
    .filter-item {
        touch-action: manipulation !important;
    }
    
    /* تحسين التمرير للقوائم */
    .list-group,
    .nav,
    .navbar {
        -webkit-overflow-scrolling: touch !important;
        touch-action: pan-y !important;
    }
    
    /* منع zoom غير المرغوب فيه */
    input,
    select,
    textarea {
        touch-action: manipulation !important;
    }
    
    /* تحسين التمرير للنوافذ المنبثقة */
    .modal,
    .modal-body {
        -webkit-overflow-scrolling: touch !important;
        touch-action: pan-y !important;
    }
}

/* إصلاح مشاكل التمرير مع النوافذ الجانبية */
body.sidebar-open {
    overflow: hidden !important;
    position: fixed !important;
    width: 100% !important;
}

/* تحسين التمرير داخل النافذة الجانبية */
.mobile-sidebar-panel {
    -webkit-overflow-scrolling: touch !important;
    touch-action: pan-y !important;
}

.mobile-sidebar-content {
    -webkit-overflow-scrolling: touch !important;
    touch-action: pan-y !important;
}

/* تحسين الأداء */
.mobile-sidebar-overlay {
    -webkit-transform: translateZ(0) !important;
    transform: translateZ(0) !important;
}

.mobile-sidebar-panel {
    -webkit-transform: translateZ(0) !important;
    transform: translateZ(0) !important;
    will-change: transform !important;
}
