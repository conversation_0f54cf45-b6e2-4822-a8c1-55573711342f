/* أنماط صفحة المفضلة والمقارنة */

/* أنماط عامة للصفحات */
.wishlist-page-header,
.compare-page-header {
    margin-bottom: 2rem;
}

.wishlist-title,
.compare-title {
    font-size: 1.8rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.wishlist-subtitle,
.compare-subtitle {
    color: var(--text-muted);
    font-size: 1rem;
}

/* أنماط بطاقات المنتجات في المفضلة */
.wishlist-products {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

/* أنماط أزرار المفضلة */
.wishlist-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: white;
    color: #ccc;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    cursor: pointer;
}

.wishlist-btn:hover,
.wishlist-btn.active {
    background-color: #6a0dad;
    color: white;
}

.wishlist-btn.active {
    color: #6a0dad;
}

.wishlist-btn.active:hover {
    background-color: #f8f9fa;
    color: #6a0dad;
}

/* أنماط جدول المقارنة */
.compare-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1.5rem;
    background-color: white;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.compare-table th,
.compare-table td {
    padding: 1rem;
    text-align: center;
    border: 1px solid #eee;
}

.compare-table th {
    background-color: #f8f9fa;
    font-weight: 700;
    color: var(--dark-color);
}

.compare-table tr:nth-child(even) {
    background-color: #f8f9fa;
}

.compare-product-cell {
    position: relative;
    min-width: 200px;
}

.compare-remove-btn {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: #f8f9fa;
    color: #999;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.compare-remove-btn:hover {
    background-color: #6a0dad;
    color: white;
}

.compare-product-img {
    width: 120px;
    height: 120px;
    object-fit: cover;
    margin: 0 auto 1rem;
    display: block;
}

.compare-product-title {
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark-color);
}

.compare-product-price {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.compare-product-actions {
    display: flex;
    justify-content: center;
    gap: 0.5rem;
}

/* Estilos para el contador de productos */
.compare-products-count,
.wishlist-products-count {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--dark-color);
}

/* Estilos para mensajes de alerta */
.alert {
    border-radius: 0;
    border: none;
    padding: 1rem;
}

/* Animaciones */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* Estilos responsivos */
@media (max-width: 768px) {
    .wishlist-products {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }

    .compare-table {
        display: block;
        overflow-x: auto;
    }

    .compare-product-cell {
        min-width: 150px;
    }
}

/* Estilos para el botón de añadir a la lista de deseos */
.add-to-wishlist-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: #d1ced7  !important;
    color: #999;
    border: 1px solid #d1ced7 ;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    font-size: 16px;
}

.add-to-wishlist-btn:hover {
    background-color: white !important;
    color: #dc4cdc;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    border: 1px solid #d0d0d0;
}

/* المنتجات المفضلة - دائرة بيضاء وقلب بنفسجي */
.add-to-wishlist-btn.active {
    background-color: white !important;
    border: 1px solid #e0e0e0 !important;
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(106, 13, 173, 0.15);
}

.add-to-wishlist-btn.active:hover {
    background-color: white !important;
    border: 1px solid #d0d0d0 !important;
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(106, 13, 173, 0.25);
}

/* تأكيد لون القلب البنفسجي في الحالة النشطة */
.add-to-wishlist-btn.active i,
.wishlist-active i {
    color: #6a0dad !important;
}

/* إزالة أي خلفية بنفسجية من الدائرة */
.add-to-wishlist-btn.active,
.wishlist-active {
    background-color: white !important;
    border: 1px solid #e0e0e0 !important;
}

/* قاعدة أكثر تحديداً لضمان الأولوية */
button.add-to-wishlist-btn.active {
    background-color: white !important;
    border: 1px solid #e0e0e0 !important;
    color: #6a0dad !important;
}

button.add-to-wishlist-btn.active i {
    color: #6a0dad !important;
}

/* قاعدة نهائية للتأكد من التطبيق الصحيح */
.product-card .add-to-wishlist-btn.active,
.product-item .add-to-wishlist-btn.active {
    background-color: white !important;
    border: 1px solid #e0e0e0 !important;
}

.product-card .add-to-wishlist-btn.active i,
.product-item .add-to-wishlist-btn.active i {
    color: #6a0dad !important;
}

/* التنسيق النهائي للمفضلة - دائرة بيضاء وقلب بنفسجي */
.add-to-wishlist-btn.active {
    background: white !important;
    border: 1px solid #e0e0e0 !important;
}

.add-to-wishlist-btn.active i {
    color: #6a0dad !important;
}

/* تأثير نبضة للمنتجات المفضلة */
.add-to-wishlist-btn.active.pulse {
    animation: heartbeat 0.6s ease-in-out;
}

@keyframes heartbeat {
    0% {
        transform: scale(1.1);
    }
    25% {
        transform: scale(1.3);
    }
    50% {
        transform: scale(1.1);
    }
    75% {
        transform: scale(1.25);
    }
    100% {
        transform: scale(1.1);
    }
}

/* Estilos para el botón de añadir a la comparación */
.add-to-compare-btn {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: white;
    color: #333;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.add-to-compare-btn:hover,
.add-to-compare-btn.active {
    background-color: #6a0dad;
    color: white;
    transform: translateY(-3px);
}

.add-to-compare-btn.active {
    background-color: #6a0dad !important;
    color: white !important;
}

/* Estilos para los contadores */
.wishlist-count,
.compare-count {
    font-size: 0.7rem;
    background-color: #6a0dad;
    color: white;
    position: absolute;
    top: -5px;
    right: -5px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    z-index: 5;
}

/* Animación de pulso para los contadores */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 0.5s ease-in-out;
}

/* أنماط القلب - بسيط وواضح */
/* الحالة العادية: قلب فارغ رمادي */
.add-to-wishlist-btn i.bi-heart-fill {
    display: none !important;
}

.add-to-wishlist-btn i.bi-heart {
    display: inline-block !important;
    color: #666 !important;
    font-size: 18px;
}

/* الحالة النشطة: قلب مملوء بنفسجي */
.add-to-wishlist-btn.active i.bi-heart {
    display: none !important;
}

.add-to-wishlist-btn.active i.bi-heart-fill {
    display: inline-block !important;
    color: #6a0dad !important;
    font-size: 18px;
}

/* تأكيد أن أزرار المفضلة في product-item-actions تتبع تنسيق المجموعة */
.product-item-actions .add-to-wishlist-btn {
    position: static !important;
    width: 28px !important;
    height: 28px !important;
    margin: 0 !important;
    flex-shrink: 0 !important;
}

.product-item-actions .add-to-wishlist-btn i {
    font-size: 14px !important;
}



@keyframes glow {
    from {
        opacity: 0.2;
        transform: scale(1);
    }
    to {
        opacity: 0.4;
        transform: scale(1.05);
    }
}

/* أنماط صفحة المقارنة */
.compare-container {
    padding: 2rem 0;
}

.compare-header {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.products-count {
    font-weight: 600;
    color: var(--primary-color);
}

.products-count span {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.9rem;
    margin-right: 0.5rem;
}

.compare-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* نصائح المقارنة */
.compare-tips {
    margin-bottom: 1.5rem;
}

.compare-tips .alert {
    border: none;
    border-radius: 10px;
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    color: #1565c0;
    border-right: 4px solid #2196f3;
    font-size: 0.9rem;
    padding: 1rem 1.5rem;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.1);
}

.compare-tips .alert i {
    font-size: 1.1rem;
    color: #2196f3;
}

/* جدول المقارنة المحسن */
.compare-table-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
    animation: fadeInUp 0.6s ease-out;
}

.compare-table-container:hover {
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.compare-table-wrapper {
    overflow-x: auto;
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) #f1f1f1;
}

.compare-table-wrapper::-webkit-scrollbar {
    height: 8px;
}

.compare-table-wrapper::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.compare-table-wrapper::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

.compare-table {
    width: 100%;
    border-collapse: collapse;
    min-width: 800px;
}

.compare-table th,
.compare-table td {
    padding: 1rem;
    text-align: center;
    vertical-align: text-top;
    border-bottom: 1px solid #e9ecef;
}

.property-header {
    background: linear-gradient(360deg, rgb(248, 249, 250), rgb(174, 122, 188));
    color: white;
    font-weight: 700;
    font-size: 1.1rem;
    position: sticky;
    right: 0;
    z-index: 10;
    min-width: 150px;
    text-align: right;
    padding-right: 1.5rem;
}

.product-header {
    background: #f8f9fa;
    border-bottom: 2px solid var(--primary-color);
    position: relative;
    min-width: 250px;
}

.product-header-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.product-image-wrapper {
    position: relative;
    width: 120px;
    height: 120px;
}

.product-header-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.product-header-img:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.remove-product-btn {
    position: absolute;
    top: -8px;
    left: -8px;
    width: 28px;
    height: 28px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    z-index: 5;
}

.remove-product-btn:hover {
    background: #c82333;
    transform: scale(1.1);
}

.remove-product-btn:active {
    transform: scale(0.95);
}

.product-header-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--dark-color);
    margin: 0;
    text-align: center;
    line-height: 1.3;
    max-width: 200px;
}

.compare-row {
    transition: background-color 0.3s ease;
}

.compare-row:hover {
    background-color: rgba(106, 13, 173, 0.02);
}

.property-label {
    background: #f8f9fa;
    font-weight: 600;
    color: var(--dark-color);
    text-align: right;
    padding-right: 1.5rem;
    position: sticky;
    right: 0;
    z-index: 5;
    border-left: 3px solid var(--primary-color);
}

.property-value {
    padding: 1.2rem 1rem;
}

/* أنماط السعر */
.price-row {
    background: rgba(106, 13, 173, 0.03);
}

.price-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
}

.current-price {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--primary-color);
}

.original-price {
    font-size: 1rem;
    color: var(--text-muted);
    text-decoration: line-through;
}

.discount-badge {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(231, 76, 60, 0.3);
}

/* أنماط الشارات */
.category-badge,
.type-badge {
    background: ghostwhite;
    color: #061a66;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    display: inline-block;
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    display: inline-block;
}

.status-badge.available {
    background: linear-gradient(45deg, #27ae60, #2ecc71);
    color: white;
}

.status-badge.unavailable {
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    color: white;
}

/* صف الوصف */
.description-row .property-value {
    text-align: right;
    padding: 1.5rem 1rem;
}

.description-text {
    line-height: 1.6;
    color: var(--text-color);
    max-height: 100px;
    overflow-y: auto;
    padding: 0.5rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-right: 3px solid var(--primary-color);
}

/* صف الإجراءات */
.actions-row {
    background: #f8f9fa;
}

.action-buttons {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.action-buttons .btn {
    min-width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.action-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* أنماط الشاشات الصغيرة */
@media (max-width: 768px) {
    .compare-container {
        padding: 1rem 0;
    }

    .compare-header {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .compare-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .compare-actions .btn {
        width: 100%;
        justify-content: center;
    }

    .compare-table {
        min-width: 600px;
    }

    .compare-table th,
    .compare-table td {
        padding: 0.75rem 0.5rem;
    }

    .property-header {
        min-width: 120px;
        font-size: 1rem;
        padding-right: 1rem;
    }

    .product-header {
        min-width: 200px;
    }

    .product-image-wrapper {
        width: 80px;
        height: 80px;
    }

    .product-header-title {
        font-size: 0.9rem;
        max-width: 150px;
    }

    .remove-product-btn {
        width: 24px;
        height: 24px;
        font-size: 0.8rem;
        top: -6px;
        left: -6px;
    }

    .current-price {
        font-size: 1.2rem;
    }

    .description-text {
        max-height: 80px;
        font-size: 0.9rem;
    }

    .action-buttons {
        gap: 0.25rem;
    }

    .action-buttons .btn {
        min-width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }
}

@media (max-width: 480px) {
    .compare-table {
        min-width: 500px;
    }

    .property-header {
        min-width: 100px;
        font-size: 0.9rem;
        padding: 0.75rem 0.5rem;
    }

    .product-header {
        min-width: 150px;
    }

    .product-image-wrapper {
        width: 60px;
        height: 60px;
    }

    .product-header-title {
        font-size: 0.8rem;
        max-width: 120px;
    }

    .current-price {
        font-size: 1.1rem;
    }

    .category-badge,
    .type-badge,
    .status-badge {
        font-size: 0.8rem;
        padding: 0.4rem 0.8rem;
    }

    .description-text {
        max-height: 60px;
        font-size: 0.8rem;
        padding: 0.4rem;
    }

    .action-buttons .btn {
        min-width: 32px;
        height: 32px;
        font-size: 0.8rem;
    }
}
