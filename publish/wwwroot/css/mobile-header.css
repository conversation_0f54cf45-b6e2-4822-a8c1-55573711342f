/* تنسيقات الهيدر في وضع الهاتف */

/* إخفاء الهيدر في وضع الهاتف على الشاشات الكبيرة */
.mobile-header {
    display: none;
}

/* إخفاء النافذة الجانبية على جميع الشاشات بشكل افتراضي */
.mobile-sidebar-overlay {
    display: none !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background-color: rgba(0, 0, 0, 0.5) !important;
    z-index: 9998 !important;
}

.mobile-sidebar-overlay.active {
    display: block !important;
}

.mobile-sidebar-panel {
    position: fixed !important;
    top: 0 !important;
    left: -350px !important;
    width: 320px !important;
    height: 100% !important;
    background-color: #fff !important;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.3) !important;
    transition: left 0.3s ease !important;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
    touch-action: pan-y !important;
    z-index: 9999 !important;
}

.mobile-sidebar-panel.active {
    left: 0 !important;
}

/* تنسيقات محتوى النافذة الجانبية */
.mobile-sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f8f9fa;
}

.mobile-sidebar-title-main {
    margin: 0;
    color: #6a0dad;
    font-weight: 600;
}

.mobile-sidebar-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #666;
    cursor: pointer;
    padding: 5px;
}

.mobile-sidebar-close:hover {
    color: #6a0dad;
}

.mobile-sidebar-content {
    padding: 0;
}

.mobile-sidebar-section {
    border-bottom: 1px solid #eee;
    padding: 15px 0;
}

.mobile-sidebar-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 10px;
    padding: 0 20px;
    color: #333;
}

.purple-title {
    color: #6a0dad !important;
}

.mobile-sidebar-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.mobile-sidebar-item {
    margin: 0;
}

.mobile-sidebar-link {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
}

.mobile-sidebar-link:hover {
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
}

.mobile-sidebar-link i {
    margin-left: 10px;
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

.logout-button {
    width: 100%;
    text-align: right;
    background: none;
    border: none;
    cursor: pointer;
}

/* تحسين التمرير باللمس للهواتف */
body {
    -webkit-overflow-scrolling: touch !important;
    touch-action: pan-y !important;
}

html, body {
    overflow-x: hidden !important;
    -webkit-overflow-scrolling: touch !important;
}

/* تنسيقات خاصة للشاشات الصغيرة */
@media (max-width: 767.98px) {
    /* تحسين التمرير في وضع الهاتف */
    body {
        -webkit-overflow-scrolling: touch !important;
        touch-action: pan-y !important;
        overflow-x: hidden !important;
    }

    /* إظهار الهيدر في وضع الهاتف */
    .mobile-header {
        display: block;
        background-color: #fff;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        position: sticky;
        top: 0;
        z-index: 1000;
        -webkit-overflow-scrolling: touch !important;
    }

    /* تنسيق حاوية الهيدر */
    .mobile-header-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 15px;
        height: 60px;
    }

    /* تنسيق الشعار */
    .mobile-header-logo {
        flex: 1;
        text-align: center;
    }

    .mobile-logo-img {
        height: 40px;
        width: auto;
    }

    /* تنسيق أزرار الهيدر */
    .mobile-header-cart,
    .mobile-header-wishlist,
    .mobile-header-menu {
        position: relative;
    }

    .mobile-header-cart .icon-link,
    .mobile-header-wishlist .icon-link,
    .mobile-menu-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        color: #333;
        text-decoration: none;
        background: none;
        border: none;
        cursor: pointer;
        font-size: 1.2rem;
    }

    .mobile-menu-button:hover,
    .mobile-header-cart .icon-link:hover,
    .mobile-header-wishlist .icon-link:hover {
        color: #6a0dad;
    }

    /* تنسيق العدادات */
    .mobile-badge0 {
        position: absolute;
        top: -5px;
        right: -5px;
        font-size: 0.7rem;
        min-width: 18px;
        height: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

/* عرض الهيدر في وضع الهاتف على الشاشات الصغيرة فقط */
@media (max-width: 767.98px) {
    /* إظهار الهيدر في وضع الهاتف */
    .mobile-header {
        display: block;
        background-color: #fff;
        padding: 10px 0;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        position: relative;
        z-index: 1000;
    }

    /* إخفاء الهيدر العادي */
    .top-header {
        display: none;
    }

    /* تنسيق حاوية الهيدر في وضع الهاتف */
    .mobile-header-container {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    /* تنسيق زر السلة */
    .mobile-header-cart {
        order: 1;
    }

    /* تنسيق الشعار */
    .mobile-header-logo {
        order: 2;
        text-align: center;
    }

    /* تنسيق زر القائمة */
    .mobile-header-menu {
        order: 3;
    }

    /* تنسيق صورة الشعار */
    .mobile-logo-img {
        height: 80px;
    }

    /* تنسيق زر القائمة */
    .mobile-menu-button {
        background: none;
        border: none;
        font-size: 1.5rem;
        color: #333;
        cursor: pointer;
        padding: 0;
    }

    /* تنسيق رأس النافذة الجانبية */
    .mobile-sidebar-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        border-bottom: 1px solid #eee;
        position: relative;
    }

    /* تنسيق العنوان الرئيسي للنافذة الجانبية */
    .mobile-sidebar-title-main {
        position: absolute;
        right: 0;
        left: 0;
        text-align: center;
        margin: 0;
        font-size: 1.2rem;
        font-weight: 600;
    }

    /* تنسيق زر إغلاق النافذة الجانبية */
    .mobile-sidebar-close {
        background: none;
        border: none;
        font-size: 1.2rem;
        color: #333;
        cursor: pointer;
        padding: 0;
        z-index: 1;
        position: relative;
    }

    /* تنسيق محتوى النافذة الجانبية */
    .mobile-sidebar-content {
        padding: 0;
        -webkit-overflow-scrolling: touch !important;
        touch-action: pan-y !important;
        overflow-y: auto !important;
    }

    /* تنسيق قسم النافذة الجانبية */
    .mobile-sidebar-section {
        margin-bottom: 20px;
    }

    /* تنسيق عنوان القسم */
    .mobile-sidebar-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 10px;
        padding: 10px 15px;
        color: #6a0dad;
    }

    /* تنسيق قائمة الروابط */
    .mobile-sidebar-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    /* تنسيق عنصر القائمة */
    .mobile-sidebar-item {
        margin-bottom: 0;
    }

    /* تنسيق الرابط */
    .mobile-sidebar-link {
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: #333;
        text-decoration: none;
        padding: 15px;
        transition: all 0.3s ease;
        font-size: 1rem;
        border-bottom: 1px solid #f0f0f0;
    }

    /* تنسيق الجزء الأيمن من الرابط (الأيقونة والنص) */
    .mobile-sidebar-link i {
        margin-left: 15px;
        font-size: 1.3rem;
        min-width: 24px;
        text-align: center;
    }

    /* تنسيق النص في الرابط */
    .mobile-sidebar-link span {
        flex: 1;
    }

    /* تنسيق الرابط عند التحويم */
    .mobile-sidebar-link:hover {
        color: #6a0dad;
        background-color: rgba(106, 13, 173, 0.05);
    }

    /* تنسيق الرابط النشط */
    .mobile-sidebar-link.active {
        color: #6a0dad;
        font-weight: 600;
        background-color: rgba(106, 13, 173, 0.1);
    }

    /* تنسيق خاص للروابط الرئيسية */
    .main-links .mobile-sidebar-link,
    .products-categories-list .mobile-sidebar-link {
        padding: 18px 15px;
    }

    /* تنسيق زر تسجيل الخروج */
    .logout-button {
        width: 100%;
        text-align: right;
        background: none;
        border: none;
        cursor: pointer;
        display: flex;
        align-items: center;
    }
}

/* عدادات الهاتف */
.mobile-badge0 {
    position: absolute !important;
    top: -5px !important;
    right: 12px !important;
    background-color: #e83e8c !important;
    color: white !important;
    width: 18px !important;
    height: 18px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 0.65rem !important;
    font-weight: 600 !important;
    z-index: 10 !important;
    min-width: 18px !important;
    text-align: center !important;
}
.mobile-badge {
    position: absolute !important;
    top: -5px !important;
    right: 47px !important;
    background-color: #e83e8c !important;
    color: white !important;
    width: 18px !important;
    height: 18px !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 0.65rem !important;
    font-weight: 600 !important;
    z-index: 10 !important;
    min-width: 18px !important;
    text-align: center !important;
}
/* تأثيرات حركية للعدادات في الهاتف */
.mobile-badge.bounce {
    animation: mobileBounce 0.6s ease-in-out;
}

.mobile-badge.pulse {
    animation: mobilePulse 1s ease-in-out;
}

@keyframes mobileBounce {
    0%, 20%, 60%, 100% {
        transform: translateY(0) scale(1);
    }
    40% {
        transform: translateY(-8px) scale(1.1);
    }
    80% {
        transform: translateY(-4px) scale(1.05);
    }
}

@keyframes mobilePulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.3);
    }
    100% {
        transform: scale(1);
    }
}
