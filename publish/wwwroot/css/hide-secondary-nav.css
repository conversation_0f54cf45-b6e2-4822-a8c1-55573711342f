/* إخفاء الشريط السفلي */
.secondary-navigation {
    display: none !important;
}

/* إخفاء أي شريط تنقل ثانوي */
.navbar + .navbar,
.main-navigation + nav,
.navbar-light + .navbar-light {
    display: none !important;
}

/* إخفاء أي عنصر يحتوي على روابط الفئات في الشريط السفلي */
.categories-nav-secondary,
.bottom-categories-nav,
.secondary-categories-bar {
    display: none !important;
}

/* إخفاء الشريط البنفسجي الذي يحتوي على روابط الفئات */
.purple-bar,
.categories-bar,
.category-nav,
.nav-categories,
div[class*="category"] + .navbar,
.navbar-light.bg-light,
.navbar.bg-primary,
.navbar.bg-purple,
.navbar-expand-lg + .navbar-expand-lg,
header + nav,
header + div > nav {
    display: none !important;
}

/* إخفاء أي عنصر يحتوي على "جميع المنتجات" */
.navbar:not(.main-navigation),
nav:not(.main-navigation),
.nav:not(.main-nav) {
    display: none !important;
}

/* إخفاء الشريط الثاني الذي يظهر في الصورة */
.main-navigation + *,
header + .navbar,
.navbar ~ .navbar,
.navbar + .navbar,
nav + nav,
.navbar-expand-lg:not(.main-navigation) {
    display: none !important;
}

/* إخفاء الشريط البنفسجي بشكل محدد */
.navbar.navbar-expand-lg.navbar-light.bg-purple,
.navbar.navbar-expand-lg.navbar-light:not(.main-navigation) {
    display: none !important;
}
