/* أنماط المفضلة منفصلة عن السلة */

/* إزالة جميع الأنماط المتضاربة للمفضلة */
.wishlist-btn {
    display: none !important; /* إخفاء الكلاس القديم */
}

/* أنماط زر المفضلة الوحيد */
.add-to-wishlist-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.95) !important;
    color: #666 !important;
    border: 2px solid rgba(255, 255, 255, 0.8) !important;
    display: flex !important;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    font-size: 18px;
    backdrop-filter: blur(10px);
}

/* حالة التمرير */
.add-to-wishlist-btn:hover {
    background-color: rgba(255, 255, 255, 0.98) !important;
    color: #6a0dad !important;
    border-color: #6a0dad !important;
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(106, 13, 173, 0.2);
}

/* حالة النشاط - منتج في المفضلة */
.add-to-wishlist-btn.active {
    background-color: rgba(255, 255, 255, 0.95) !important;
    color: #6a0dad !important;
    border: 2px solid rgba(106, 13, 173, 0.3) !important;
    box-shadow: 0 3px 12px rgba(106, 13, 173, 0.25);
}

.add-to-wishlist-btn.active:hover {
    background-color: rgba(255, 255, 255, 0.98) !important;
    color: #6a0dad !important;
    border-color: #6a0dad !important;
    transform: scale(1.05);
}

/* أنماط الأيقونة */
.add-to-wishlist-btn i {
    font-size: 20px !important;
    color: inherit !important;
    transition: all 0.3s ease;
}

/* إخفاء القلب المملوء بشكل افتراضي */
.add-to-wishlist-btn i.bi-heart-fill {
    display: none !important;
}

/* إظهار القلب الفارغ بشكل افتراضي */
.add-to-wishlist-btn i.bi-heart {
    display: inline-block !important;
    color: #666 !important;
}

/* عند التفعيل - إظهار القلب المملوء */
.add-to-wishlist-btn.active i.bi-heart {
    display: none !important;
}

.add-to-wishlist-btn.active i.bi-heart-fill {
    display: inline-block !important;
    color: #6a0dad !important;
    animation: heartPulse 0.6s ease-in-out;
}

/* تأثير نبضة للقلب عند التفعيل */
@keyframes heartPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

/* تأثيرات حركية */
.add-to-wishlist-btn.adding {
    transform: scale(0.9);
    opacity: 0.7;
}

.add-to-wishlist-btn.pulse {
    animation: wishlist-pulse 0.6s ease-in-out;
}

@keyframes wishlist-pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

/* أنماط عداد المفضلة */
.wishlist-count {
    font-size: 0.7rem;
    background-color: #6a0dad !important;
    color: white !important;
    position: absolute;
    top: -5px;
    right: -5px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    z-index: 5;
}

/* تأثير نبضة للعداد */
.wishlist-count.pulse {
    animation: wishlist-count-pulse 0.5s ease-in-out;
}

@keyframes wishlist-count-pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.3);
    }
    100% {
        transform: scale(1);
    }
}

/* أنماط صفحة المفضلة */
.wishlist-products {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 2rem;
}

.wishlist-product-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    transition: transform 0.3s ease;
}

.wishlist-product-card:hover {
    transform: translateY(-5px);
}

.wishlist-product-img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.wishlist-product-content {
    padding: 1rem;
}

.wishlist-product-name {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.wishlist-product-price {
    font-size: 1.2rem;
    font-weight: 700;
    color: #6a0dad;
    margin-bottom: 1rem;
}

/* أنماط الأزرار في صفحة المفضلة */
.wishlist-product-actions {
    display: flex;
    gap: 0.5rem;
}

.wishlist-remove-btn {
    background-color: #dc3545;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.wishlist-remove-btn:hover {
    background-color: #c82333;
}

.wishlist-add-to-cart-btn {
    background-color: #6a0dad;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    flex: 1;
}

.wishlist-add-to-cart-btn:hover {
    background-color: #5a0b9a;
}

/* أنماط متجاوبة */
@media (max-width: 768px) {
    .wishlist-products {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 1rem;
    }

    .add-to-wishlist-btn {
        width: 36px;
        height: 36px;
        font-size: 16px;
        top: 8px;
        right: 8px;
    }

    .add-to-wishlist-btn i {
        font-size: 18px !important;
    }
}

@media (max-width: 480px) {
    .wishlist-products {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }

    .add-to-wishlist-btn {
        width: 32px;
        height: 32px;
        font-size: 14px;
        top: 6px;
        right: 6px;
    }

    .add-to-wishlist-btn i {
        font-size: 16px !important;
    }
}

/* إزالة أي تداخل مع أنماط السلة */
.add-to-wishlist-btn.add-to-cart-btn {
    display: none !important; /* منع التداخل */
}

/* التأكد من عدم تأثر أزرار السلة */
.add-to-cart-btn.add-to-wishlist-btn {
    display: none !important; /* منع التداخل */
}

/* إصلاح مشكلة القلوب المكررة - إخفاء أي قلوب إضافية */
.add-to-wishlist-btn i:not(:first-child) {
    display: none !important;
}

/* ضمان ظهور قلب واحد فقط */
.add-to-wishlist-btn {
    overflow: hidden;
}

.add-to-wishlist-btn i + i {
    display: none !important;
}

/* أنماط خاصة للمنتجات المفضلة */
.product-card.favorited {
    border: 2px solid #6a0dad;
}

.product-card.favorited .add-to-wishlist-btn {
    background-color: white !important;
    color: #6a0dad !important;
    border-color: #6a0dad !important;
}

/* ضمان ظهور زر المفضلة على جميع أنواع المنتجات */
.product-item .add-to-wishlist-btn,
.product-card .add-to-wishlist-btn,
.best-seller-item .add-to-wishlist-btn {
    position: absolute !important;
    top: 10px !important;
    right: 10px !important;
    z-index: 25 !important;
    width: 40px !important;
    height: 40px !important;
    border-radius: 50% !important;
    background-color: rgba(255, 255, 255, 0.95) !important;
    border: 2px solid rgba(255, 255, 255, 0.8) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15) !important;
    transition: all 0.3s ease !important;
    backdrop-filter: blur(10px) !important;
}

/* ضمان أن زر المفضلة يظهر فوق جميع العناصر الأخرى */
.product-item-img-container .add-to-wishlist-btn {
    position: absolute !important;
    top: 10px !important;
    right: 10px !important;
    z-index: 30 !important;
}

/* تأكيد أن الأيقونة تظهر بشكل صحيح */
.product-item .add-to-wishlist-btn i,
.product-card .add-to-wishlist-btn i,
.best-seller-item .add-to-wishlist-btn i {
    font-size: 20px !important;
    color: #666 !important;
    transition: all 0.3s ease !important;
}

/* رسائل التوست الخاصة بالمفضلة */
#wishlist-toast-notification {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: rgba(106, 13, 173, 0.9);
    color: white;
    padding: 12px 20px;
    border-radius: 4px;
    z-index: 1000;
    transition: opacity 0.5s ease-in-out;
    opacity: 0;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    font-size: 14px;
    max-width: 300px;
    text-align: center;
}

/* تأثيرات إضافية للمفضلة */
.add-to-wishlist-btn.wishlist-active {
    animation: wishlist-added 0.6s ease-in-out;
}

@keyframes wishlist-added {
    0% {
        transform: scale(1);
    }
    25% {
        transform: scale(1.2);
        background-color: #6a0dad !important;
        color: white !important;
    }
    50% {
        transform: scale(1.1);
    }
    75% {
        transform: scale(1.15);
    }
    100% {
        transform: scale(1);
    }
}
