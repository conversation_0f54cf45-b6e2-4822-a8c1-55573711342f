/* Shopping Cart Styling - Completamente rediseñado */
.cart-item-img {
  width: 90px;
  height: 90px;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  background-color: #f9f9f9;
  padding: 5px;
}

.cart-item-img:hover {
  transform: scale(1.08);
  box-shadow: 0 6px 15px rgba(142, 36, 170, 0.15);
}

.order-item-img {
  width: 70px;
  height: 70px;
  object-fit: contain;
  border-radius: 8px;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.08);
  background-color: #f9f9f9;
  padding: 5px;
}

.quantity-control {
  max-width: 150px;
  display: flex;
  align-items: center;
  border-radius: 30px;
  overflow: hidden;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(142, 36, 170, 0.1);
}

.quantity-input {
  text-align: center;
  border: none;
  height: 36px;
  width: 50px;
  font-weight: 600;
  background-color: #fff;
  color: var(--primary-color);
}

.quantity-btn {
  width: 36px;
  height: 36px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  background-color: rgba(142, 36, 170, 0.05);
  color: var(--primary-color);
  transition: all 0.3s ease;
}

.quantity-btn:hover {
  background-color: var(--primary-color);
  color: white;
}

.cart-count {
  font-size: 0.7rem;
  padding: 0.25rem 0.5rem;
  min-width: 20px;
  height: 20px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: 3px;
  background-color: var(--primary-color);
  color: white;
  border-radius: 50%;
  box-shadow: 0 3px 6px rgba(142, 36, 170, 0.2);
}

.cart-count:empty {
  display: none;
}

/* Cart Table Styling - Completamente rediseñado */
.table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
}

.table thead th {
  background-color: rgba(142, 36, 170, 0.08);
  color: var(--primary-color);
  font-weight: 700;
  padding: 1.25rem 1rem;
  border-bottom: 1px solid rgba(142, 36, 170, 0.05);
  font-size: 0.9rem;
  letter-spacing: 0.5px;
}

.table tbody td {
  padding: 1.25rem 1rem;
  vertical-align: middle;
  border-bottom: 1px solid rgba(0, 0, 0, 0.03);
  font-size: 0.9rem;
}

.table tbody tr {
  transition: all 0.3s ease;
}

.table tbody tr:hover {
  background-color: rgba(142, 36, 170, 0.02);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

.table tfoot {
  background-color: rgba(142, 36, 170, 0.03);
}

.table tfoot td {
  padding: 1.25rem 1rem;
  font-weight: 700;
  color: var(--primary-color);
  font-size: 1rem;
}

/* Order Status Badges - Completamente rediseñado */
.badge {
  padding: 0.4rem 0.8rem;
  border-radius: 30px;
  font-weight: 600;
  font-size: 0.75rem;
  display: inline-block;
  letter-spacing: 0.3px;
}

.badge.bg-warning {
  background-color: rgba(255, 152, 0, 0.15) !important;
  color: #ff9800 !important;
}

.badge.bg-info {
  background-color: rgba(33, 150, 243, 0.15) !important;
  color: #2196f3 !important;
}

.badge.bg-primary {
  background-color: rgba(142, 36, 170, 0.15) !important;
  color: var(--primary-color) !important;
}

.badge.bg-success {
  background-color: rgba(106, 13, 173, 0.1) !important;
  color: #6a0dad !important;
}

.badge.bg-danger {
  background-color: rgba(244, 67, 54, 0.15) !important;
  color: #f44336 !important;
}

/* Order Details */
.order-info {
  padding: 2rem;
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
}

.order-info h4 {
  color: var(--primary-color);
  font-weight: 700;
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
}

.order-info hr {
  margin: 1.5rem 0;
  opacity: 0.1;
  border-color: var(--primary-color);
}

.order-info .row {
  margin-bottom: 1rem;
}

.order-info .col-title {
  font-weight: 600;
  color: var(--text-muted);
  font-size: 0.85rem;
}

.order-info .col-value {
  font-weight: 600;
  color: var(--dark-color);
  font-size: 0.9rem;
}
