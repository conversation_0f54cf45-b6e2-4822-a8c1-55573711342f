/* تنسيقات شريط التنقل العلوي القابل للاختفاء */

/* الشريط العلوي */
.sticky-nav {
    position: fixed;
    top: -80px; /* مخفي في البداية */
    left: 0;
    right: 0;
    background-color: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1050; /* قيمة أعلى من z-index للعناصر الأخرى */
    transition: top 0.3s ease;
    border-bottom: 1px solid #eee;
}

/* عند التمرير لأعلى */
.sticky-nav.visible {
    top: 0;
    animation: slideDown 0.3s ease;
}

/* تأثير حركي للظهور */
@keyframes slideDown {
    from {
        top: -80px;
    }
    to {
        top: 0;
    }
}

/* نسخة الشاشات الكبيرة */
.sticky-nav-desktop {
    height: 50px; /* تقليل ارتفاع الشريط */
}

/* حاوية الشريط للشاشات الكبيرة */
.sticky-nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 0 5px; /* إضافة مسافة داخلية صغيرة */
}

/* الشعار */
.sticky-nav-logo {
    flex: 0 0 auto;
}

.sticky-logo-img {
    height: 35px; /* تقليل حجم الشعار */
}

/* روابط التنقل */
.sticky-nav-links {
    display: flex;
    align-items: center;
    margin: 0 10px;
    flex: 1;
    justify-content: center;
}

.sticky-nav-link {
    color: #333;
    text-decoration: none;
    padding: 0 8px; /* تقليل المسافة بين الروابط */
    font-size: 14px;
    font-weight: 500;
    font-family: 'Tajawal', sans-serif;
    transition: color 0.3s ease;
}

.sticky-nav-link:hover {
    color: #6a0dad;
}

/* تنسيق الروابط النشطة */
.sticky-nav-link.active {
    color: #6a0dad;
    font-weight: 500;
    position: relative;
}

.sticky-nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -3px; /* تقليل المسافة أسفل الرابط */
    left: 25%;
    width: 50%;
    height: 2px;
    background-color: #6a0dad;
}

/* تنسيق الأيقونات النشطة */
.sticky-icon-link.active {
    color: #6a0dad;
    position: relative;
}

.sticky-icon-link.active::after {
    content: '';
    position: absolute;
    bottom: -3px; /* تقليل المسافة أسفل الأيقونة */
    left: 25%;
    width: 50%;
    height: 2px;
    background-color: #6a0dad;
}

/* أيقونات الهيدر */
.sticky-nav-icons {
    display: flex;
    align-items: center;
    gap: 10px; /* تقليل المسافة بين الأيقونات */
}

.sticky-icon-link {
    color: #333;
    font-size: 1.1rem; /* تقليل حجم الأيقونات */
    position: relative;
    text-decoration: none;
}

.sticky-icon-link:hover {
    color: #6a0dad;
}

/* العدادات */
.sticky-badge {
    position: absolute;
    top: -6px; /* تقليل المسافة العلوية */
    right: -6px; /* تقليل المسافة اليمنى */
    font-size: 0.6rem;
    padding: 0.12rem 0.25rem; /* تقليل المسافة الداخلية */
    min-width: 14px;
    height: 14px;
}

/* نسخة الهاتف المحمول */
.sticky-nav-mobile {
    height: 45px; /* تقليل ارتفاع الشريط في الهاتف */
}

/* حاوية الشريط للهاتف */
.sticky-mobile-nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 0 3px; /* تقليل المسافة الداخلية */
    direction: rtl; /* تأكيد اتجاه RTL */
}

/* زر القائمة (على اليمين) */
.sticky-mobile-menu-btn {
    font-size: 1.4rem; /* تقليل حجم الأيقونة */
    order: 3; /* ترتيب العناصر في flex */
}

.sticky-mobile-menu-btn a {
    color: #333;
    text-decoration: none;
}

/* الشعار في الهاتف (في الوسط) */
.sticky-mobile-logo {
    text-align: center;
    order: 2; /* ترتيب العناصر في flex */
}

.sticky-mobile-logo-img {
    height: 32px; /* تقليل حجم الشعار */
}

/* السلة في الهاتف (على اليسار) */
.sticky-mobile-cart {
    font-size: 1.2rem; /* تقليل حجم الأيقونة */
    position: relative;
    order: 1; /* ترتيب العناصر في flex */
}

.sticky-mobile-cart a {
    color: #333;
    text-decoration: none;
}

/* تنسيقات للشاشات المتوسطة */
@media (min-width: 768px) and (max-width: 991.98px) {
    .sticky-nav-link {
        padding: 0 10px;
        font-size: 12px;
    }
}
