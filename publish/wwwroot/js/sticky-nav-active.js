// تحديث الروابط النشطة في الشريط العلوي القابل للاختفاء

document.addEventListener('DOMContentLoaded', function() {
    // تحديث الروابط النشطة عند تحميل الصفحة
    updateStickyNavActiveLinks();
    
    // إضافة مستمع حدث للتمرير لتحديث الروابط النشطة عند ظهور الشريط العلوي
    window.addEventListener('scroll', function() {
        const stickyNav = document.querySelector('.sticky-nav');
        if (stickyNav && stickyNav.classList.contains('visible')) {
            updateStickyNavActiveLinks();
        }
    });
});

// وظيفة لتحديث الروابط النشطة في الشريط العلوي
function updateStickyNavActiveLinks() {
    // الحصول على المسار الحالي
    const currentPath = window.location.pathname;
    const currentSearch = window.location.search;
    
    // تحديد جميع روابط الشريط العلوي
    const stickyNavLinks = document.querySelectorAll('.sticky-nav-link');
    
    // إزالة الفئة النشطة من جميع الروابط
    stickyNavLinks.forEach(link => {
        link.classList.remove('active');
    });
    
    // تحديد الرابط النشط بناءً على المسار الحالي
    if (currentPath.includes('/Products') && currentSearch.includes('category=')) {
        // استخراج اسم الفئة من المسار
        const categoryParam = new URLSearchParams(currentSearch).get('category');
        
        // تحديد الرابط المطابق للفئة
        stickyNavLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && href.includes(`category=${categoryParam}`)) {
                link.classList.add('active');
            }
        });
    } else if (currentPath.includes('/Products') && !currentSearch.includes('category=')) {
        // إذا كنا في صفحة المنتجات بدون فئة محددة، فإن رابط "المتجر" هو النشط
        stickyNavLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && (href.endsWith('/Products') || href.endsWith('/Products/Index'))) {
                link.classList.add('active');
            }
        });
    } else if (currentPath.includes('/Categories/Products/') || currentPath.includes('/Categories/Details/')) {
        // إذا كنا في صفحة فئة محددة، فإن رابط الفئة هو النشط
        // هنا نحتاج إلى معرفة اسم الفئة من البيانات المعروضة في الصفحة
        const categoryName = document.querySelector('.category-name')?.textContent?.trim();
        if (categoryName) {
            stickyNavLinks.forEach(link => {
                if (link.textContent.trim() === categoryName) {
                    link.classList.add('active');
                }
            });
        }
    } else if (currentPath === '/' || currentPath === '/Home' || currentPath === '/Home/Index') {
        // إذا كنا في الصفحة الرئيسية، فإن رابط "الرئيسية" هو النشط
        stickyNavLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && (href === '/' || href.endsWith('/Home') || href.endsWith('/Home/Index'))) {
                link.classList.add('active');
            }
        });
    } else if (currentPath.includes('/Home/About')) {
        // إذا كنا في صفحة "عن المتجر"، فإن رابط "عن المتجر" هو النشط
        stickyNavLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && href.includes('/Home/About')) {
                link.classList.add('active');
            }
        });
    } else if (currentPath.includes('/Home/Contact')) {
        // إذا كنا في صفحة "اتصل بنا"، فإن رابط "اتصل بنا" هو النشط
        stickyNavLinks.forEach(link => {
            const href = link.getAttribute('href');
            if (href && href.includes('/Home/Contact')) {
                link.classList.add('active');
            }
        });
    } else if (currentPath.includes('/Cart')) {
        // إذا كنا في صفحة السلة، فإن أيقونة السلة هي النشطة
        const cartIcon = document.querySelector('.sticky-nav-icons .sticky-icon-link i.bi-cart3');
        if (cartIcon) {
            cartIcon.closest('.sticky-icon-link').classList.add('active');
        }
    } else if (currentPath.includes('/Wishlist')) {
        // إذا كنا في صفحة المفضلة، فإن أيقونة المفضلة هي النشطة
        const wishlistIcon = document.querySelector('.sticky-nav-icons .sticky-icon-link i.bi-heart');
        if (wishlistIcon) {
            wishlistIcon.closest('.sticky-icon-link').classList.add('active');
        }
    } else if (currentPath.includes('/Compare')) {
        // إذا كنا في صفحة المقارنة، فإن أيقونة المقارنة هي النشطة
        const compareIcon = document.querySelector('.sticky-nav-icons .sticky-icon-link i.bi-arrow-left-right');
        if (compareIcon) {
            compareIcon.closest('.sticky-icon-link').classList.add('active');
        }
    }
}
