// التحكم في النافذة الجانبية في وضع الهاتف

document.addEventListener('DOMContentLoaded', function() {
    // العناصر الرئيسية
    const mobileSidebarOverlay = document.getElementById('mobileSidebarOverlay');
    const mobileSidebarPanel = document.getElementById('mobileSidebarPanel');
    const mobileSidebarClose = document.getElementById('mobileSidebarClose');

    // إغلاق النافذة الجانبية عند النقر على زر الإغلاق
    if (mobileSidebarClose) {
        mobileSidebarClose.addEventListener('click', function() {
            closeMobileSidebar();
        });
    }

    // إغلاق النافذة الجانبية عند النقر خارجها
    if (mobileSidebarOverlay) {
        mobileSidebarOverlay.addEventListener('click', function(e) {
            if (e.target === mobileSidebarOverlay) {
                closeMobileSidebar();
            }
        });
    }

    // إغلاق النافذة الجانبية عند الضغط على زر ESC
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && mobileSidebarOverlay && mobileSidebarOverlay.style.display === 'block') {
            closeMobileSidebar();
        }
    });
});

// فتح النافذة الجانبية
function openMobileSidebar() {
    const mobileSidebarOverlay = document.getElementById('mobileSidebarOverlay');
    const mobileSidebarPanel = document.getElementById('mobileSidebarPanel');

    if (mobileSidebarOverlay && mobileSidebarPanel) {
        mobileSidebarOverlay.style.display = 'block';
        setTimeout(function() {
            mobileSidebarPanel.classList.add('active');
        }, 10);
        document.body.style.overflow = 'hidden';
    }
}

// إغلاق النافذة الجانبية
function closeMobileSidebar() {
    const mobileSidebarOverlay = document.getElementById('mobileSidebarOverlay');
    const mobileSidebarPanel = document.getElementById('mobileSidebarPanel');

    if (mobileSidebarOverlay && mobileSidebarPanel) {
        mobileSidebarPanel.classList.remove('active');
        setTimeout(function() {
            mobileSidebarOverlay.style.display = 'none';
        }, 300);
        document.body.style.overflow = '';
    }
}
