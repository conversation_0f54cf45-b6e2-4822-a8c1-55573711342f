// وظائف لأزرار المنتجات (إضافة للسلة، المفضلة، المقارنة)

// حماية من التحميل المتعدد
if (window.productActionsLoaded) {
    console.log('ملف product-actions.js محمل بالفعل، تم تجاهل التحميل المتكرر');
} else {
    window.productActionsLoaded = true;

    document.addEventListener('DOMContentLoaded', function() {
        console.log('تم تحميل ملف product-actions.js');

        // تعريف متغيرات عالمية لمنع التحديثات المتكررة
        window.isUpdatingWishlistCount = false;
        window.isUpdatingCompareCount = false;
        window.isUpdatingWishlistButtons = false;
        window.isUpdatingCompareButtons = false;

    // تهيئة أزرار المنتجات - تأخير التهيئة لضمان تحميل الصفحة بالكامل
    setTimeout(function() {
        try {
            initProductButtons();
        } catch (e) {
            console.error('خطأ في تهيئة أزرار المنتجات:', e);
        }
    }, 300);

    // تحديث عدادات السلة والمفضلة والمقارنة عند تحميل الصفحة مرة واحدة فقط
    try {
        // تأخير أكبر لضمان تحميل الصفحة بالكامل
        setTimeout(function() {
            try {
                updateCartCount();
            } catch (cartError) {
                console.error('خطأ في تحديث عداد السلة:', cartError);
            }

            try {
                updateWishlistCount();
            } catch (wishlistError) {
                console.error('خطأ في تحديث عداد المفضلة:', wishlistError);
            }

            try {
                updateCompareCount();
            } catch (compareError) {
                console.error('خطأ في تحديث عداد المقارنة:', compareError);
            }
        }, 800);
    } catch (e) {
        console.error('خطأ في تحديث العدادات:', e);
    }

    // إضافة مستمعات أحداث للتحديث المباشر للعدادات
    try {
        document.addEventListener('wishlistCountUpdated', function(e) {
            try {
                if (!e || !e.detail) return;

                const wishlistBadge = document.querySelector('.wishlist-count');
                if (wishlistBadge && e.detail && e.detail.count !== undefined) {
                    wishlistBadge.textContent = e.detail.count;
                }
            } catch (e) {
                console.error('خطأ في تحديث عداد المفضلة:', e);
            }
        });

        document.addEventListener('compareCountUpdated', function(e) {
            try {
                if (!e || !e.detail) return;

                const compareBadge = document.querySelector('.compare-count');
                if (compareBadge && e.detail && e.detail.count !== undefined) {
                    compareBadge.textContent = e.detail.count;
                }
            } catch (e) {
                console.error('خطأ في تحديث عداد المقارنة:', e);
            }
        });
    } catch (eventError) {
        console.error('خطأ في إضافة مستمعات الأحداث:', eventError);
    }
});

function initProductButtons() {
    try {
        console.log('بدء تهيئة أزرار المنتجات');

        // تهيئة أزرار إضافة للسلة
        try {
            initCartButtonsOnly();
        } catch (cartError) {
            console.error('خطأ في تهيئة أزرار السلة:', cartError);
        }

        // تهيئة أزرار المفضلة
        try {
            initWishlistButtons();
        } catch (wishlistError) {
            console.error('خطأ في تهيئة أزرار المفضلة:', wishlistError);
        }

        // تهيئة أزرار المقارنة
        try {
            initCompareButtons();
        } catch (compareError) {
            console.error('خطأ في تهيئة أزرار المقارنة:', compareError);
        }

        // منع انتقال الرابط عند النقر على الأزرار
        try {
            preventButtonLinkRedirect();
        } catch (redirectError) {
            console.error('خطأ في تهيئة منع انتقال الروابط:', redirectError);
        }

        console.log('تم الانتهاء من تهيئة أزرار المنتجات');
    } catch (mainError) {
        console.error('خطأ رئيسي في تهيئة أزرار المنتجات:', mainError);
    }
}

// منع انتقال الرابط عند النقر على الأزرار
function preventButtonLinkRedirect() {
    try {
        // استخدام try-catch للتعامل مع أي أخطاء في querySelectorAll
        let actionButtons = [];
        try {
            actionButtons = document.querySelectorAll('.product-item-action-btn') || [];
        } catch (queryError) {
            console.error('خطأ في استعلام أزرار الإجراءات:', queryError);
            return;
        }

        if (!actionButtons || actionButtons.length === 0) {
            console.log('لم يتم العثور على أزرار إجراءات');
            return;
        }

        // تحويل NodeList إلى مصفوفة وتصفية العناصر null أو undefined
        const validButtons = Array.from(actionButtons).filter(btn => btn !== null && btn !== undefined);

        if (validButtons.length === 0) {
            console.log('لم يتم العثور على أزرار إجراءات صالحة');
            return;
        }

        validButtons.forEach(button => {
            if (!button || typeof button.addEventListener !== 'function') {
                return;
            }

            button.addEventListener('click', function(e) {
                if (!e) return false;

                try {
                    e.preventDefault();
                    e.stopPropagation();

                    // إيقاف انتشار الحدث لمنع تفعيل الرابط الممتد
                    const event = e || window.event;
                    if (event.stopPropagation) {
                        event.stopPropagation();
                    }
                    if (event.cancelBubble !== null) {
                        event.cancelBubble = true;
                    }

                    // منع السلوك الافتراضي
                    if (event.preventDefault) {
                        event.preventDefault();
                    }
                } catch (eventError) {
                    console.error('خطأ في معالجة حدث النقر:', eventError);
                }

                return false;
            });
        });
    } catch (mainError) {
        console.error('خطأ رئيسي في تهيئة منع انتقال الروابط:', mainError);
    }
}

// تهيئة أزرار إضافة للسلة
function initCartButtons() {
    // حماية من التنفيذ المتعدد
    if (window.cartButtonsInitialized) {
        console.log('أزرار السلة مهيأة بالفعل، تم تجاهل التهيئة المتكررة');
        return;
    }

    try {
        console.log('بدء تهيئة أزرار السلة');
        window.cartButtonsInitialized = true;

        // استخدام try-catch للتعامل مع أي أخطاء في querySelectorAll
        let cartButtons = [];
        try {
            cartButtons = document.querySelectorAll('.add-to-cart-btn') || [];
        } catch (queryError) {
            console.error('خطأ في استعلام أزرار السلة:', queryError);
            window.cartButtonsInitialized = false; // إعادة تعيين في حالة الخطأ
            return;
        }

        if (!cartButtons || cartButtons.length === 0) {
            console.log('لم يتم العثور على أزرار سلة');
            return;
        }

        console.log(`تم العثور على ${cartButtons.length} زر سلة`);

        // تحويل NodeList إلى مصفوفة وتصفية العناصر null أو undefined
        const validButtons = Array.from(cartButtons).filter(btn => btn !== null && btn !== undefined);

        if (validButtons.length === 0) {
            console.log('لم يتم العثور على أزرار سلة صالحة');
            return;
        }

        validButtons.forEach(button => {
            if (!button || typeof button.addEventListener !== 'function' || typeof button.removeEventListener !== 'function') {
                console.log('زر سلة غير صالح');
                return;
            }

            try {
                // إزالة أي مستمعات أحداث سابقة
                button.removeEventListener('click', cartClickHandler);

                // إضافة مستمع الحدث الجديد
                button.addEventListener('click', cartClickHandler);
            } catch (eventError) {
                console.error('خطأ في إضافة مستمع الحدث لزر السلة:', eventError);
            }
        });

        console.log('تم الانتهاء من تهيئة أزرار السلة');
    } catch (mainError) {
        console.error('خطأ رئيسي في تهيئة أزرار السلة:', mainError);
    }
}

// معالج النقر على زر السلة
function cartClickHandler(e) {
    e.preventDefault();
    e.stopPropagation();

    // إيقاف انتشار الحدث لمنع تفعيل الرابط الممتد
    const event = e || window.event;
    if (event.stopPropagation) {
        event.stopPropagation();
    }
    if (event.cancelBubble !== null) {
        event.cancelBubble = true;
    }

    // التحقق من أن الزر ليس في حالة معالجة
    if (this.classList.contains('adding') || this.disabled) {
        console.log('الزر في حالة معالجة، تم تجاهل النقر');
        return false;
    }

    const productId = this.getAttribute('data-product-id');

    // إضافة تأثير حركي للزر وتعطيله مؤقتاً
    this.classList.add('adding');
    this.disabled = true;

    // طباعة معلومات تصحيح الأخطاء في وحدة التحكم
    console.log('تم النقر على زر إضافة للسلة للمنتج رقم:', productId);

    // إرسال طلب AJAX لإضافة المنتج للسلة
    addToCart(productId, this);

    // منع السلوك الافتراضي
    return false;
}

// إضافة منتج للسلة
function addToCart(productId, buttonElement) {
    // التحقق من أن العملية ليست قيد التنفيذ بالفعل
    if (buttonElement && (buttonElement.classList.contains('adding') || buttonElement.disabled)) {
        console.log('العملية قيد التنفيذ بالفعل، تم تجاهل الطلب');
        return;
    }

    // الحصول على رمز التحقق من CSRF
    const token = document.querySelector('input[name="__RequestVerificationToken"]')?.value;

    if (!token) {
        console.error('لم يتم العثور على رمز التحقق من CSRF');
        showToast('حدث خطأ أثناء إضافة المنتج للسلة');
        if (buttonElement) {
            buttonElement.classList.remove('adding');
            buttonElement.disabled = false;
        }
        return;
    }

    console.log('جاري إضافة المنتج للسلة:', productId);

    // إضافة timeout لإعادة تفعيل الزر في حالة عدم الاستجابة
    const timeoutId = setTimeout(() => {
        if (buttonElement) {
            buttonElement.classList.remove('adding');
            buttonElement.disabled = false;
            console.log('تم إعادة تفعيل الزر بسبب انتهاء المهلة الزمنية');
        }
    }, 10000); // 10 ثوان

    fetch('/Cart/AddToCart', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'RequestVerificationToken': token
        },
        body: `productId=${productId}&quantity=1&__RequestVerificationToken=${token}`
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        // إلغاء timeout
        clearTimeout(timeoutId);

        if (data.success) {
            showToast('تمت إضافة المنتج إلى السلة');

            // تحديث عداد السلة بشكل فوري
            updateCartCount();

            // إطلاق حدث تحديث السلة
            if (typeof $ !== 'undefined') {
                $(document).trigger('cart:updated');
            }

            // تحديث جميع أزرار السلة للمنتج نفسه
            updateAllCartButtonsForProduct(productId, true);

            console.log('تم تحديث السلة بنجاح');
        } else {
            console.error('خطأ في إضافة المنتج للسلة:', data);
            showToast('حدث خطأ أثناء إضافة المنتج للسلة');
        }

        // إزالة تأثير الإضافة من الزر وإعادة تفعيله
        if (buttonElement) {
            buttonElement.classList.remove('adding');
            buttonElement.disabled = false;
        }
    })
    .catch(error => {
        // إلغاء timeout
        clearTimeout(timeoutId);

        console.error('خطأ في طلب إضافة المنتج للسلة:', error);
        showToast('حدث خطأ أثناء إضافة المنتج للسلة');

        // إزالة تأثير الإضافة من الزر وإعادة تفعيله
        if (buttonElement) {
            buttonElement.classList.remove('adding');
            buttonElement.disabled = false;
        }
    });
}

// تحديث جميع أزرار السلة للمنتج نفسه
function updateAllCartButtonsForProduct(productId, isAdded) {
    const allCartButtons = document.querySelectorAll(`.add-to-cart-btn[data-product-id="${productId}"]`);
    allCartButtons.forEach(button => {
        if (isAdded) {
            button.classList.add('added');
            setTimeout(() => {
                button.classList.remove('added');
            }, 2000);
        }
    });
}

// تحديث عدد العناصر في السلة
function updateCartCount() {
    console.log('جاري تحديث عداد السلة');

    fetch('/Cart/GetCartItemsCount')
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        // البحث عن جميع عدادات السلة (للشاشات الكبيرة والصغيرة)
        const cartBadges = document.querySelectorAll('.cart-count');

        if (cartBadges && cartBadges.length > 0) {
            const newCount = data.count;

            console.log(`تم العثور على ${cartBadges.length} عداد سلة، العدد الجديد: ${newCount}`);

            cartBadges.forEach((cartBadge, index) => {
                if (cartBadge) {
                    // حفظ القيمة القديمة للمقارنة
                    const oldCount = parseInt(cartBadge.textContent) || 0;

                    // تحديث النص
                    cartBadge.textContent = newCount;

                    // إضافة تأثير حركي أكثر وضوحاً إذا تغيرت القيمة
                    if (newCount !== oldCount) {
                        try {
                            // إزالة أي تأثيرات سابقة
                            cartBadge.classList.remove('pulse', 'bounce', 'flash');

                            // إضافة تأثير جديد
                            cartBadge.classList.add('bounce');

                            // تغيير لون الخلفية مؤقتًا باستخدام كلاس
                            cartBadge.classList.add('badge-highlight');

                            // إعادة اللون الأصلي بعد فترة
                            setTimeout(() => {
                                if (cartBadge) {
                                    cartBadge.classList.remove('bounce');
                                    cartBadge.classList.remove('badge-highlight');
                                }
                            }, 1500);
                        } catch (e) {
                            console.error('خطأ في تطبيق التأثيرات على عداد السلة:', e);
                        }
                    }

                    console.log(`تم تحديث عداد السلة ${index + 1}: ${newCount}`);
                }
            });

            // طباعة معلومات تصحيح الأخطاء في وحدة التحكم
            console.log('تم تحديث جميع عدادات السلة:', newCount);
        } else {
            console.error('لم يتم العثور على أي عنصر عداد السلة (.cart-count)');
        }
    })
    .catch(error => {
        console.error('خطأ في تحديث عداد السلة:', error);
    });
}

// تهيئة أزرار المفضلة
function initWishlistButtons() {
    try {
        console.log('بدء تهيئة أزرار المفضلة');

        // التحقق من وجود أزرار المفضلة في الصفحة
        const wishlistButtons = document.querySelectorAll('.add-to-wishlist-btn');
        if (!wishlistButtons || wishlistButtons.length === 0) {
            console.log('لم يتم العثور على أزرار مفضلة');
            return;
        }

        console.log(`تم العثور على ${wishlistButtons.length} زر مفضلة`);

        // التحقق من وجود عداد المفضلة في الصفحة
        const wishlistBadge = document.querySelector('.wishlist-count');
        if (!wishlistBadge) {
            console.log('لم يتم العثور على عنصر عداد المفضلة (.wishlist-count)');
        }

        // تحديث حالة الأزرار من الخادم - استدعاء واحد فقط
        fetch('/Wishlist/GetWishlistItems')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data && data.items) {
                console.log(`تم استلام ${data.items.length} عنصر من المفضلة`);

                // تحديد الأزرار للمنتجات الموجودة في المفضلة
                wishlistButtons.forEach(button => {
                    if (!button) {
                        console.log('تم العثور على زر غير صالح');
                        return;
                    }

                    try {
                        const productIdAttr = button.getAttribute('data-product-id');
                        if (!productIdAttr) {
                            console.log('زر بدون معرف منتج:', button);
                            return;
                        }

                        const productId = parseInt(productIdAttr);
                        const isInWishlist = data.items.includes(productId);

                        if (isInWishlist) {
                            button.classList.add('active');
                            button.classList.add('wishlist-active');

                            // تغيير أيقونة القلب إلى مملوء
                            try {
                                const icon = button.querySelector('i');
                                if (icon) {
                                    // تحديث الأيقونة بناءً على نوعها
                                    if (icon.classList.contains('bi-heart')) {
                                        icon.classList.remove('bi-heart');
                                        icon.classList.add('bi-heart-fill');
                                    } else if (icon.classList.contains('fa-heart-o')) {
                                        icon.classList.remove('fa-heart-o');
                                        icon.classList.add('fa-heart');
                                    }
                                }
                            } catch (iconError) {
                                console.error('خطأ في تعديل أيقونة الزر:', iconError);
                            }
                        } else {
                            // التأكد من أن الزر في الحالة الافتراضية
                            button.classList.remove('active');
                            button.classList.remove('wishlist-active');

                            // التأكد من أن الأيقونة هي قلب فارغ
                            try {
                                const icon = button.querySelector('i');
                                if (icon) {
                                    // تحديث الأيقونة بناءً على نوعها
                                    if (icon.classList.contains('bi-heart-fill')) {
                                        icon.classList.remove('bi-heart-fill');
                                        icon.classList.add('bi-heart');
                                    } else if (icon.classList.contains('fa-heart')) {
                                        icon.classList.remove('fa-heart');
                                        icon.classList.add('fa-heart-o');
                                    }
                                }
                            } catch (iconError) {
                                console.error('خطأ في تعديل أيقونة الزر:', iconError);
                            }
                        }
                    } catch (e) {
                        console.error('خطأ في تهيئة زر المفضلة:', e);
                    }
                });

                // تحديث عداد المفضلة مرة واحدة فقط
                try {
                    if (wishlistBadge) {
                        wishlistBadge.textContent = data.items.length;

                        // إضافة تأثير بصري للعداد عند التحميل إذا كان هناك عناصر
                        if (data.items.length > 0) {
                            try {
                                wishlistBadge.classList.add('pulse');
                                setTimeout(() => {
                                    if (wishlistBadge) {
                                        wishlistBadge.classList.remove('pulse');
                                    }
                                }, 1000);
                            } catch (e) {
                                console.error('خطأ في تطبيق التأثيرات على عداد المفضلة:', e);
                            }
                        }
                    }
                } catch (badgeError) {
                    console.error('خطأ في تحديث عداد المفضلة:', badgeError);
                }
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل حالة أزرار المفضلة:', error);
        });

        // إضافة حدث النقر للأزرار
        wishlistButtons.forEach(button => {
            if (!button) {
                console.log('تم العثور على زر غير صالح عند إضافة مستمع الحدث');
                return;
            }

            try {
                // التحقق من وجود سمة data-product-id
                if (!button.hasAttribute('data-product-id')) {
                    console.log('زر بدون معرف منتج عند إضافة مستمع الحدث:', button);
                    return;
                }

                // إزالة أي مستمعات أحداث سابقة
                button.removeEventListener('click', wishlistClickHandler);

                // إضافة مستمع الحدث الجديد
                button.addEventListener('click', wishlistClickHandler);
            } catch (eventError) {
                console.error('خطأ في إضافة مستمع الحدث للزر:', eventError);
            }
        });

        console.log('تم الانتهاء من تهيئة أزرار المفضلة');
    } catch (mainError) {
        console.error('خطأ رئيسي في تهيئة أزرار المفضلة:', mainError);
    }
}

// معالج النقر على زر المفضلة
function wishlistClickHandler(e) {
    e.preventDefault();
    e.stopPropagation();

    // إيقاف انتشار الحدث لمنع تفعيل الرابط الممتد
    const event = e || window.event;
    if (event.stopPropagation) {
        event.stopPropagation();
    }
    if (event.cancelBubble !== null) {
        event.cancelBubble = true;
    }

    const productId = this.getAttribute('data-product-id');

    // إضافة تأثير حركي للزر
    this.classList.add('adding');

    // طباعة معلومات تصحيح الأخطاء في وحدة التحكم
    console.log('تم النقر على زر المفضلة للمنتج رقم:', productId);

    // إضافة أو إزالة المنتج من المفضلة
    toggleWishlistItem(productId, this);

    // منع السلوك الافتراضي
    return false;
}

// إضافة أو إزالة منتج من المفضلة
function toggleWishlistItem(productId, buttonElement) {
    // الحصول على رمز التحقق من CSRF
    const token = document.querySelector('input[name="__RequestVerificationToken"]')?.value;

    if (!token) {
        console.error('لم يتم العثور على رمز التحقق من CSRF');
        showToast('حدث خطأ أثناء تحديث المفضلة');
        if (buttonElement) {
            buttonElement.classList.remove('adding');
        }
        return;
    }

    console.log('جاري تحديث المفضلة للمنتج:', productId);

    // إضافة تأثير حركي للزر
    if (buttonElement) {
        buttonElement.classList.add('adding');

        // إضافة تأثير نبض للزر
        buttonElement.classList.add('pulse');
        setTimeout(() => {
            if (buttonElement) buttonElement.classList.remove('pulse');
        }, 500);
    }

    fetch('/Wishlist/AddToWishlist', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'RequestVerificationToken': token,
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: `productId=${productId}&__RequestVerificationToken=${token}`
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            // عرض رسالة نجاح مخصصة
            const message = data.isInWishlist ? 'تمت إضافة المنتج إلى المفضلة' : 'تمت إزالة المنتج من المفضلة';
            showToast(message);

            // تحديث حالة الزر
            if (buttonElement) {
                if (data.isInWishlist) {
                    buttonElement.classList.add('active');
                } else {
                    buttonElement.classList.remove('active');
                }
            }

            // تحديث عداد المفضلة فوراً
            updateWishlistCountImmediate();

            // إطلاق حدث تحديث المفضلة
            if (typeof $ !== 'undefined') {
                $(document).trigger('wishlist:updated', { count: data.count });
            }

            // إطلاق حدث مخصص لتحديث المفضلة
            const event = new CustomEvent('wishlistCountUpdated', { detail: { count: data.count } });
            document.dispatchEvent(event);

            // تحديث جميع أزرار المفضلة للمنتج نفسه
            updateAllWishlistButtonsForProduct(productId, data.isInWishlist);

            console.log('تم تحديث المفضلة بنجاح');
        } else {
            console.error('خطأ في تحديث المفضلة:', data);
            showToast('حدث خطأ أثناء تحديث المفضلة');
        }

        // إزالة تأثير الإضافة من الزر
        if (buttonElement) {
            setTimeout(() => {
                buttonElement.classList.remove('adding');
            }, 500);
        }
    })
    .catch(error => {
        console.error('خطأ في طلب تحديث المفضلة:', error);
        showToast('حدث خطأ أثناء تحديث المفضلة');

        // إزالة تأثير الإضافة من الزر
        if (buttonElement) {
            buttonElement.classList.remove('adding');
        }
    });
}

// تحديث جميع أزرار المفضلة للمنتج نفسه
function updateAllWishlistButtonsForProduct(productId, isActive) {
    console.log(`تحديث أزرار المفضلة للمنتج ${productId}، الحالة: ${isActive}`);

    try {
        // البحث عن جميع أزرار المفضلة للمنتج المحدد
        const allWishlistButtons = document.querySelectorAll(`button[data-product-id="${productId}"].add-to-wishlist-btn, button[data-product-id="${productId}"].wishlist-btn`);

        if (!allWishlistButtons || allWishlistButtons.length === 0) {
            console.log(`لم يتم العثور على أزرار للمنتج ${productId}`);
            return;
        }

        console.log(`تم العثور على ${allWishlistButtons.length} زر مفضلة للمنتج ${productId}`);

        allWishlistButtons.forEach(button => {
            if (!button) return;

            try {
                if (isActive) {
                    // إضافة للمفضلة
                    button.classList.add('active');

                    // تغيير لون الخلفية للزر باستخدام الكلاسات بدلاً من style
                    try {
                        // إضافة كلاس للتنسيق بدلاً من استخدام style مباشرة
                        button.classList.add('wishlist-active');

                        // إضافة تأثير نبض للزر
                        button.classList.add('pulse');
                        setTimeout(() => {
                            if (button) button.classList.remove('pulse');
                        }, 1000);
                    } catch (styleError) {
                        console.error('خطأ في تعديل نمط الزر:', styleError);
                    }

                    // إضافة أيقونة قلب مملوء
                    try {
                        const icon = button.querySelector('i');
                        if (icon) {
                            // تحديث الأيقونة بناءً على نوعها
                            if (icon.classList.contains('fa-heart-o')) {
                                icon.classList.remove('fa-heart-o');
                                icon.classList.add('fa-heart');
                            } else if (icon.classList.contains('bi-heart')) {
                                icon.classList.remove('bi-heart');
                                icon.classList.add('bi-heart-fill');
                            }
                        }
                    } catch (iconError) {
                        console.error('خطأ في تعديل أيقونة الزر:', iconError);
                    }

                    setTimeout(() => {
                        try {
                            if (button) button.classList.remove('added');
                        } catch (timeoutError) {
                            console.error('خطأ في إزالة تأثير الإضافة:', timeoutError);
                        }
                    }, 2000);
                } else {
                    // إزالة من المفضلة
                    button.classList.remove('active');

                    // إعادة اللون الأصلي باستخدام الكلاسات
                    try {
                        // إزالة كلاس التنسيق
                        button.classList.remove('wishlist-active');
                    } catch (styleError) {
                        console.error('خطأ في تعديل نمط الزر:', styleError);
                    }

                    // إعادة أيقونة القلب الفارغ
                    try {
                        const icon = button.querySelector('i');
                        if (icon) {
                            // تحديث الأيقونة بناءً على نوعها
                            if (icon.classList.contains('fa-heart')) {
                                icon.classList.remove('fa-heart');
                                icon.classList.add('fa-heart-o');
                            } else if (icon.classList.contains('bi-heart-fill')) {
                                icon.classList.remove('bi-heart-fill');
                                icon.classList.add('bi-heart');
                            }
                        }
                    } catch (iconError) {
                        console.error('خطأ في تعديل أيقونة الزر:', iconError);
                    }

                    // إضافة تأثير للإزالة
                    try {
                        button.classList.add('flash');
                        setTimeout(() => {
                            if (button) button.classList.remove('flash');
                        }, 1000);
                    } catch (removeError) {
                        console.error('خطأ في إضافة تأثير الإزالة:', removeError);
                    }
                }
            } catch (buttonError) {
                console.error('خطأ في تحديث زر المفضلة:', buttonError);
            }
        });
    } catch (mainError) {
        console.error('خطأ رئيسي في تحديث أزرار المفضلة:', mainError);
    }

    // لا نقوم بتحديث العداد هنا لتجنب التحديثات المتكررة
}

// تهيئة أزرار المقارنة
function initCompareButtons() {
    try {
        console.log('بدء تهيئة أزرار المقارنة');

        // التحقق من وجود أزرار المقارنة في الصفحة
        const compareButtons = document.querySelectorAll('.add-to-compare-btn');
        if (!compareButtons || compareButtons.length === 0) {
            console.log('لم يتم العثور على أزرار مقارنة');
            return;
        }

        // تحويل NodeList إلى مصفوفة وتصفية العناصر null أو undefined
        const validButtons = Array.from(compareButtons).filter(button => button !== null && button !== undefined);

        console.log(`تم العثور على ${validButtons.length} زر مقارنة صالح`);

        if (validButtons.length === 0) {
            console.log('لم يتم العثور على أزرار مقارنة صالحة');
            return;
        }

        // التحقق من وجود عداد المقارنة في الصفحة
        const compareBadge = document.querySelector('.compare-count');
        if (!compareBadge) {
            console.log('لم يتم العثور على عنصر عداد المقارنة (.compare-count)');
        }

        // تحديث حالة الأزرار من الخادم - استدعاء واحد فقط
        fetch('/Compare/GetCompareItems')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data && data.items) {
                console.log(`تم استلام ${data.items.length} عنصر من المقارنة`);

                // تحديد الأزرار للمنتجات الموجودة في المقارنة
                // استخدام المصفوفة المصفاة بدلاً من NodeList الأصلية
                validButtons.forEach(button => {
                    try {
                        if (!button) {
                            console.log('تم العثور على زر غير صالح');
                            return;
                        }

                        const productIdAttr = button.getAttribute('data-product-id');
                        if (!productIdAttr) {
                            console.log('زر بدون معرف منتج:', button);
                            return;
                        }

                        const productId = parseInt(productIdAttr);
                        const isInCompareList = data.items.includes(productId);

                        if (isInCompareList) {
                            button.classList.add('active');
                            button.classList.add('compare-active');
                        } else {
                            button.classList.remove('active');
                            button.classList.remove('compare-active');
                        }
                    } catch (buttonError) {
                        console.error('خطأ في تحديث زر المقارنة:', buttonError);
                    }
                });

                // تحديث عداد المقارنة مرة واحدة فقط
                try {
                    if (compareBadge) {
                        compareBadge.textContent = data.items.length;

                        // إضافة تأثير بصري للعداد عند التحميل إذا كان هناك عناصر
                        if (data.items.length > 0) {
                            try {
                                compareBadge.classList.add('pulse');
                                setTimeout(() => {
                                    if (compareBadge) {
                                        compareBadge.classList.remove('pulse');
                                    }
                                }, 1000);
                            } catch (e) {
                                console.error('خطأ في تطبيق التأثيرات على عداد المقارنة:', e);
                            }
                        }
                    }
                } catch (badgeError) {
                    console.error('خطأ في تحديث عداد المقارنة:', badgeError);
                }
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل حالة أزرار المقارنة:', error);
        });

        // إضافة حدث النقر للأزرار
        validButtons.forEach(button => {
            try {
                if (!button) {
                    console.log('تم العثور على زر غير صالح عند إضافة مستمع الحدث');
                    return;
                }

                // التحقق من وجود سمة data-product-id
                if (!button.hasAttribute('data-product-id')) {
                    console.log('زر بدون معرف منتج عند إضافة مستمع الحدث:', button);
                    return;
                }

                // إزالة أي مستمعات أحداث سابقة
                button.removeEventListener('click', compareClickHandler);

                // إضافة مستمع الحدث الجديد
                button.addEventListener('click', compareClickHandler);
            } catch (eventError) {
                console.error('خطأ في إضافة مستمع الحدث للزر المقارنة:', eventError);
            }
        });

        console.log('تم الانتهاء من تهيئة أزرار المقارنة');
    } catch (mainError) {
        console.error('خطأ رئيسي في تهيئة أزرار المقارنة:', mainError);
    }
}

// معالج النقر على زر المقارنة
function compareClickHandler(e) {
    e.preventDefault();
    e.stopPropagation();

    // إيقاف انتشار الحدث لمنع تفعيل الرابط الممتد
    const event = e || window.event;
    if (event.stopPropagation) {
        event.stopPropagation();
    }
    if (event.cancelBubble !== null) {
        event.cancelBubble = true;
    }

    const productId = this.getAttribute('data-product-id');

    // إضافة تأثير حركي للزر
    this.classList.add('adding');

    // طباعة معلومات تصحيح الأخطاء في وحدة التحكم
    console.log('تم النقر على زر المقارنة للمنتج رقم:', productId);

    // إضافة أو إزالة المنتج من المقارنة
    toggleCompareItem(productId, this);

    // منع السلوك الافتراضي
    return false;
}

// إضافة أو إزالة منتج من المقارنة
function toggleCompareItem(productId, buttonElement) {
    // الحصول على رمز التحقق من CSRF
    const token = document.querySelector('input[name="__RequestVerificationToken"]')?.value;

    if (!token) {
        console.error('لم يتم العثور على رمز التحقق من CSRF');
        showToast('حدث خطأ أثناء تحديث المقارنة');
        if (buttonElement) {
            buttonElement.classList.remove('adding');
        }
        return;
    }

    console.log('جاري تحديث المقارنة للمنتج:', productId);

    // إضافة تأثير حركي للزر
    if (buttonElement) {
        buttonElement.classList.add('adding');
    }

    fetch('/Compare/AddToCompare', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'RequestVerificationToken': token,
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: `productId=${productId}&__RequestVerificationToken=${token}`
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            showToast(data.message);

            // تحديث حالة الزر
            if (buttonElement) {
                if (data.isInCompareList) {
                    buttonElement.classList.add('active');
                } else {
                    buttonElement.classList.remove('active');
                }
            }

            // تحديث عداد المقارنة بشكل فوري
            updateCompareCount();

            // إطلاق حدث تحديث المقارنة
            if (typeof $ !== 'undefined') {
                $(document).trigger('compare:updated', { count: data.count });
            }

            // إطلاق حدث مخصص لتحديث المقارنة
            const event = new CustomEvent('compareCountUpdated', { detail: { count: data.count } });
            document.dispatchEvent(event);

            // تحديث جميع أزرار المقارنة للمنتج نفسه
            updateAllCompareButtonsForProduct(productId, data.isInCompareList);

            // إذا تم إزالة منتج آخر لتجاوز الحد الأقصى
            if (data.removedId && data.removedId !== -1) {
                updateAllCompareButtonsForProduct(data.removedId, false);
            }

            console.log('تم تحديث المقارنة بنجاح');
        } else {
            console.error('خطأ في تحديث المقارنة:', data);
            showToast('حدث خطأ أثناء تحديث المقارنة');
        }

        // إزالة تأثير الإضافة من الزر
        if (buttonElement) {
            setTimeout(() => {
                buttonElement.classList.remove('adding');
            }, 500);
        }
    })
    .catch(error => {
        console.error('خطأ في طلب تحديث المقارنة:', error);
        showToast('حدث خطأ أثناء تحديث المقارنة');

        // إزالة تأثير الإضافة من الزر
        if (buttonElement) {
            buttonElement.classList.remove('adding');
        }
    });
}

// تحديث جميع أزرار المقارنة للمنتج نفسه
function updateAllCompareButtonsForProduct(productId, isActive) {
    console.log(`تحديث أزرار المقارنة للمنتج ${productId}، الحالة: ${isActive}`);

    const allCompareButtons = document.querySelectorAll(`.add-to-compare-btn[data-product-id="${productId}"]`);
    if (!allCompareButtons || allCompareButtons.length === 0) {
        console.log(`لم يتم العثور على أزرار مقارنة للمنتج ${productId}`);
        return;
    }

    allCompareButtons.forEach(button => {
        if (!button) {
            console.log('تم العثور على زر غير صالح عند تحديث أزرار المقارنة');
            return;
        }

        try {
            if (isActive) {
                button.classList.add('active');
                // تغيير لون الخلفية للزر باستخدام كلاس
                button.classList.add('compare-active');

                // إضافة تأثير نبض للزر
                button.classList.add('pulse');
                setTimeout(() => {
                    try {
                        if (button) button.classList.remove('pulse');
                    } catch (e) {
                        console.error('خطأ في إزالة تأثير النبض:', e);
                    }
                }, 1000);
            } else {
                button.classList.remove('active');
                // إعادة اللون الأصلي باستخدام كلاس
                button.classList.remove('compare-active');

                // إضافة تأثير للإزالة
                button.classList.add('flash');
                setTimeout(() => {
                    try {
                        if (button) button.classList.remove('flash');
                    } catch (e) {
                        console.error('خطأ في إزالة تأثير الوميض:', e);
                    }
                }, 1000);
            }
        } catch (buttonError) {
            console.error('خطأ في تحديث زر المقارنة:', buttonError);
        }
    });

    // لا نقوم بتحديث العداد هنا لتجنب التحديثات المتكررة
}

// تحديث عداد المقارنة - تم تحسينه ليكون أكثر كفاءة وليعمل مثل السلة
function updateCompareCount() {
    // تجنب التحديثات المتكررة
    if (window.isUpdatingCompareCount) {
        console.log('جاري تحديث عداد المقارنة بالفعل، تم تجاهل الطلب');
        return;
    }

    window.isUpdatingCompareCount = true;
    console.log('جاري تحديث عداد المقارنة');

    // التحقق من وجود عنصر عداد المقارنة قبل إجراء الطلب
    const compareBadge = document.querySelector('.compare-count');
    if (!compareBadge) {
        console.log('لم يتم العثور على عنصر عداد المقارنة (.compare-count)');
        window.isUpdatingCompareCount = false;
        return;
    }

    fetch('/Compare/GetCompareItemsCount')
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        try {
            const compareBadge = document.querySelector('.compare-count');
            if (compareBadge) {
                // حفظ القيمة القديمة للمقارنة
                const oldCount = parseInt(compareBadge.textContent) || 0;
                const newCount = data.count;

                // تحديث النص
                compareBadge.textContent = newCount;

                // إضافة تأثير حركي أكثر وضوحاً إذا تغيرت القيمة
                if (newCount !== oldCount) {
                    try {
                        // إزالة أي تأثيرات سابقة
                        compareBadge.classList.remove('pulse', 'bounce', 'flash', 'badge-highlight');

                        // إضافة تأثير جديد
                        compareBadge.classList.add('bounce');

                        // إضافة كلاس للتأثير البصري بدلاً من استخدام style مباشرة
                        compareBadge.classList.add('badge-highlight');

                        // إعادة اللون الأصلي بعد فترة
                        setTimeout(() => {
                            try {
                                if (compareBadge) {
                                    compareBadge.classList.remove('bounce');
                                    compareBadge.classList.remove('badge-highlight');
                                }
                            } catch (timeoutError) {
                                console.error('خطأ في إزالة تأثيرات العداد:', timeoutError);
                            }
                        }, 1500);
                    } catch (animationError) {
                        console.error('خطأ في تطبيق التأثيرات على عداد المقارنة:', animationError);
                    }
                }

                // طباعة معلومات تصحيح الأخطاء في وحدة التحكم
                console.log('تم تحديث عداد المقارنة:', newCount);
            } else {
                console.error('لم يتم العثور على عنصر عداد المقارنة (.compare-count)');
            }
        } catch (mainError) {
            console.error('خطأ رئيسي في تحديث عداد المقارنة:', mainError);
        }

        window.isUpdatingCompareCount = false;
    })
    .catch(error => {
        console.error('خطأ في تحديث عداد المقارنة:', error);
        window.isUpdatingCompareCount = false;
    });
}

// تحديث حالة أزرار المقارنة - تم تحسينه لتجنب الاستدعاءات المتكررة
function updateCompareButtonsState(items) {
    // تجنب التحديثات المتكررة
    if (window.isUpdatingCompareButtons) {
        console.log('جاري تحديث أزرار المقارنة بالفعل، تم تجاهل الطلب');
        return;
    }

    // التحقق من وجود أزرار المقارنة في الصفحة
    const compareButtons = document.querySelectorAll('.add-to-compare-btn');
    if (!compareButtons || compareButtons.length === 0) {
        console.log('لم يتم العثور على أزرار مقارنة للتحديث');
        return;
    }

    window.isUpdatingCompareButtons = true;

    try {
        // إذا تم تمرير العناصر، استخدمها مباشرة بدلاً من إجراء استدعاء جديد
        if (items) {
            updateCompareButtonsWithItems(items);
            window.isUpdatingCompareButtons = false;
            return;
        }

        // إذا لم يتم تمرير العناصر، قم بإجراء استدعاء للحصول عليها
        fetch('/Compare/GetCompareItems')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data && data.items) {
                updateCompareButtonsWithItems(data.items);
            }
            window.isUpdatingCompareButtons = false;
        })
        .catch(error => {
            console.error('خطأ في تحديث حالة أزرار المقارنة:', error);
            window.isUpdatingCompareButtons = false;
        });
    } catch (e) {
        console.error('خطأ في تحديث حالة أزرار المقارنة:', e);
        window.isUpdatingCompareButtons = false;
    }
}

// وظيفة مساعدة لتحديث أزرار المقارنة باستخدام قائمة العناصر
function updateCompareButtonsWithItems(items) {
    try {
        if (!items || !Array.isArray(items)) {
            console.error('قائمة العناصر غير صالحة:', items);
            return;
        }

        // استخدام try-catch للتعامل مع أي أخطاء في querySelectorAll
        let compareButtons = [];
        try {
            compareButtons = document.querySelectorAll('.add-to-compare-btn') || [];
        } catch (queryError) {
            console.error('خطأ في استعلام أزرار المقارنة:', queryError);
            return;
        }

        if (!compareButtons || compareButtons.length === 0) {
            console.log('لم يتم العثور على أزرار مقارنة');
            return;
        }

        console.log(`تحديث ${compareButtons.length} زر مقارنة مع ${items.length} عنصر`);

        // تحويل NodeList إلى مصفوفة وتصفية العناصر null أو undefined
        const validButtons = Array.from(compareButtons).filter(btn => btn !== null && btn !== undefined);

        if (validButtons.length === 0) {
            console.log('لم يتم العثور على أزرار مقارنة صالحة');
            return;
        }

        // تحديث حالة جميع الأزرار
        validButtons.forEach(button => {
            try {
                if (!button) {
                    return;
                }

                // التحقق من وجود واجهة DOM كاملة للعنصر
                if (!button.getAttribute || typeof button.getAttribute !== 'function') {
                    console.log('زر غير صالح (لا يحتوي على واجهة DOM كاملة)');
                    return;
                }

                const productIdAttr = button.getAttribute('data-product-id');
                if (!productIdAttr) {
                    console.log('زر بدون معرف منتج');
                    return;
                }

                const productId = parseInt(productIdAttr);
                const isInCompareList = items.includes(productId);

                // التحقق من وجود خاصية classList قبل استخدامها
                if (!button.classList || typeof button.classList.add !== 'function') {
                    console.log('زر بدون خاصية classList');
                    return;
                }

                // تحديث حالة الزر
                if (isInCompareList) {
                    button.classList.add('active');
                    button.classList.add('compare-active');
                } else {
                    button.classList.remove('active');
                    button.classList.remove('compare-active');
                }
            } catch (buttonError) {
                console.error('خطأ في تحديث زر المقارنة:', buttonError);
            }
        });

        console.log('تم تحديث حالة أزرار المقارنة');
    } catch (mainError) {
        console.error('خطأ رئيسي في تحديث أزرار المقارنة:', mainError);
    }
}

// تحديث عداد المفضلة - تم تحسينه ليكون أكثر كفاءة وليعمل مثل السلة
function updateWishlistCount() {
    // تجنب التحديثات المتكررة
    if (window.isUpdatingWishlistCount) {
        console.log('جاري تحديث عداد المفضلة بالفعل، تم تجاهل الطلب');
        return;
    }

    window.isUpdatingWishlistCount = true;
    console.log('جاري تحديث عداد المفضلة');

    // التحقق من وجود عنصر عداد المفضلة قبل إجراء الطلب
    const wishlistBadge = document.querySelector('.wishlist-count');
    if (!wishlistBadge) {
        console.log('لم يتم العثور على عنصر عداد المفضلة (.wishlist-count)');
        window.isUpdatingWishlistCount = false;
        return;
    }

    fetch('/Wishlist/GetWishlistItemsCount')
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        try {
            const wishlistBadge = document.querySelector('.wishlist-count');
            if (wishlistBadge) {
                // حفظ القيمة القديمة للمقارنة
                const oldCount = parseInt(wishlistBadge.textContent) || 0;
                const newCount = data.count;

                // تحديث النص
                wishlistBadge.textContent = newCount;

                // إضافة تأثير حركي أكثر وضوحاً إذا تغيرت القيمة
                if (newCount !== oldCount) {
                    try {
                        // إزالة أي تأثيرات سابقة
                        wishlistBadge.classList.remove('pulse', 'bounce', 'flash', 'badge-highlight');

                        // إضافة تأثير جديد
                        wishlistBadge.classList.add('bounce');

                        // إضافة كلاس للتأثير البصري بدلاً من استخدام style مباشرة
                        wishlistBadge.classList.add('badge-highlight');

                        // إعادة اللون الأصلي بعد فترة
                        setTimeout(() => {
                            try {
                                if (wishlistBadge) {
                                    wishlistBadge.classList.remove('bounce');
                                    wishlistBadge.classList.remove('badge-highlight');
                                }
                            } catch (timeoutError) {
                                console.error('خطأ في إزالة تأثيرات العداد:', timeoutError);
                            }
                        }, 1500);
                    } catch (animationError) {
                        console.error('خطأ في تطبيق التأثيرات على عداد المفضلة:', animationError);
                    }
                }

                // طباعة معلومات تصحيح الأخطاء في وحدة التحكم
                console.log('تم تحديث عداد المفضلة:', newCount);
            } else {
                console.error('لم يتم العثور على عنصر عداد المفضلة (.wishlist-count)');
            }
        } catch (mainError) {
            console.error('خطأ رئيسي في تحديث عداد المفضلة:', mainError);
        }

        window.isUpdatingWishlistCount = false;
    })
    .catch(error => {
        console.error('خطأ في تحديث عداد المفضلة:', error);
        window.isUpdatingWishlistCount = false;
    });
}

// تحديث عداد المفضلة فوراً
function updateWishlistCountImmediate() {
    console.log('🎯 تحديث عداد المفضلة فوراً');

    fetch('/Wishlist/GetWishlistItemsCount')
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        const wishlistBadge = document.querySelector('.wishlist-count');
        if (wishlistBadge) {
            wishlistBadge.textContent = data.count;

            // إضافة تأثير حركي
            wishlistBadge.classList.add('pulse');
            setTimeout(() => {
                wishlistBadge.classList.remove('pulse');
            }, 1000);

            console.log('✅ تم تحديث عداد المفضلة إلى:', data.count);
        }
    })
    .catch(error => {
        console.error('❌ خطأ في تحديث عداد المفضلة:', error);
    });
}

// تحديث حالة أزرار المفضلة - تم تحسينه لتجنب الاستدعاءات المتكررة
function updateWishlistButtonsState(items) {
    // تجنب التحديثات المتكررة
    if (window.isUpdatingWishlistButtons) {
        console.log('جاري تحديث أزرار المفضلة بالفعل، تم تجاهل الطلب');
        return;
    }

    // التحقق من وجود أزرار المفضلة في الصفحة
    const wishlistButtons = document.querySelectorAll('.add-to-wishlist-btn');
    if (!wishlistButtons || wishlistButtons.length === 0) {
        console.log('لم يتم العثور على أزرار مفضلة للتحديث');
        return;
    }

    window.isUpdatingWishlistButtons = true;

    try {
        // إذا تم تمرير العناصر، استخدمها مباشرة بدلاً من إجراء استدعاء جديد
        if (items) {
            updateWishlistButtonsWithItems(items);
            window.isUpdatingWishlistButtons = false;
            return;
        }

        // إذا لم يتم تمرير العناصر، قم بإجراء استدعاء للحصول عليها
        fetch('/Wishlist/GetWishlistItems')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data && data.items) {
                updateWishlistButtonsWithItems(data.items);
            }
            window.isUpdatingWishlistButtons = false;
        })
        .catch(error => {
            console.error('خطأ في تحديث حالة أزرار المفضلة:', error);
            window.isUpdatingWishlistButtons = false;
        });
    } catch (e) {
        console.error('خطأ في تحديث حالة أزرار المفضلة:', e);
        window.isUpdatingWishlistButtons = false;
    }
}

// وظيفة مساعدة لتحديث أزرار المفضلة باستخدام قائمة العناصر
function updateWishlistButtonsWithItems(items) {
    try {
        if (!items || !Array.isArray(items)) {
            console.error('قائمة العناصر غير صالحة:', items);
            return;
        }

        // استخدام try-catch للتعامل مع أي أخطاء في querySelectorAll
        let wishlistButtons = [];
        try {
            wishlistButtons = document.querySelectorAll('.add-to-wishlist-btn') || [];
        } catch (queryError) {
            console.error('خطأ في استعلام أزرار المفضلة:', queryError);
            return;
        }

        if (!wishlistButtons || wishlistButtons.length === 0) {
            console.log('لم يتم العثور على أزرار مفضلة');
            return;
        }

        console.log(`تحديث ${wishlistButtons.length} زر مفضلة مع ${items.length} عنصر`);

        // تحويل NodeList إلى مصفوفة وتصفية العناصر null أو undefined
        const validButtons = Array.from(wishlistButtons).filter(btn => btn !== null && btn !== undefined);

        if (validButtons.length === 0) {
            console.log('لم يتم العثور على أزرار مفضلة صالحة');
            return;
        }

        // تحديث حالة جميع الأزرار
        validButtons.forEach(button => {
            try {
                if (!button) {
                    return;
                }

                // التحقق من وجود واجهة DOM كاملة للعنصر
                if (!button.getAttribute || typeof button.getAttribute !== 'function') {
                    console.log('زر غير صالح (لا يحتوي على واجهة DOM كاملة)');
                    return;
                }

                const productIdAttr = button.getAttribute('data-product-id');
                if (!productIdAttr) {
                    console.log('زر بدون معرف منتج');
                    return;
                }

                const productId = parseInt(productIdAttr);
                const isInWishlist = items.includes(productId);

                // التحقق من وجود خاصية classList قبل استخدامها
                if (!button.classList || typeof button.classList.add !== 'function') {
                    console.log('زر بدون خاصية classList');
                    return;
                }

                // تحديث حالة الزر
                if (isInWishlist) {
                    button.classList.add('active');
                    button.classList.add('wishlist-active');
                } else {
                    button.classList.remove('active');
                    button.classList.remove('wishlist-active');
                }

                // تحديث أيقونة القلب إذا وجدت
                try {
                    const icon = button.querySelector('i');
                    if (icon && icon.classList) {
                        if (isInWishlist) {
                            // تحديث الأيقونة بناءً على نوعها
                            if (icon.classList.contains('bi-heart')) {
                                icon.classList.remove('bi-heart');
                                icon.classList.add('bi-heart-fill');
                            } else if (icon.classList.contains('fa-heart-o')) {
                                icon.classList.remove('fa-heart-o');
                                icon.classList.add('fa-heart');
                            }
                        } else {
                            // إعادة الأيقونة إلى حالتها الأصلية
                            if (icon.classList.contains('bi-heart-fill')) {
                                icon.classList.remove('bi-heart-fill');
                                icon.classList.add('bi-heart');
                            } else if (icon.classList.contains('fa-heart')) {
                                icon.classList.remove('fa-heart');
                                icon.classList.add('fa-heart-o');
                            }
                        }
                    }
                } catch (iconError) {
                    console.error('خطأ في تحديث أيقونة الزر:', iconError);
                }
            } catch (buttonError) {
                console.error('خطأ في تحديث زر المفضلة:', buttonError);
            }
        });

        console.log('تم تحديث حالة أزرار المفضلة');
    } catch (mainError) {
        console.error('خطأ رئيسي في تحديث أزرار المفضلة:', mainError);
    }
}

// عرض رسالة توست
function showToast(message) {
    // إنشاء عنصر التوست إذا لم يكن موجودًا
    let toast = document.getElementById('toast-notification');
    if (!toast) {
        toast = document.createElement('div');
        toast.id = 'toast-notification';

        // إضافة كلاس للتوست بدلاً من استخدام style مباشرة
        toast.className = 'toast-notification';

        document.body.appendChild(toast);
    }

    // عرض الرسالة
    toast.textContent = message;
    toast.classList.add('toast-visible');

    // إخفاء بعد 3 ثوان
    setTimeout(() => {
        toast.classList.remove('toast-visible');
    }, 3000);
}

// حذف ملفات تعريف الارتباط (Cookies)
function clearAllCookies() {
    try {
        // حذف ملفات تعريف الارتباط المعروفة
        deleteCookie('wishlist');
        deleteCookie('compareList');
        deleteCookie('cart');

        // تحديث العدادات
        updateWishlistCount();
        updateCompareCount();
        updateCartCount();

        // تحديث حالة الأزرار
        updateWishlistButtonsState([]);
        updateCompareButtonsState([]);

        console.log('تم حذف جميع ملفات تعريف الارتباط بنجاح');
        showToast('تم حذف جميع ملفات تعريف الارتباط بنجاح');

        // إطلاق أحداث التحديث
        if (typeof $ !== 'undefined') {
            $(document).trigger('wishlist:updated');
            $(document).trigger('compare:updated');
            $(document).trigger('cart:updated');
        }

        // إطلاق أحداث مخصصة
        document.dispatchEvent(new CustomEvent('wishlistCountUpdated', { detail: { count: 0 } }));
        document.dispatchEvent(new CustomEvent('compareCountUpdated', { detail: { count: 0 } }));
    } catch (error) {
        console.error('خطأ في حذف ملفات تعريف الارتباط:', error);
        showToast('حدث خطأ أثناء حذف ملفات تعريف الارتباط');
    }
}

// حذف ملف تعريف ارتباط محدد
function deleteCookie(name) {
    document.cookie = name + '=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
}

} // إغلاق حماية التحميل المتعدد
