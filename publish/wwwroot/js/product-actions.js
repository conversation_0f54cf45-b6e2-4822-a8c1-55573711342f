// وظائف لأزرار المنتجات (إضافة للسلة، المفضلة، المقارنة)

// حماية من التحميل المتعدد
if (window.productActionsLoaded) {
    console.log('ملف product-actions.js محمل بالفعل، تم تجاهل التحميل المتكرر');
} else {
    window.productActionsLoaded = true;

    document.addEventListener('DOMContentLoaded', function() {
        console.log('🚀 تم تحميل ملف product-actions.js - الإصدار الجديد!');

        // تعريف متغيرات عالمية لمنع التحديثات المتكررة
        window.isUpdatingWishlistCount = false;
        window.isUpdatingCompareCount = false;
        window.isUpdatingWishlistButtons = false;
        window.isUpdatingCompareButtons = false;

    // تهيئة أزرار السلة فقط - المفضلة منفصلة في wishlist-actions.js
    setTimeout(function() {
        try {
            initCartButtonsOnly();
        } catch (e) {
            console.error('خطأ في تهيئة أزرار السلة:', e);
        }
    }, 300);

    // تحديث عدادات السلة والمفضلة والمقارنة عند تحميل الصفحة مرة واحدة فقط
    try {
        // تحديث فوري من الـ cookies
        setTimeout(function() {
            try {
                updateCartCountFromCookies();
            } catch (cookieError) {
                console.error('خطأ في تحديث عداد السلة من الـ cookies:', cookieError);
            }
        }, 100);

        setTimeout(function() {
            try {
                updateCartCount();
            } catch (cartError) {
                console.error('خطأ في تحديث عداد السلة:', cartError);
            }

            // ملاحظة: تم نقل تحديث عداد المفضلة إلى ملف wishlist-actions.js منفصل

            try {
                updateCompareCount();
            } catch (compareError) {
                console.error('خطأ في تحديث عداد المقارنة:', compareError);
            }
        }, 800);
    } catch (e) {
        console.error('خطأ في تحديث العدادات:', e);
    }

    // إضافة مستمعات أحداث للتحديث المباشر للعدادات
    try {
        // ملاحظة: تم نقل مستمع أحداث المفضلة إلى ملف wishlist-actions.js منفصل

        document.addEventListener('compareCountUpdated', function(e) {
            try {
                if (!e || !e.detail) return;

                const compareBadge = document.querySelector('.compare-count');
                if (compareBadge && e.detail && e.detail.count !== undefined) {
                    compareBadge.textContent = e.detail.count;
                }
            } catch (e) {
                console.error('خطأ في تحديث عداد المقارنة:', e);
            }
        });
    } catch (eventError) {
        console.error('خطأ في إضافة مستمعات الأحداث:', eventError);
    }
});

function initProductButtons() {
    try {
        console.log('بدء تهيئة أزرار المنتجات');

        // تهيئة أزرار إضافة للسلة
        try {
            initCartButtonsOnly();
        } catch (cartError) {
            console.error('خطأ في تهيئة أزرار السلة:', cartError);
        }

        // ملاحظة: تم نقل تهيئة أزرار المفضلة إلى ملف wishlist-actions.js منفصل

        // تهيئة أزرار المقارنة
        try {
            initCompareButtons();
        } catch (compareError) {
            console.error('خطأ في تهيئة أزرار المقارنة:', compareError);
        }

        // منع انتقال الرابط عند النقر على الأزرار
        try {
            preventButtonLinkRedirect();
        } catch (redirectError) {
            console.error('خطأ في تهيئة منع انتقال الروابط:', redirectError);
        }

        console.log('تم الانتهاء من تهيئة أزرار المنتجات');
    } catch (mainError) {
        console.error('خطأ رئيسي في تهيئة أزرار المنتجات:', mainError);
    }
}

// منع انتقال الرابط عند النقر على الأزرار
function preventButtonLinkRedirect() {
    try {
        // استخدام try-catch للتعامل مع أي أخطاء في querySelectorAll
        let actionButtons = [];
        try {
            actionButtons = document.querySelectorAll('.product-item-action-btn') || [];
        } catch (queryError) {
            console.error('خطأ في استعلام أزرار الإجراءات:', queryError);
            return;
        }

        if (!actionButtons || actionButtons.length === 0) {
            console.log('لم يتم العثور على أزرار إجراءات');
            return;
        }

        // تحويل NodeList إلى مصفوفة وتصفية العناصر null أو undefined
        const validButtons = Array.from(actionButtons).filter(btn => btn !== null && btn !== undefined);

        if (validButtons.length === 0) {
            console.log('لم يتم العثور على أزرار إجراءات صالحة');
            return;
        }

        validButtons.forEach(button => {
            if (!button || typeof button.addEventListener !== 'function') {
                return;
            }

            button.addEventListener('click', function(e) {
                if (!e) return false;

                try {
                    e.preventDefault();
                    e.stopPropagation();

                    // إيقاف انتشار الحدث لمنع تفعيل الرابط الممتد
                    const event = e || window.event;
                    if (event.stopPropagation) {
                        event.stopPropagation();
                    }
                    if (event.cancelBubble !== null) {
                        event.cancelBubble = true;
                    }

                    // منع السلوك الافتراضي
                    if (event.preventDefault) {
                        event.preventDefault();
                    }
                } catch (eventError) {
                    console.error('خطأ في معالجة حدث النقر:', eventError);
                }

                return false;
            });
        });
    } catch (mainError) {
        console.error('خطأ رئيسي في تهيئة منع انتقال الروابط:', mainError);
    }
}

// تهيئة أزرار إضافة للسلة فقط (منفصلة عن المفضلة)
function initCartButtonsOnly() {
    // حماية من التنفيذ المتعدد
    if (window.cartButtonsInitialized) {
        console.log('أزرار السلة مهيأة بالفعل، تم تجاهل التهيئة المتكررة');
        return;
    }

    try {
        console.log('بدء تهيئة أزرار السلة');
        window.cartButtonsInitialized = true;

        // استخدام try-catch للتعامل مع أي أخطاء في querySelectorAll
        let cartButtons = [];
        try {
            cartButtons = document.querySelectorAll('.add-to-cart-btn') || [];
        } catch (queryError) {
            console.error('خطأ في استعلام أزرار السلة:', queryError);
            window.cartButtonsInitialized = false; // إعادة تعيين في حالة الخطأ
            return;
        }

        if (!cartButtons || cartButtons.length === 0) {
            console.log('لم يتم العثور على أزرار سلة');
            return;
        }

        console.log(`تم العثور على ${cartButtons.length} زر سلة`);

        // تحويل NodeList إلى مصفوفة وتصفية العناصر null أو undefined
        const validButtons = Array.from(cartButtons).filter(btn => btn !== null && btn !== undefined);

        if (validButtons.length === 0) {
            console.log('لم يتم العثور على أزرار سلة صالحة');
            return;
        }

        validButtons.forEach((button, index) => {
            if (!button || typeof button.addEventListener !== 'function' || typeof button.removeEventListener !== 'function') {
                console.log('زر سلة غير صالح');
                return;
            }

            console.log(`🛒 تهيئة زر السلة ${index + 1}:`, button.getAttribute('data-product-id'));

            try {
                // إزالة أي مستمعات أحداث سابقة
                button.removeEventListener('click', cartClickHandler);

                // إضافة مستمع الحدث الجديد
                button.addEventListener('click', cartClickHandler);

                console.log(`✅ تم إضافة event listener لزر السلة ${index + 1}`);
            } catch (eventError) {
                console.error('خطأ في إضافة مستمع الحدث لزر السلة:', eventError);
            }
        });

        console.log('تم الانتهاء من تهيئة أزرار السلة');
    } catch (mainError) {
        console.error('خطأ رئيسي في تهيئة أزرار السلة:', mainError);
    }
}

// معالج النقر على زر السلة
function cartClickHandler(e) {
    console.log('🛒 تم النقر على زر السلة!');

    e.preventDefault();
    e.stopPropagation();

    // إيقاف انتشار الحدث لمنع تفعيل الرابط الممتد
    const event = e || window.event;
    if (event.stopPropagation) {
        event.stopPropagation();
    }
    if (event.cancelBubble !== null) {
        event.cancelBubble = true;
    }

    // التحقق من أن الزر ليس في حالة معالجة
    if (this.classList.contains('adding') || this.disabled) {
        console.log('الزر في حالة معالجة، تم تجاهل النقر');
        return false;
    }

    const productId = this.getAttribute('data-product-id');
    console.log('🛒 معرف المنتج:', productId);

    // طباعة معلومات تصحيح الأخطاء في وحدة التحكم
    console.log('🛒 تم النقر على زر إضافة للسلة للمنتج رقم:', productId);

    // إرسال طلب AJAX لإضافة المنتج للسلة
    addToCart(productId, this);

    // منع السلوك الافتراضي
    return false;
}

// إضافة منتج للسلة
function addToCart(productId, buttonElement) {
    console.log('🛒 بدء دالة addToCart للمنتج:', productId);

    // التحقق من أن العملية ليست قيد التنفيذ بالفعل
    if (buttonElement && buttonElement.classList.contains('adding')) {
        console.log('العملية قيد التنفيذ بالفعل، تم تجاهل الطلب');
        return;
    }

    // إضافة تأثير حركي للزر وتعطيله مؤقتاً
    if (buttonElement) {
        buttonElement.classList.add('adding');
        buttonElement.disabled = true;
        console.log('🛒 تم تعطيل الزر مؤقتاً');
    }

    // الحصول على رمز التحقق من CSRF
    const token = document.querySelector('input[name="__RequestVerificationToken"]')?.value;
    console.log('🔑 CSRF Token:', token ? 'موجود' : 'غير موجود');

    if (!token) {
        console.error('❌ لم يتم العثور على رمز التحقق من CSRF');
        showToast('حدث خطأ أثناء إضافة المنتج للسلة');
        if (buttonElement) {
            buttonElement.classList.remove('adding');
            buttonElement.disabled = false;
            console.log('🔄 تم إعادة تفعيل الزر بسبب عدم وجود CSRF token');
        }
        return;
    }

    console.log('🛒 جاري إضافة المنتج للسلة:', productId);

    // إضافة timeout لإعادة تفعيل الزر في حالة عدم الاستجابة
    const timeoutId = setTimeout(() => {
        if (buttonElement) {
            buttonElement.classList.remove('adding');
            buttonElement.disabled = false;
            console.log('⏰ تم إعادة تفعيل الزر بسبب انتهاء المهلة الزمنية');
        }
    }, 5000); // 5 ثوان

    console.log('📡 إرسال طلب إضافة للسلة...');
    console.log('📡 URL:', '/Cart/AddToCart');
    console.log('📡 Body:', `productId=${productId}&quantity=1&__RequestVerificationToken=${token}`);

    fetch('/Cart/AddToCart', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'RequestVerificationToken': token,
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: `productId=${productId}&quantity=1&__RequestVerificationToken=${token}`
    })
    .then(response => {
        console.log('📡 استجابة الخادم:', response.status);
        console.log('📡 Headers:', response.headers);
        console.log('📡 Response OK:', response.ok);

        if (!response.ok) {
            console.error('❌ خطأ في الاستجابة:', response.status, response.statusText);
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        // التحقق من نوع المحتوى
        const contentType = response.headers.get('content-type');
        console.log('📡 Content-Type:', contentType);

        if (!contentType || !contentType.includes('application/json')) {
            console.warn('⚠️ الاستجابة ليست JSON');
            return response.text().then(text => {
                console.log('📡 Response Text:', text);
                throw new Error('الاستجابة ليست JSON صالح');
            });
        }

        return response.json();
    })
    .then(data => {
        console.log('📦 بيانات الاستجابة:', data);

        // إلغاء timeout
        clearTimeout(timeoutId);

        if (data.success) {
            console.log('✅ تمت إضافة المنتج للسلة بنجاح');
            showToast('تمت إضافة المنتج إلى السلة');

            // تحديث عداد السلة بشكل فوري
            updateCartCount();

            // إطلاق حدث تحديث السلة
            if (typeof $ !== 'undefined') {
                $(document).trigger('cart:updated');
            }

            // تحديث جميع أزرار السلة للمنتج نفسه
            updateAllCartButtonsForProduct(productId, true);

            console.log('✅ تم تحديث السلة بنجاح');
        } else {
            console.error('❌ خطأ في إضافة المنتج للسلة:', data);
            showToast('حدث خطأ أثناء إضافة المنتج للسلة');
        }

        // إزالة تأثير الإضافة من الزر وإعادة تفعيله
        if (buttonElement) {
            buttonElement.classList.remove('adding');
            buttonElement.disabled = false;
            console.log('🔄 تم إعادة تفعيل الزر بعد العملية الناجحة');
        }
    })
    .catch(error => {
        // إلغاء timeout
        clearTimeout(timeoutId);

        console.error('❌ خطأ في طلب إضافة المنتج للسلة:', error);
        console.error('❌ تفاصيل الخطأ:', error.message);
        console.error('❌ Stack trace:', error.stack);

        showToast('حدث خطأ أثناء إضافة المنتج للسلة: ' + error.message);

        // إزالة تأثير الإضافة من الزر وإعادة تفعيله
        if (buttonElement) {
            buttonElement.classList.remove('adding');
            buttonElement.disabled = false;
            console.log('🔄 تم إعادة تفعيل الزر بعد الخطأ');
        }
    });
}

// تحديث جميع أزرار السلة للمنتج نفسه
function updateAllCartButtonsForProduct(productId, isAdded) {
    const allCartButtons = document.querySelectorAll(`.add-to-cart-btn[data-product-id="${productId}"]`);
    allCartButtons.forEach(button => {
        if (isAdded) {
            button.classList.add('added');
            setTimeout(() => {
                button.classList.remove('added');
            }, 2000);
        }
    });
}

// دالة مساعدة لقراءة الـ cookies
function getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) {
        const cookieValue = parts.pop().split(';').shift();
        try {
            // فك تشفير URL encoding
            return decodeURIComponent(cookieValue);
        } catch (e) {
            console.warn('خطأ في فك تشفير cookie:', name, e);
            return cookieValue;
        }
    }
    return null;
}

// تحديث عداد السلة من الـ cookies مباشرة
function updateCartCountFromCookies() {
    try {
        const cart = getCookie('cart');
        let count = 0;

        if (cart && cart !== '[]' && cart !== '') {
            try {
                const items = JSON.parse(cart);
                if (Array.isArray(items)) {
                    count = items.reduce((total, item) => total + (item.quantity || 1), 0);
                }
            } catch (parseError) {
                console.warn('خطأ في تحليل JSON للسلة:', parseError, 'القيمة:', cart);
                count = 0;
            }
        }

        console.log(`تحديث عداد السلة من الـ cookies: ${count}`);

        const countElements = document.querySelectorAll('.cart-count');
        countElements.forEach(element => {
            if (element) {
                element.textContent = count;
            }
        });

        return count;
    } catch (error) {
        console.error('خطأ في قراءة عداد السلة من الـ cookies:', error);
        return 0;
    }
}

// تحديث عدد العناصر في السلة
function updateCartCount() {
    console.log('جاري تحديث عداد السلة');

    // تحديث فوري من الـ cookies
    const cookieCount = updateCartCountFromCookies();

    fetch('/Cart/GetCartItemsCount')
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        // البحث عن جميع عدادات السلة (للشاشات الكبيرة والصغيرة)
        const cartBadges = document.querySelectorAll('.cart-count');

        if (cartBadges && cartBadges.length > 0) {
            const newCount = data.count;

            console.log(`تم العثور على ${cartBadges.length} عداد سلة، العدد الجديد: ${newCount}`);

            cartBadges.forEach((cartBadge, index) => {
                if (cartBadge) {
                    // حفظ القيمة القديمة للمقارنة
                    const oldCount = parseInt(cartBadge.textContent) || 0;

                    // تحديث النص
                    cartBadge.textContent = newCount;

                    // إضافة تأثير حركي أكثر وضوحاً إذا تغيرت القيمة
                    if (newCount !== oldCount) {
                        try {
                            // إزالة أي تأثيرات سابقة
                            cartBadge.classList.remove('pulse', 'bounce', 'flash');

                            // إضافة تأثير جديد
                            cartBadge.classList.add('bounce');

                            // تغيير لون الخلفية مؤقتًا باستخدام كلاس
                            cartBadge.classList.add('badge-highlight');

                            // إعادة اللون الأصلي بعد فترة
                            setTimeout(() => {
                                if (cartBadge) {
                                    cartBadge.classList.remove('bounce');
                                    cartBadge.classList.remove('badge-highlight');
                                }
                            }, 1500);
                        } catch (e) {
                            console.error('خطأ في تطبيق التأثيرات على عداد السلة:', e);
                        }
                    }

                    console.log(`تم تحديث عداد السلة ${index + 1}: ${newCount}`);
                }
            });

            // طباعة معلومات تصحيح الأخطاء في وحدة التحكم
            console.log('تم تحديث جميع عدادات السلة:', newCount);
        } else {
            console.error('لم يتم العثور على أي عنصر عداد السلة (.cart-count)');
        }
    })
    .catch(error => {
        console.error('خطأ في تحديث عداد السلة:', error);
    });

    // تحديث المبلغ الإجمالي أيضاً
    updateCartTotal();
}

// تحديث المبلغ الإجمالي في الهيدر
function updateCartTotal() {
    console.log('جاري تحديث المبلغ الإجمالي للسلة');

    fetch('/Cart/GetCartTotal')
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        // البحث عن عنصر عرض السعر في الهيدر
        const cartPriceElement = document.querySelector('.cart-price');

        if (cartPriceElement) {
            const newTotal = data.total;
            console.log(`المبلغ الإجمالي الجديد: ${newTotal}`);

            // تنسيق المبلغ مع فواصل الآلاف
            const formattedTotal = newTotal.toLocaleString('ar-OM', {
                minimumFractionDigits: 3,
                maximumFractionDigits: 3
            });

            // تحديث النص
            cartPriceElement.textContent = `ر.ع ${formattedTotal}`;

            console.log('تم تحديث المبلغ الإجمالي في الهيدر');
        } else {
            console.error('لم يتم العثور على عنصر عرض السعر (.cart-price)');
        }
    })
    .catch(error => {
        console.error('خطأ في تحديث المبلغ الإجمالي:', error);
    });
}

// ملاحظة: تم نقل دالة initWishlistButtons إلى ملف wishlist-actions.js منفصل

// ملاحظة: تم نقل معالج النقر على زر المفضلة إلى ملف wishlist-actions.js منفصل

// ملاحظة: تم نقل دالة toggleWishlistItem إلى ملف wishlist-actions.js منفصل

// ملاحظة: تم نقل دالة updateAllWishlistButtonsForProduct إلى ملف wishlist-actions.js منفصل

// تهيئة أزرار المقارنة
function initCompareButtons() {
    try {
        console.log('بدء تهيئة أزرار المقارنة');

        // التحقق من وجود أزرار المقارنة في الصفحة
        const compareButtons = document.querySelectorAll('.add-to-compare-btn');
        if (!compareButtons || compareButtons.length === 0) {
            console.log('لم يتم العثور على أزرار مقارنة');
            return;
        }

        // تحويل NodeList إلى مصفوفة وتصفية العناصر null أو undefined
        const validButtons = Array.from(compareButtons).filter(button => button !== null && button !== undefined);

        console.log(`تم العثور على ${validButtons.length} زر مقارنة صالح`);

        if (validButtons.length === 0) {
            console.log('لم يتم العثور على أزرار مقارنة صالحة');
            return;
        }

        // التحقق من وجود عداد المقارنة في الصفحة
        const compareBadge = document.querySelector('.compare-count');
        if (!compareBadge) {
            console.log('لم يتم العثور على عنصر عداد المقارنة (.compare-count)');
        }

        // تحديث حالة الأزرار من الخادم - استدعاء واحد فقط
        fetch('/Compare/GetCompareItems')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data && data.items) {
                console.log(`تم استلام ${data.items.length} عنصر من المقارنة`);

                // تحديد الأزرار للمنتجات الموجودة في المقارنة
                // استخدام المصفوفة المصفاة بدلاً من NodeList الأصلية
                validButtons.forEach(button => {
                    try {
                        if (!button) {
                            console.log('تم العثور على زر غير صالح');
                            return;
                        }

                        const productIdAttr = button.getAttribute('data-product-id');
                        if (!productIdAttr) {
                            console.log('زر بدون معرف منتج:', button);
                            return;
                        }

                        const productId = parseInt(productIdAttr);
                        const isInCompareList = data.items.includes(productId);

                        if (isInCompareList) {
                            button.classList.add('active');
                            button.classList.add('compare-active');
                        } else {
                            button.classList.remove('active');
                            button.classList.remove('compare-active');
                        }
                    } catch (buttonError) {
                        console.error('خطأ في تحديث زر المقارنة:', buttonError);
                    }
                });

                // تحديث عداد المقارنة مرة واحدة فقط
                try {
                    if (compareBadge) {
                        compareBadge.textContent = data.items.length;

                        // إضافة تأثير بصري للعداد عند التحميل إذا كان هناك عناصر
                        if (data.items.length > 0) {
                            try {
                                compareBadge.classList.add('pulse');
                                setTimeout(() => {
                                    if (compareBadge) {
                                        compareBadge.classList.remove('pulse');
                                    }
                                }, 1000);
                            } catch (e) {
                                console.error('خطأ في تطبيق التأثيرات على عداد المقارنة:', e);
                            }
                        }
                    }
                } catch (badgeError) {
                    console.error('خطأ في تحديث عداد المقارنة:', badgeError);
                }
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل حالة أزرار المقارنة:', error);
        });

        // إضافة حدث النقر للأزرار
        validButtons.forEach(button => {
            try {
                if (!button) {
                    console.log('تم العثور على زر غير صالح عند إضافة مستمع الحدث');
                    return;
                }

                // التحقق من وجود سمة data-product-id
                if (!button.hasAttribute('data-product-id')) {
                    console.log('زر بدون معرف منتج عند إضافة مستمع الحدث:', button);
                    return;
                }

                // إزالة أي مستمعات أحداث سابقة
                button.removeEventListener('click', compareClickHandler);

                // إضافة مستمع الحدث الجديد
                button.addEventListener('click', compareClickHandler);
            } catch (eventError) {
                console.error('خطأ في إضافة مستمع الحدث للزر المقارنة:', eventError);
            }
        });

        console.log('تم الانتهاء من تهيئة أزرار المقارنة');
    } catch (mainError) {
        console.error('خطأ رئيسي في تهيئة أزرار المقارنة:', mainError);
    }
}

// معالج النقر على زر المقارنة
function compareClickHandler(e) {
    e.preventDefault();
    e.stopPropagation();

    // إيقاف انتشار الحدث لمنع تفعيل الرابط الممتد
    const event = e || window.event;
    if (event.stopPropagation) {
        event.stopPropagation();
    }
    if (event.cancelBubble !== null) {
        event.cancelBubble = true;
    }

    const productId = this.getAttribute('data-product-id');

    // إضافة تأثير حركي للزر
    this.classList.add('adding');

    // طباعة معلومات تصحيح الأخطاء في وحدة التحكم
    console.log('تم النقر على زر المقارنة للمنتج رقم:', productId);

    // إضافة أو إزالة المنتج من المقارنة
    toggleCompareItem(productId, this);

    // منع السلوك الافتراضي
    return false;
}

// إضافة أو إزالة منتج من المقارنة
function toggleCompareItem(productId, buttonElement) {
    // الحصول على رمز التحقق من CSRF
    const token = document.querySelector('input[name="__RequestVerificationToken"]')?.value;

    if (!token) {
        console.error('لم يتم العثور على رمز التحقق من CSRF');
        showToast('حدث خطأ أثناء تحديث المقارنة');
        if (buttonElement) {
            buttonElement.classList.remove('adding');
        }
        return;
    }

    console.log('جاري تحديث المقارنة للمنتج:', productId);

    // إضافة تأثير حركي للزر
    if (buttonElement) {
        buttonElement.classList.add('adding');
    }

    fetch('/Compare/AddToCompare', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'RequestVerificationToken': token,
            'X-Requested-With': 'XMLHttpRequest'
        },
        body: `productId=${productId}&__RequestVerificationToken=${token}`
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            showToast(data.message);

            // تحديث حالة الزر
            if (buttonElement) {
                if (data.isInCompareList) {
                    buttonElement.classList.add('active');
                } else {
                    buttonElement.classList.remove('active');
                }
            }

            // تحديث عداد المقارنة بشكل فوري
            updateCompareCount();

            // إطلاق حدث تحديث المقارنة
            if (typeof $ !== 'undefined') {
                $(document).trigger('compare:updated', { count: data.count });
            }

            // إطلاق حدث مخصص لتحديث المقارنة
            const event = new CustomEvent('compareCountUpdated', { detail: { count: data.count } });
            document.dispatchEvent(event);

            // تحديث جميع أزرار المقارنة للمنتج نفسه
            updateAllCompareButtonsForProduct(productId, data.isInCompareList);

            // إذا تم إزالة منتج آخر لتجاوز الحد الأقصى
            if (data.removedId && data.removedId !== -1) {
                updateAllCompareButtonsForProduct(data.removedId, false);
            }

            console.log('تم تحديث المقارنة بنجاح');
        } else {
            console.error('خطأ في تحديث المقارنة:', data);
            showToast('حدث خطأ أثناء تحديث المقارنة');
        }

        // إزالة تأثير الإضافة من الزر
        if (buttonElement) {
            setTimeout(() => {
                buttonElement.classList.remove('adding');
            }, 500);
        }
    })
    .catch(error => {
        console.error('خطأ في طلب تحديث المقارنة:', error);
        showToast('حدث خطأ أثناء تحديث المقارنة');

        // إزالة تأثير الإضافة من الزر
        if (buttonElement) {
            buttonElement.classList.remove('adding');
        }
    });
}

// تحديث جميع أزرار المقارنة للمنتج نفسه
function updateAllCompareButtonsForProduct(productId, isActive) {
    console.log(`تحديث أزرار المقارنة للمنتج ${productId}، الحالة: ${isActive}`);

    const allCompareButtons = document.querySelectorAll(`.add-to-compare-btn[data-product-id="${productId}"]`);
    if (!allCompareButtons || allCompareButtons.length === 0) {
        console.log(`لم يتم العثور على أزرار مقارنة للمنتج ${productId}`);
        return;
    }

    allCompareButtons.forEach(button => {
        if (!button) {
            console.log('تم العثور على زر غير صالح عند تحديث أزرار المقارنة');
            return;
        }

        try {
            if (isActive) {
                button.classList.add('active');
                // تغيير لون الخلفية للزر باستخدام كلاس
                button.classList.add('compare-active');

                // إضافة تأثير نبض للزر
                button.classList.add('pulse');
                setTimeout(() => {
                    try {
                        if (button) button.classList.remove('pulse');
                    } catch (e) {
                        console.error('خطأ في إزالة تأثير النبض:', e);
                    }
                }, 1000);
            } else {
                button.classList.remove('active');
                // إعادة اللون الأصلي باستخدام كلاس
                button.classList.remove('compare-active');

                // إضافة تأثير للإزالة
                button.classList.add('flash');
                setTimeout(() => {
                    try {
                        if (button) button.classList.remove('flash');
                    } catch (e) {
                        console.error('خطأ في إزالة تأثير الوميض:', e);
                    }
                }, 1000);
            }
        } catch (buttonError) {
            console.error('خطأ في تحديث زر المقارنة:', buttonError);
        }
    });

    // لا نقوم بتحديث العداد هنا لتجنب التحديثات المتكررة
}

// تحديث عداد المقارنة - تم تحسينه ليكون أكثر كفاءة وليعمل مثل السلة
function updateCompareCount() {
    // تجنب التحديثات المتكررة
    if (window.isUpdatingCompareCount) {
        console.log('جاري تحديث عداد المقارنة بالفعل، تم تجاهل الطلب');
        return;
    }

    window.isUpdatingCompareCount = true;
    console.log('جاري تحديث عداد المقارنة');

    // التحقق من وجود عنصر عداد المقارنة قبل إجراء الطلب
    const compareBadge = document.querySelector('.compare-count');
    if (!compareBadge) {
        console.log('لم يتم العثور على عنصر عداد المقارنة (.compare-count)');
        window.isUpdatingCompareCount = false;
        return;
    }

    fetch('/Compare/GetCompareItemsCount')
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        try {
            const compareBadge = document.querySelector('.compare-count');
            if (compareBadge) {
                // حفظ القيمة القديمة للمقارنة
                const oldCount = parseInt(compareBadge.textContent) || 0;
                const newCount = data.count;

                // تحديث النص
                compareBadge.textContent = newCount;

                // إضافة تأثير حركي أكثر وضوحاً إذا تغيرت القيمة
                if (newCount !== oldCount) {
                    try {
                        // إزالة أي تأثيرات سابقة
                        compareBadge.classList.remove('pulse', 'bounce', 'flash', 'badge-highlight');

                        // إضافة تأثير جديد
                        compareBadge.classList.add('bounce');

                        // إضافة كلاس للتأثير البصري بدلاً من استخدام style مباشرة
                        compareBadge.classList.add('badge-highlight');

                        // إعادة اللون الأصلي بعد فترة
                        setTimeout(() => {
                            try {
                                if (compareBadge) {
                                    compareBadge.classList.remove('bounce');
                                    compareBadge.classList.remove('badge-highlight');
                                }
                            } catch (timeoutError) {
                                console.error('خطأ في إزالة تأثيرات العداد:', timeoutError);
                            }
                        }, 1500);
                    } catch (animationError) {
                        console.error('خطأ في تطبيق التأثيرات على عداد المقارنة:', animationError);
                    }
                }

                // طباعة معلومات تصحيح الأخطاء في وحدة التحكم
                console.log('تم تحديث عداد المقارنة:', newCount);
            } else {
                console.error('لم يتم العثور على عنصر عداد المقارنة (.compare-count)');
            }
        } catch (mainError) {
            console.error('خطأ رئيسي في تحديث عداد المقارنة:', mainError);
        }

        window.isUpdatingCompareCount = false;
    })
    .catch(error => {
        console.error('خطأ في تحديث عداد المقارنة:', error);
        window.isUpdatingCompareCount = false;
    });
}

// تحديث حالة أزرار المقارنة - تم تحسينه لتجنب الاستدعاءات المتكررة
function updateCompareButtonsState(items) {
    // تجنب التحديثات المتكررة
    if (window.isUpdatingCompareButtons) {
        console.log('جاري تحديث أزرار المقارنة بالفعل، تم تجاهل الطلب');
        return;
    }

    // التحقق من وجود أزرار المقارنة في الصفحة
    const compareButtons = document.querySelectorAll('.add-to-compare-btn');
    if (!compareButtons || compareButtons.length === 0) {
        console.log('لم يتم العثور على أزرار مقارنة للتحديث');
        return;
    }

    window.isUpdatingCompareButtons = true;

    try {
        // إذا تم تمرير العناصر، استخدمها مباشرة بدلاً من إجراء استدعاء جديد
        if (items) {
            updateCompareButtonsWithItems(items);
            window.isUpdatingCompareButtons = false;
            return;
        }

        // إذا لم يتم تمرير العناصر، قم بإجراء استدعاء للحصول عليها
        fetch('/Compare/GetCompareItems')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data && data.items) {
                updateCompareButtonsWithItems(data.items);
            }
            window.isUpdatingCompareButtons = false;
        })
        .catch(error => {
            console.error('خطأ في تحديث حالة أزرار المقارنة:', error);
            window.isUpdatingCompareButtons = false;
        });
    } catch (e) {
        console.error('خطأ في تحديث حالة أزرار المقارنة:', e);
        window.isUpdatingCompareButtons = false;
    }
}

// وظيفة مساعدة لتحديث أزرار المقارنة باستخدام قائمة العناصر
function updateCompareButtonsWithItems(items) {
    try {
        if (!items || !Array.isArray(items)) {
            console.error('قائمة العناصر غير صالحة:', items);
            return;
        }

        // استخدام try-catch للتعامل مع أي أخطاء في querySelectorAll
        let compareButtons = [];
        try {
            compareButtons = document.querySelectorAll('.add-to-compare-btn') || [];
        } catch (queryError) {
            console.error('خطأ في استعلام أزرار المقارنة:', queryError);
            return;
        }

        if (!compareButtons || compareButtons.length === 0) {
            console.log('لم يتم العثور على أزرار مقارنة');
            return;
        }

        console.log(`تحديث ${compareButtons.length} زر مقارنة مع ${items.length} عنصر`);

        // تحويل NodeList إلى مصفوفة وتصفية العناصر null أو undefined
        const validButtons = Array.from(compareButtons).filter(btn => btn !== null && btn !== undefined);

        if (validButtons.length === 0) {
            console.log('لم يتم العثور على أزرار مقارنة صالحة');
            return;
        }

        // تحديث حالة جميع الأزرار
        validButtons.forEach(button => {
            try {
                if (!button) {
                    return;
                }

                // التحقق من وجود واجهة DOM كاملة للعنصر
                if (!button.getAttribute || typeof button.getAttribute !== 'function') {
                    console.log('زر غير صالح (لا يحتوي على واجهة DOM كاملة)');
                    return;
                }

                const productIdAttr = button.getAttribute('data-product-id');
                if (!productIdAttr) {
                    console.log('زر بدون معرف منتج');
                    return;
                }

                const productId = parseInt(productIdAttr);
                const isInCompareList = items.includes(productId);

                // التحقق من وجود خاصية classList قبل استخدامها
                if (!button.classList || typeof button.classList.add !== 'function') {
                    console.log('زر بدون خاصية classList');
                    return;
                }

                // تحديث حالة الزر
                if (isInCompareList) {
                    button.classList.add('active');
                    button.classList.add('compare-active');
                } else {
                    button.classList.remove('active');
                    button.classList.remove('compare-active');
                }
            } catch (buttonError) {
                console.error('خطأ في تحديث زر المقارنة:', buttonError);
            }
        });

        console.log('تم تحديث حالة أزرار المقارنة');
    } catch (mainError) {
        console.error('خطأ رئيسي في تحديث أزرار المقارنة:', mainError);
    }
}



// ملاحظة: تم نقل دالة updateWishlistCountDisplay إلى ملف wishlist-actions.js منفصل

// ملاحظة: تم نقل جميع دوال المفضلة إلى ملف wishlist-actions.js منفصل

// ملاحظة: تم نقل جميع دوال تحديث أزرار المفضلة إلى ملف wishlist-actions.js منفصل

// عرض رسالة توست
function showToast(message) {
    // إنشاء عنصر التوست إذا لم يكن موجودًا
    let toast = document.getElementById('toast-notification');
    if (!toast) {
        toast = document.createElement('div');
        toast.id = 'toast-notification';

        // إضافة كلاس للتوست بدلاً من استخدام style مباشرة
        toast.className = 'toast-notification';

        document.body.appendChild(toast);
    }

    // عرض الرسالة
    toast.textContent = message;
    toast.classList.add('toast-visible');

    // إخفاء بعد 3 ثوان
    setTimeout(() => {
        toast.classList.remove('toast-visible');
    }, 3000);
}

// حذف ملفات تعريف الارتباط (Cookies)
function clearAllCookies() {
    try {
        // حذف ملفات تعريف الارتباط المعروفة
        deleteCookie('wishlist');
        deleteCookie('compareList');
        deleteCookie('cart');

        // تحديث العدادات (عدا المفضلة)
        updateCompareCount();
        updateCartCount();

        // تحديث حالة الأزرار
        updateCompareButtonsState([]);

        console.log('تم حذف جميع ملفات تعريف الارتباط بنجاح');
        showToast('تم حذف جميع ملفات تعريف الارتباط بنجاح');

        // إطلاق أحداث التحديث
        if (typeof $ !== 'undefined') {
            $(document).trigger('compare:updated');
            $(document).trigger('cart:updated');
        }

        // إطلاق أحداث مخصصة
        document.dispatchEvent(new CustomEvent('compareCountUpdated', { detail: { count: 0 } }));
    } catch (error) {
        console.error('خطأ في حذف ملفات تعريف الارتباط:', error);
        showToast('حدث خطأ أثناء حذف ملفات تعريف الارتباط');
    }
}

// تصدير الدوال للاستخدام العام
window.updateCartCount = updateCartCount;
window.updateCartCountFromCookies = updateCartCountFromCookies;

// حذف ملف تعريف ارتباط محدد
function deleteCookie(name) {
    document.cookie = name + '=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
}

// ملاحظة: تم نقل تهيئة الصفحة إلى بداية الملف لتجنب التكرار

} // إغلاق حماية التحميل المتعدد
