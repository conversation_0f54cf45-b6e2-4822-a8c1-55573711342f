// وظائف المفضلة منفصلة عن السلة

// حماية من التحميل المتعدد
if (window.wishlistActionsLoaded) {
    console.log('ملف wishlist-actions.js محمل بالفعل، تم تجاهل التحميل المتكرر');
} else {
    window.wishlistActionsLoaded = true;

    document.addEventListener('DOMContentLoaded', function() {
        console.log('🎯 تم تحميل ملف wishlist-actions.js - منفصل عن السلة');

        // تعريف متغيرات عالمية للمفضلة فقط
        window.isUpdatingWishlistCount = false;
        window.isUpdatingWishlistButtons = false;

        // تحديث فوري لعداد المفضلة من الـ cookies
        setTimeout(function() {
            try {
                if (window.updateWishlistCountFromCookies) {
                    updateWishlistCountFromCookies();
                }
            } catch (e) {
                console.error('خطأ في تحديث عداد المفضلة:', e);
            }
        }, 100);

        // تهيئة أزرار المفضلة فقط
        setTimeout(function() {
            try {
                initWishlistButtonsOnly();
            } catch (e) {
                console.error('خطأ في تهيئة أزرار المفضلة:', e);
            }
        }, 500);
    });

    // تنظيف القلوب المكررة
    function cleanupDuplicateHearts() {
        const wishlistButtons = document.querySelectorAll('.add-to-wishlist-btn');
        wishlistButtons.forEach(button => {
            const icons = button.querySelectorAll('i');
            if (icons.length > 1) {
                // الاحتفاظ بالأيقونة الأولى فقط
                for (let i = 1; i < icons.length; i++) {
                    icons[i].remove();
                }
                console.log('تم إزالة قلوب مكررة من زر المفضلة');
            }
        });
    }

    // تهيئة أزرار المفضلة فقط
    function initWishlistButtonsOnly() {
        // حماية من التنفيذ المتعدد
        if (window.wishlistButtonsInitialized) {
            console.log('أزرار المفضلة مهيأة بالفعل، تم تجاهل التهيئة المتكررة');
            return;
        }

        try {
            console.log('بدء تهيئة أزرار المفضلة فقط');
            window.wishlistButtonsInitialized = true;

            // تنظيف القلوب المكررة أولاً
            cleanupDuplicateHearts();

            // البحث عن أزرار المفضلة فقط
            const wishlistButtons = document.querySelectorAll('.add-to-wishlist-btn');
            if (!wishlistButtons || wishlistButtons.length === 0) {
                console.log('لم يتم العثور على أزرار مفضلة');
                window.wishlistButtonsInitialized = false;
                return;
            }

            console.log(`تم العثور على ${wishlistButtons.length} زر مفضلة`);

            // تحديث حالة الأزرار من الخادم
            updateWishlistButtonStatesFromServer();

            // إضافة event listeners للأزرار
            wishlistButtons.forEach(button => {
                if (!button) return;

                try {
                    // التحقق من وجود data-product-id
                    if (!button.hasAttribute('data-product-id')) {
                        console.log('زر بدون معرف منتج:', button);
                        return;
                    }

                    // إزالة أي event listeners سابقة
                    button.removeEventListener('click', wishlistClickHandlerOnly);

                    // إضافة event listener جديد
                    button.addEventListener('click', wishlistClickHandlerOnly);
                } catch (eventError) {
                    console.error('خطأ في إضافة event listener للزر:', eventError);
                }
            });

            console.log('تم الانتهاء من تهيئة أزرار المفضلة');
        } catch (mainError) {
            console.error('خطأ رئيسي في تهيئة أزرار المفضلة:', mainError);
            window.wishlistButtonsInitialized = false;
        }
    }

    // تحديث حالة أزرار المفضلة من الخادم
    function updateWishlistButtonStatesFromServer() {
        fetch('/Wishlist/GetWishlistItems')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data && data.items) {
                console.log(`تم استلام ${data.items.length} عنصر من المفضلة`);

                // تحديث حالة كل زر
                const wishlistButtons = document.querySelectorAll('.add-to-wishlist-btn');
                wishlistButtons.forEach(button => {
                    if (!button) return;

                    try {
                        const productIdAttr = button.getAttribute('data-product-id');
                        if (!productIdAttr) return;

                        const productId = parseInt(productIdAttr);
                        const isInWishlist = data.items.includes(productId);

                        updateSingleWishlistButton(button, isInWishlist);
                    } catch (e) {
                        console.error('خطأ في تحديث زر المفضلة:', e);
                    }
                });

                // تحديث عداد المفضلة
                updateWishlistCountDisplay(data.items.length);
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل حالة أزرار المفضلة:', error);
        });
    }

    // تحديث زر مفضلة واحد
    function updateSingleWishlistButton(button, isActive) {
        try {
            if (isActive) {
                button.classList.add('active');
                button.classList.add('wishlist-active');

                // البحث عن الأيقونة الأولى فقط لتجنب التكرار
                const icon = button.querySelector('i:first-child');
                if (icon) {
                    // إزالة أي أيقونات إضافية
                    const extraIcons = button.querySelectorAll('i:not(:first-child)');
                    extraIcons.forEach(extraIcon => extraIcon.remove());

                    if (icon.classList.contains('bi-heart')) {
                        icon.classList.remove('bi-heart');
                        icon.classList.add('bi-heart-fill');
                    } else if (icon.classList.contains('fa-heart-o')) {
                        icon.classList.remove('fa-heart-o');
                        icon.classList.add('fa-heart');
                    }
                }
            } else {
                button.classList.remove('active');
                button.classList.remove('wishlist-active');

                // البحث عن الأيقونة الأولى فقط لتجنب التكرار
                const icon = button.querySelector('i:first-child');
                if (icon) {
                    // إزالة أي أيقونات إضافية
                    const extraIcons = button.querySelectorAll('i:not(:first-child)');
                    extraIcons.forEach(extraIcon => extraIcon.remove());

                    if (icon.classList.contains('bi-heart-fill')) {
                        icon.classList.remove('bi-heart-fill');
                        icon.classList.add('bi-heart');
                    } else if (icon.classList.contains('fa-heart')) {
                        icon.classList.remove('fa-heart');
                        icon.classList.add('fa-heart-o');
                    }
                }
            }
        } catch (e) {
            console.error('خطأ في تحديث زر المفضلة:', e);
        }
    }

    // معالج النقر على زر المفضلة فقط
    function wishlistClickHandlerOnly(e) {
        e.preventDefault();
        e.stopPropagation();

        // منع انتشار الحدث
        const event = e || window.event;
        if (event.stopPropagation) {
            event.stopPropagation();
        }
        if (event.cancelBubble !== null) {
            event.cancelBubble = true;
        }

        const productId = this.getAttribute('data-product-id');
        console.log('تم النقر على زر المفضلة للمنتج رقم:', productId);

        // إضافة تأثير حركي
        this.classList.add('adding');

        // تبديل حالة المفضلة
        toggleWishlistItemOnly(productId, this);

        return false;
    }

    // تبديل حالة المفضلة فقط
    function toggleWishlistItemOnly(productId, buttonElement) {
        // الحصول على رمز CSRF
        const token = document.querySelector('input[name="__RequestVerificationToken"]')?.value;

        if (!token) {
            console.error('لم يتم العثور على رمز التحقق من CSRF');
            showWishlistToast('حدث خطأ أثناء تحديث المفضلة');
            if (buttonElement) {
                buttonElement.classList.remove('adding');
            }
            return;
        }

        console.log('جاري تحديث المفضلة للمنتج:', productId);

        fetch('/Wishlist/AddToWishlist', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'RequestVerificationToken': token,
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: `productId=${productId}&__RequestVerificationToken=${token}`
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // عرض رسالة نجاح
                const message = data.isInWishlist ? 'تمت إضافة المنتج إلى المفضلة' : 'تمت إزالة المنتج من المفضلة';
                showWishlistToast(message);

                // تحديث حالة الزر
                updateSingleWishlistButton(buttonElement, data.isInWishlist);

                // تحديث عداد المفضلة
                updateWishlistCountDisplay(data.count);

                // تحديث جميع أزرار المفضلة للمنتج نفسه
                updateAllWishlistButtonsForProductOnly(productId, data.isInWishlist);

                console.log('تم تحديث المفضلة بنجاح');
            } else {
                console.error('خطأ في تحديث المفضلة:', data);
                showWishlistToast('حدث خطأ أثناء تحديث المفضلة');
            }

            // إزالة تأثير الإضافة
            if (buttonElement) {
                setTimeout(() => {
                    buttonElement.classList.remove('adding');
                }, 500);
            }
        })
        .catch(error => {
            console.error('خطأ في طلب تحديث المفضلة:', error);
            showWishlistToast('حدث خطأ أثناء تحديث المفضلة');

            if (buttonElement) {
                buttonElement.classList.remove('adding');
            }
        });
    }

    // تحديث جميع أزرار المفضلة للمنتج نفسه
    function updateAllWishlistButtonsForProductOnly(productId, isActive) {
        const allWishlistButtons = document.querySelectorAll(`button[data-product-id="${productId}"].add-to-wishlist-btn`);
        
        allWishlistButtons.forEach(button => {
            updateSingleWishlistButton(button, isActive);
        });
    }

    // دالة مساعدة لقراءة الـ cookies
    function getCookie(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) {
            const cookieValue = parts.pop().split(';').shift();
            try {
                // فك تشفير URL encoding
                return decodeURIComponent(cookieValue);
            } catch (e) {
                console.warn('خطأ في فك تشفير cookie:', name, e);
                return cookieValue;
            }
        }
        return null;
    }

    // تحديث عداد المفضلة من الـ cookies مباشرة
    function updateWishlistCountFromCookies() {
        try {
            const wishlist = getCookie('wishlist');
            let count = 0;

            if (wishlist && wishlist !== '[]' && wishlist !== '') {
                try {
                    const items = JSON.parse(wishlist);
                    count = Array.isArray(items) ? items.length : 0;
                } catch (parseError) {
                    console.warn('خطأ في تحليل JSON للمفضلة:', parseError, 'القيمة:', wishlist);
                    count = 0;
                }
            }

            console.log(`تحديث عداد المفضلة من الـ cookies: ${count}`);

            const countElements = document.querySelectorAll('.wishlist-count');
            countElements.forEach(element => {
                if (element) {
                    element.textContent = count;
                }
            });

            return count;
        } catch (error) {
            console.error('خطأ في قراءة عداد المفضلة من الـ cookies:', error);
            return 0;
        }
    }

    // تحديث عداد المفضلة فقط
    function updateWishlistCountDisplay(count) {
        // منع التحديث المتعدد
        if (window.isUpdatingWishlistCount) {
            console.log('تحديث عداد المفضلة قيد التنفيذ، تم تجاهل الطلب');
            return;
        }

        window.isUpdatingWishlistCount = true;

        try {
            console.log(`🔢 تحديث عداد المفضلة إلى: ${count}`);

            // البحث عن عدادات المفضلة فقط
            const wishlistBadges = document.querySelectorAll('.wishlist-count');
            console.log(`🔍 تم العثور على ${wishlistBadges.length} عداد مفضلة`);

            if (wishlistBadges.length === 0) {
                console.log('⚠️ لم يتم العثور على أي عداد مفضلة');
                return;
            }

            wishlistBadges.forEach((badge, index) => {
                if (badge) {
                    const oldCount = parseInt(badge.textContent) || 0;
                    console.log(`   ${index + 1}. العداد القديم: ${oldCount}, الجديد: ${count}`);

                    badge.textContent = count;

                    // تأثير حركي إذا تغيرت القيمة
                    if (count !== oldCount) {
                        badge.classList.add('pulse');
                        setTimeout(() => {
                            if (badge) badge.classList.remove('pulse');
                        }, 1000);
                    }
                }
            });

            console.log('✅ تم تحديث جميع عدادات المفضلة بنجاح');
        } catch (error) {
            console.error('❌ خطأ في تحديث عداد المفضلة:', error);
        } finally {
            // إعادة تعيين العلامة بعد فترة قصيرة
            setTimeout(() => {
                window.isUpdatingWishlistCount = false;
            }, 500);
        }
    }

    // تحديث عداد المفضلة من الخادم
    function updateWishlistCount() {
        if (window.isUpdatingWishlistCount) {
            return;
        }

        // تحديث فوري من الـ cookies
        const cookieCount = updateWishlistCountFromCookies();

        window.isUpdatingWishlistCount = true;
        console.log('بدء تحديث عداد المفضلة من الخادم...');

        fetch('/Wishlist/GetWishlistItemsCount', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('استجابة عداد المفضلة من الخادم:', data);
            if (data.count !== undefined) {
                updateWishlistCountDisplay(data.count);
            }
        })
        .catch(error => {
            console.error('خطأ في تحديث عداد المفضلة من الخادم:', error);
            // في حالة الخطأ، استخدم القيمة من الـ cookies
            console.log('استخدام القيمة من الـ cookies بدلاً من الخادم');
        })
        .finally(() => {
            window.isUpdatingWishlistCount = false;
        });
    }

    // عرض رسالة خاصة بالمفضلة
    function showWishlistToast(message) {
        // إنشاء toast خاص بالمفضلة
        let toast = document.getElementById('wishlist-toast-notification');
        if (!toast) {
            toast = document.createElement('div');
            toast.id = 'wishlist-toast-notification';
            toast.style.position = 'fixed';
            toast.style.bottom = '20px';
            toast.style.right = '20px';
            toast.style.backgroundColor = 'rgba(106, 13, 173, 0.9)';
            toast.style.color = 'white';
            toast.style.padding = '12px 20px';
            toast.style.borderRadius = '4px';
            toast.style.zIndex = '1000';
            toast.style.transition = 'opacity 0.5s ease-in-out';
            toast.style.opacity = '0';
            toast.style.boxShadow = '0 4px 8px rgba(0,0,0,0.1)';
            document.body.appendChild(toast);
        }

        // عرض الرسالة
        toast.textContent = message;
        toast.style.opacity = '1';

        // إخفاء بعد 3 ثوان
        setTimeout(() => {
            toast.style.opacity = '0';
        }, 3000);
    }

    // جعل الدوال متاحة عالمياً للمفضلة فقط
    window.initWishlistButtonsOnly = initWishlistButtonsOnly;
    window.toggleWishlistItemOnly = toggleWishlistItemOnly;
    window.updateWishlistCountDisplay = updateWishlistCountDisplay;
    window.updateWishlistCount = updateWishlistCount;
    window.updateWishlistCountFromCookies = updateWishlistCountFromCookies;
    window.cleanupDuplicateHearts = cleanupDuplicateHearts;

} // إغلاق حماية التحميل المتعدد
