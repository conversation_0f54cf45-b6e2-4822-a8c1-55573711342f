// التحكم في نافذة تسجيل الدخول الجانبية

document.addEventListener('DOMContentLoaded', function() {
    // العناصر الرئيسية
    const loginButton = document.getElementById('loginButton');
    const sideLoginOverlay = document.getElementById('sideLoginOverlay');
    const sideLoginPanel = document.getElementById('sideLoginPanel');
    const sideLoginClose = document.getElementById('sideLoginClose');

    // أقسام النافذة الجانبية
    const loginSection = document.getElementById('loginSection');
    const forgotPasswordSection = document.getElementById('forgotPasswordSection');
    const registerSection = document.getElementById('registerSection');

    // أزرار التنقل بين الأقسام
    const showForgotPassword = document.getElementById('showForgotPassword');
    const showRegister = document.getElementById('showRegister');
    const backToLogin1 = document.getElementById('backToLogin1');
    const backToLogin2 = document.getElementById('backToLogin2');

    // حقول كلمة المرور وأزرار إظهار/إخفاء كلمة المرور
    const passwordField = document.getElementById('password');
    const passwordToggle = document.getElementById('passwordToggle');
    const registerPasswordField = document.getElementById('registerPassword');
    const registerPasswordToggle = document.getElementById('registerPasswordToggle');
    const registerConfirmPasswordField = document.getElementById('registerConfirmPassword');
    const registerConfirmPasswordToggle = document.getElementById('registerConfirmPasswordToggle');

    // فتح النافذة الجانبية عند النقر على زر تسجيل الدخول
    if (loginButton) {
        loginButton.addEventListener('click', function(e) {
            e.preventDefault();
            openSideLogin();
            showSection(loginSection);
        });
    }

    // إغلاق النافذة الجانبية عند النقر على زر الإغلاق
    if (sideLoginClose) {
        sideLoginClose.addEventListener('click', function() {
            closeSideLogin();
        });
    }

    // إغلاق النافذة الجانبية عند النقر خارجها
    if (sideLoginOverlay) {
        sideLoginOverlay.addEventListener('click', function(e) {
            if (e.target === sideLoginOverlay) {
                closeSideLogin();
            }
        });
    }

    // التنقل بين الأقسام
    if (showForgotPassword) {
        showForgotPassword.addEventListener('click', function(e) {
            e.preventDefault();
            showSection(forgotPasswordSection);
        });
    }

    if (showRegister) {
        showRegister.addEventListener('click', function(e) {
            e.preventDefault();
            showSection(registerSection);
        });
    }

    if (backToLogin1) {
        backToLogin1.addEventListener('click', function(e) {
            e.preventDefault();
            showSection(loginSection);
        });
    }

    if (backToLogin2) {
        backToLogin2.addEventListener('click', function(e) {
            e.preventDefault();
            showSection(loginSection);
        });
    }

    // إظهار/إخفاء كلمة المرور في قسم تسجيل الدخول
    setupPasswordToggle(passwordToggle, passwordField);

    // إظهار/إخفاء كلمة المرور في قسم التسجيل
    setupPasswordToggle(registerPasswordToggle, registerPasswordField);
    setupPasswordToggle(registerConfirmPasswordToggle, registerConfirmPasswordField);

    // وظيفة لإعداد زر إظهار/إخفاء كلمة المرور
    function setupPasswordToggle(toggleButton, passwordInput) {
        if (toggleButton && passwordInput) {
            toggleButton.addEventListener('click', function() {
                if (passwordInput.type === 'password') {
                    passwordInput.type = 'text';
                    toggleButton.innerHTML = '<i class="bi bi-eye-slash"></i>';
                } else {
                    passwordInput.type = 'password';
                    toggleButton.innerHTML = '<i class="bi bi-eye"></i>';
                }
            });
        }
    }

    // وظيفة لإظهار قسم معين وإخفاء الأقسام الأخرى
    function showSection(section) {
        // إخفاء جميع الأقسام
        loginSection.style.display = 'none';
        forgotPasswordSection.style.display = 'none';
        registerSection.style.display = 'none';

        // إظهار القسم المطلوب
        section.style.display = 'block';
    }

    // تعريف وظائف فتح وإغلاق النافذة الجانبية كوظائف عالمية
    window.openSideLogin = function() {
        if (sideLoginOverlay && sideLoginPanel) {
            sideLoginOverlay.style.display = 'block';
            setTimeout(function() {
                sideLoginPanel.classList.add('active');
            }, 10);
            document.body.style.overflow = 'hidden';
        }
    };

    window.closeSideLogin = function() {
        if (sideLoginOverlay && sideLoginPanel) {
            sideLoginPanel.classList.remove('active');
            setTimeout(function() {
                sideLoginOverlay.style.display = 'none';
                // إعادة عرض قسم تسجيل الدخول عند إغلاق النافذة
                showSection(loginSection);
            }, 300);
            document.body.style.overflow = '';
        }
    };

    // إغلاق النافذة الجانبية عند الضغط على زر ESC
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && sideLoginOverlay.style.display === 'block') {
            closeSideLogin();
        }
    });
});
