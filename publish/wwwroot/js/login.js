// ملف JavaScript مخصص لصفحة تسجيل الدخول
$(document).ready(function() {
    console.log("تم تحميل ملف login.js");
    
    // التأكد من أن نموذج تسجيل الدخول يعمل بشكل صحيح
    $("#account").on("submit", function(e) {
        console.log("تم إرسال نموذج تسجيل الدخول من ملف login.js");
        
        // الحصول على البيانات من النموذج
        var email = $("#Input_Email").val();
        var password = $("#Input_Password").val();
        var rememberMe = $("#Input_RememberMe").is(":checked");
        
        console.log("البريد الإلكتروني: " + email);
        console.log("تم تحديد تذكرني: " + rememberMe);
        
        // التحقق من صحة البيانات
        if (!email || !password) {
            console.log("يرجى ملء جميع الحقول المطلوبة");
            return false;
        }
        
        // السماح بإرسال النموذج بشكل طبيعي
        return true;
    });
    
    // التأكد من أن زر تسجيل الدخول يعمل بشكل صحيح
    $("#login-submit").on("click", function(e) {
        console.log("تم النقر على زر تسجيل الدخول من ملف login.js");
        
        // التحقق من صحة البيانات
        var email = $("#Input_Email").val();
        var password = $("#Input_Password").val();
        
        if (!email || !password) {
            console.log("يرجى ملء جميع الحقول المطلوبة");
            alert("يرجى ملء جميع الحقول المطلوبة");
            e.preventDefault();
            return false;
        }
        
        // السماح بإرسال النموذج بشكل طبيعي
        return true;
    });
    
    // إضافة مستمع للنقر على زر تسجيل الدخول باستخدام طريقة مختلفة
    document.getElementById("login-submit").addEventListener("click", function(e) {
        console.log("تم النقر على زر تسجيل الدخول باستخدام addEventListener");
        
        // التحقق من صحة البيانات
        var email = document.getElementById("Input_Email").value;
        var password = document.getElementById("Input_Password").value;
        
        if (!email || !password) {
            console.log("يرجى ملء جميع الحقول المطلوبة (addEventListener)");
            // لا نمنع الإرسال هنا لتجنب التعارض مع المستمع السابق
        }
    });
});
