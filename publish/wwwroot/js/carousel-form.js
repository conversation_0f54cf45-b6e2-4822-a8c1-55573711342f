// ملف JavaScript مخصص لنماذج الشريط المتحرك
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل ملف carousel-form.js');
    
    // التحقق من وجود نموذج إضافة صورة الشريط المتحرك
    const carouselForm = document.querySelector('form[action*="CarouselImages/Create"]');
    
    if (carouselForm) {
        console.log('تم العثور على نموذج إضافة صورة الشريط المتحرك');
        
        // معاينة الصورة قبل التحميل
        const imageFile = document.getElementById('imageFile');
        const imagePreview = document.getElementById('imagePreview');
        
        if (imageFile && imagePreview) {
            console.log('تم العثور على حقل الصورة وعنصر المعاينة');
            
            imageFile.addEventListener('change', function() {
                const file = this.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        imagePreview.src = e.target.result;
                        imagePreview.style.display = 'block';
                    }
                    reader.readAsDataURL(file);
                }
            });
        } else {
            console.warn('لم يتم العثور على حقل الصورة أو عنصر المعاينة');
        }
        
        // معالجة تقديم النموذج
        carouselForm.addEventListener('submit', function(e) {
            console.log('تم تقديم النموذج');
            
            // لا نمنع السلوك الافتراضي للنموذج
            // نترك النموذج يعمل بشكل طبيعي
        });
    } else {
        console.log('لم يتم العثور على نموذج إضافة صورة الشريط المتحرك');
    }
});
