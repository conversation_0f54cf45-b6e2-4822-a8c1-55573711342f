<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>تحميل صور الشريط المتحرك - راعي المخور</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.rtl.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <style>
        body {
            font-family: 'Tajawal', sans-serif;
            background-color: #f8f9fa;
            padding: 20px;
        }
        .upload-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #6a0dad;
        }
        .form-label {
            font-weight: bold;
        }
        .preview-container {
            margin-top: 20px;
            text-align: center;
        }
        #imagePreview {
            max-width: 100%;
            max-height: 300px;
            border-radius: 5px;
            display: none;
        }
        .success-message {
            display: none;
            background-color: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            text-align: center;
        }
        .error-message {
            display: none;
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="upload-container">
            <div class="header">
                <h1>تحميل صور الشريط المتحرك</h1>
                <p>قم بتحميل الصور التي ستظهر في الشريط المتحرك في الصفحة الرئيسية</p>
            </div>
            
            <form id="uploadForm">
                <div class="mb-3">
                    <label for="title" class="form-label">عنوان الصورة</label>
                    <input type="text" class="form-control" id="title" name="title" required>
                </div>
                
                <div class="mb-3">
                    <label for="description" class="form-label">وصف الصورة (اختياري)</label>
                    <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                </div>
                
                <div class="mb-3">
                    <label for="linkUrl" class="form-label">رابط التوجيه (اختياري)</label>
                    <input type="url" class="form-control" id="linkUrl" name="linkUrl">
                    <div class="form-text">الرابط الذي سيتم التوجيه إليه عند النقر على الصورة</div>
                </div>
                
                <div class="mb-3">
                    <label for="displayOrder" class="form-label">ترتيب العرض</label>
                    <input type="number" class="form-control" id="displayOrder" name="displayOrder" value="0" min="0">
                    <div class="form-text">الرقم الأصغر يظهر أولاً</div>
                </div>
                
                <div class="mb-3">
                    <label for="imageFile" class="form-label">الصورة</label>
                    <input type="file" class="form-control" id="imageFile" name="imageFile" accept="image/*" required>
                    <div class="form-text">يفضل صورة بأبعاد 800×500 بكسل</div>
                </div>
                
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="isActive" name="isActive" checked>
                    <label class="form-check-label" for="isActive">نشط</label>
                </div>
                
                <div class="preview-container">
                    <img id="imagePreview" src="#" alt="معاينة الصورة">
                </div>
                
                <div class="d-grid gap-2 mt-4">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-cloud-upload"></i> تحميل الصورة
                    </button>
                    <a href="/" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-right"></i> العودة إلى الصفحة الرئيسية
                    </a>
                </div>
            </form>
            
            <div id="successMessage" class="success-message">
                <i class="bi bi-check-circle-fill"></i> تم تحميل الصورة بنجاح!
            </div>
            
            <div id="errorMessage" class="error-message">
                <i class="bi bi-exclamation-triangle-fill"></i> <span id="errorText">حدث خطأ أثناء تحميل الصورة</span>
            </div>
        </div>
    </div>
    
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // معاينة الصورة قبل التحميل
            const imageFile = document.getElementById('imageFile');
            const imagePreview = document.getElementById('imagePreview');
            
            imageFile.addEventListener('change', function() {
                const file = this.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        imagePreview.src = e.target.result;
                        imagePreview.style.display = 'block';
                    }
                    reader.readAsDataURL(file);
                }
            });
            
            // معالجة تحميل النموذج
            const uploadForm = document.getElementById('uploadForm');
            const successMessage = document.getElementById('successMessage');
            const errorMessage = document.getElementById('errorMessage');
            const errorText = document.getElementById('errorText');
            
            uploadForm.addEventListener('submit', function(e) {
                e.preventDefault();
                
                // إخفاء الرسائل السابقة
                successMessage.style.display = 'none';
                errorMessage.style.display = 'none';
                
                // إنشاء كائن FormData
                const formData = new FormData(uploadForm);
                
                // إرسال البيانات باستخدام Fetch API
                fetch('/api/carousel', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    if (!response.ok) {
                        return response.text().then(text => {
                            throw new Error(text || 'حدث خطأ أثناء تحميل الصورة');
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    // عرض رسالة النجاح
                    successMessage.style.display = 'block';
                    
                    // إعادة تعيين النموذج
                    uploadForm.reset();
                    imagePreview.style.display = 'none';
                    
                    // التمرير إلى رسالة النجاح
                    successMessage.scrollIntoView({ behavior: 'smooth' });
                })
                .catch(error => {
                    // عرض رسالة الخطأ
                    errorText.textContent = error.message || 'حدث خطأ أثناء تحميل الصورة';
                    errorMessage.style.display = 'block';
                    
                    // التمرير إلى رسالة الخطأ
                    errorMessage.scrollIntoView({ behavior: 'smooth' });
                });
            });
        });
    </script>
</body>
</html>
