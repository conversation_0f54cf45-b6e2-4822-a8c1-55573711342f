/* تنسيقات لوحة تحكم المدير */

.admin-dashboard {
    background-color: #f8f9fa;
    min-height: 100vh;
}

.admin-dashboard-title {
    color: #6a0dad;
    font-weight: 700;
    margin-bottom: 2rem;
    position: relative;
    padding-bottom: 1rem;
}

.admin-dashboard-title::after {
    content: "";
    position: absolute;
    bottom: 0;
    right: 0;
    width: 100px;
    height: 4px;
    background-color: #6a0dad;
    border-radius: 2px;
}

/* بطاقات الإحصائيات */
.stats-card {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
}

.stats-card-body {
    padding: 1.5rem;
    display: flex;
    align-items: center;
    flex-grow: 1;
}

.stats-card-icon {
    font-size: 2.5rem;
    margin-left: 1rem;
}

.stats-card-content {
    flex-grow: 1;
}

.stats-card-title {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
    opacity: 0.9;
}

.stats-card-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0;
}

.stats-card-footer {
    padding: 0.75rem 1.5rem;
    background-color: rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: flex-end;
}

.stats-card-footer a {
    display: flex;
    align-items: center;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.stats-card-footer a i {
    margin-right: 0.5rem;
    transition: transform 0.3s ease;
}

.stats-card-footer a:hover i {
    transform: translateX(-5px);
}

/* بطاقات عامة */
.admin-card {
    background-color: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    height: 100%;
}

.admin-card-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.admin-card-title {
    color: #6a0dad;
    font-weight: 600;
    margin-bottom: 0;
}

.admin-card-body {
    padding: 1.5rem;
}

/* بطاقات حالة الطلبات */
.order-status-card {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
    padding: 1rem;
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
}

.order-status-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.order-status-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-left: 1rem;
    color: white;
}

.order-status-icon.pending {
    background-color: #ffc107;
}

.order-status-icon.confirmed {
    background-color: #17a2b8;
}

.order-status-icon.processing {
    background-color: #007bff;
}

.order-status-icon.shipped {
    background-color: #6c757d;
}

.order-status-icon.delivered {
    background-color: #28a745;
}

.order-status-icon.cancelled {
    background-color: #dc3545;
}

.order-status-details h5 {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.order-status-details h3 {
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 0;
}

/* جدول البيانات */
.table {
    margin-bottom: 0;
}

.table th {
    font-weight: 600;
    color: #6a0dad;
    border-top: none;
}

.table td {
    vertical-align: middle;
}

/* صورة المنتج المصغرة */
.product-img-small {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    overflow: hidden;
}

.product-img-small img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* صورة الشريط المتحرك المصغرة */
.carousel-image-thumbnail {
    width: 80px;
    height: 45px;
    border-radius: 8px;
    overflow: hidden;
}

.carousel-image-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* معاينة الصورة الحالية */
.current-image-preview {
    max-width: 300px;
    margin: 0 auto;
}

.current-image-preview img {
    width: 100%;
    height: auto;
    border-radius: 8px;
}

/* ألوان مخصصة */
.bg-purple {
    background-color: #6a0dad;
}

/* قائمة الإدارة الجانبية */
.admin-menu {
    background-color: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    margin-bottom: 2rem;
}

.admin-menu-header {
    background-color: #6a0dad;
    color: white;
    padding: 1.25rem;
    text-align: center;
}

.admin-menu-header h5 {
    margin-bottom: 0;
    font-weight: 600;
}

.admin-menu-body {
    padding: 1rem 0;
}

.admin-menu-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.admin-menu-item {
    padding: 0;
    margin: 0;
    transition: all 0.3s ease;
}

.admin-menu-item.active {
    background-color: rgba(106, 13, 173, 0.1);
}

.admin-menu-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
}

.admin-menu-link:hover {
    background-color: rgba(106, 13, 173, 0.05);
    color: #6a0dad;
}

.admin-menu-item.active .admin-menu-link {
    color: #6a0dad;
    font-weight: 500;
}

.admin-menu-link i {
    font-size: 1.25rem;
    margin-left: 0.75rem;
    width: 24px;
    text-align: center;
}

/* تنسيقات متجاوبة */
@media (max-width: 767.98px) {
    .stats-card-body {
        padding: 1rem;
    }

    .stats-card-icon {
        font-size: 2rem;
        margin-left: 0.75rem;
    }

    .stats-card-value {
        font-size: 1.5rem;
    }

    .admin-card-header,
    .admin-card-body {
        padding: 1rem;
    }

    .admin-menu {
        margin-bottom: 1.5rem;
    }
}
