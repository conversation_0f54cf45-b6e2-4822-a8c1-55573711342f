/* تنسيقات شريط التنقل السفلي للهواتف المحمولة */

/* إخفاء شريط التنقل السفلي على الشاشات الكبيرة */
.mobile-bottom-nav {
    display: none;
}

/* عرض شريط التنقل السفلي على الشاشات الصغيرة فقط */
@media (max-width: 767.98px) {
    /* تنسيق شريط التنقل السفلي */
    .mobile-bottom-nav {
        display: flex;
        justify-content: space-around;
        align-items: center;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 60px;
        background-color: #fff;
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
        z-index: 1000;
        padding: 5px 0;
    }

    /* تنسيق عناصر شريط التنقل */
    .mobile-bottom-nav-item {
        flex: 1;
        text-align: center;
    }

    /* تنسيق روابط شريط التنقل */
    .mobile-bottom-nav-link {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #666;
        text-decoration: none;
        font-size: 0.7rem;
        position: relative;
        padding: 5px 0;
    }

    /* تنسيق الأيقونات */
    .mobile-bottom-nav-link i {
        font-size: 1.3rem;
        margin-bottom: 2px;
    }

    /* تنسيق الرابط النشط */
    .mobile-bottom-nav-link.active {
        color: #6a0dad;
    }

    /* تنسيق العدادات */
    .mobile-badge {
        position: absolute;
        top: -2px;
        right: 50%;
        transform: translateX(8px);
        font-size: 0.6rem;
        padding: 0.15rem 0.3rem;
        min-width: 15px;
        height: 15px;
    }

    /* إضافة مساحة أسفل الصفحة لتجنب تداخل المحتوى مع شريط التنقل */
    body {
        padding-bottom: 60px;
    }

    /* إخفاء أيقونات الهيدر العلوي على الشاشات الصغيرة */
    .left-section {
        display: none !important;
    }

    /* تعديل تنسيق الفوتر على الشاشات الصغيرة */
    .footer {
        margin-bottom: 60px;
    }
}
