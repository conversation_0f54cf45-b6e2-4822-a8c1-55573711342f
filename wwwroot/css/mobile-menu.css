/* تنسيقات القائمة العمودية للهواتف المحمولة */

/* إخفاء القائمة العمودية على الشاشات الكبيرة */
.mobile-menu-container {
    display: none;
}

/* عرض القائمة العمودية على الشاشات الصغيرة فقط */
@media (max-width: 767.98px) {
    /* تنسيق حاوية القائمة */
    .mobile-menu-container {
        display: block; /* تم إعادته إلى block لإظهار القائمة العمودية */
        padding: 15px 0;
        background-color: #fff;
    }

    /* تنسيق عنوان العمود */
    .mobile-menu-title {
        color: #6a0dad;
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 15px;
        padding-bottom: 5px;
        border-bottom: 2px solid #6a0dad;
    }

    /* تنسيق قائمة العناصر */
    .mobile-menu-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    /* تنسيق عنصر القائمة */
    .mobile-menu-item {
        margin-bottom: 10px;
    }

    /* تنسيق رابط القائمة */
    .mobile-menu-link {
        display: flex;
        align-items: center;
        color: #333;
        text-decoration: none;
        padding: 8px 10px;
        border-radius: 5px;
        transition: all 0.3s ease;
    }

    /* تنسيق الأيقونة في الرابط */
    .mobile-menu-link i {
        margin-left: 8px;
        font-size: 1.1rem;
    }

    /* تنسيق الرابط عند التحويم */
    .mobile-menu-link:hover {
        background-color: rgba(106, 13, 173, 0.1);
        color: #6a0dad;
    }

    /* تنسيق الرابط النشط */
    .mobile-menu-link.active {
        background-color: rgba(106, 13, 173, 0.1);
        color: #6a0dad;
        font-weight: 600;
    }

    /* تنسيق عمود القائمة */
    .mobile-menu-column {
        padding: 0 10px;
    }

    /* إخفاء زر التبديل في شريط التنقل */
    .navbar-toggler {
        display: none !important;
    }

    /* إخفاء القائمة الأفقية على الشاشات الصغيرة */
    .navbar-nav.main-nav {
        display: none;
    }

    /* تعديل تنسيق حاوية القائمة المنسدلة */
    .navbar-collapse {
        background-color: #fff;
        padding: 10px;
        border-radius: 5px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
}
