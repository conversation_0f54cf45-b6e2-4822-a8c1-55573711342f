/* قسم المنتجات الأكثر مبيعاً في الصفحة الرئيسية */

.best-sellers-section {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 2px solid #f0f0f0;
}

.best-sellers-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
}

.collapsible-header {
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 8px 0;
    border-radius: 6px;
}

.collapsible-header:hover {
    background: rgba(106, 13, 173, 0.05);
    padding-left: 8px;
    padding-right: 8px;
}

.toggle-icon {
    font-size: 0.9rem;
    transition: transform 0.3s ease;
    color: #6a0dad;
}

.best-sellers-title i {
    color: #ffd700;
    font-size: 1.2rem;
}

.best-sellers-count {
    font-size: 0.9rem;
    color: #6a0dad;
    font-weight: 500;
    background: rgba(106, 13, 173, 0.1);
    padding: 2px 8px;
    border-radius: 12px;
    margin-right: 8px;
}

.best-sellers-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* عناصر المنتجات الأكثر مبيعاً */
.best-seller-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border: 1px solid #f0f0f0;
    border-radius: 12px;
    transition: all 0.3s ease;
    cursor: pointer;
    background: #fafafa;
    position: relative;
}

.best-seller-item:hover {
    background: white;
    border-color: #6a0dad;
    box-shadow: 0 4px 15px rgba(106, 13, 173, 0.15);
    transform: translateY(-2px);
}

/* رقم الترتيب */
.best-seller-rank {
    margin-left: 15px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.rank-number {
    font-size: 1.2rem;
    font-weight: bold;
    color: #6a0dad;
    min-width: 30px;
    text-align: center;
}

/* صورة المنتج */
.best-seller-image-container {
    position: relative;
    margin-left: 15px;
    flex-shrink: 0;
}

.best-seller-image {
    width: 70px;
    height: 70px;
    object-fit: cover;
    border-radius: 10px;
    border: 2px solid #f0f0f0;
    transition: all 0.3s ease;
}

.best-seller-item:hover .best-seller-image {
    border-color: #6a0dad;
    transform: scale(1.05);
}

/* شارة المنتج */
.best-seller-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #333;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    border: 2px solid white;
}

/* معلومات المنتج */
.best-seller-info {
    flex: 1;
    min-width: 0;
}

.best-seller-name {
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: #333;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.best-seller-price {
    font-size: 1rem;
    font-weight: 600;
    color: #6a0dad;
}

/* أزرار الإجراءات */
.best-seller-actions {
    margin-right: 10px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.best-seller-wishlist-btn {
    background: white;
    border: 2px solid #f0f0f0;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #999;
}

.best-seller-wishlist-btn:hover {
    border-color: #6a0dad;
    color: #6a0dad;
    transform: scale(1.1);
}

.best-seller-wishlist-btn.active {
    background: #6a0dad;
    border-color: #6a0dad;
    color: white;
}

.best-seller-wishlist-btn i {
    font-size: 14px;
}

/* رسائل التحميل والخطأ */
.loading-spinner {
    text-align: center;
    padding: 30px 20px;
    color: #6a0dad;
    font-size: 0.9rem;
    position: relative;
}

.loading-spinner::before {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #6a0dad;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.no-products-message,
.error-message {
    text-align: center;
    padding: 30px 20px;
    color: #666;
    font-size: 0.9rem;
    border-radius: 8px;
    background: #f8f9fa;
    border: 1px dashed #dee2e6;
}

.error-message {
    color: #dc3545;
    background: #f8d7da;
    border-color: #f5c6cb;
}

.no-products-message {
    color: #6c757d;
    font-style: italic;
}

/* تنسيقات للشاشات الصغيرة */
@media (max-width: 768px) {
    .best-seller-item {
        padding: 12px;
    }
    
    .best-seller-image {
        width: 60px;
        height: 60px;
    }
    
    .best-seller-name {
        font-size: 0.9rem;
    }
    
    .best-seller-price {
        font-size: 0.9rem;
    }
    
    .best-seller-wishlist-btn {
        width: 32px;
        height: 32px;
    }
    
    .best-seller-wishlist-btn i {
        font-size: 12px;
    }
}

/* تنسيقات للشاشات الكبيرة جداً */
@media (min-width: 1400px) {
    .best-seller-item {
        padding: 18px;
    }

    .best-seller-image {
        width: 80px;
        height: 80px;
    }

    .best-seller-name {
        font-size: 1.1rem;
    }

    .best-seller-price {
        font-size: 1.1rem;
    }
}

/* قسم المنتجات الأكثر مبيعاً للهواتف */
.mobile-best-sellers {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #f0f0f0;
}

.mobile-best-sellers-header {
    margin-bottom: 20px;
}

.mobile-best-sellers-title {
    font-size: 1.2rem;
    font-weight: 700;
    margin: 0;
    color: #333;
    display: flex;
    align-items: center;
    gap: 10px;
}

.mobile-best-sellers-count {
    font-size: 0.9rem;
    color: #6a0dad;
    font-weight: 500;
    background: rgba(106, 13, 173, 0.1);
    padding: 4px 10px;
    border-radius: 15px;
    margin-right: 10px;
}

.mobile-best-sellers-container {
    display: flex;
    overflow-x: auto;
    gap: 15px;
    padding-bottom: 10px;
    scroll-behavior: smooth;
}

/* شريط التمرير المخصص للهواتف */
.mobile-best-sellers-container::-webkit-scrollbar {
    height: 4px;
}

.mobile-best-sellers-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.mobile-best-sellers-container::-webkit-scrollbar-thumb {
    background: #6a0dad;
    border-radius: 2px;
}

.mobile-best-sellers-container::-webkit-scrollbar-thumb:hover {
    background: #5a0b9a;
}

/* عناصر المنتجات في وضع الهاتف */
.mobile-best-seller-item {
    flex: 0 0 235px;
    background: #fafafa;
    border: 1px solid #f0f0f0;
    border-radius: 12px;
    padding: 15px;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.mobile-best-seller-item:hover {
    background: white;
    border-color: #6a0dad;
    box-shadow: 0 4px 15px rgba(106, 13, 173, 0.15);
    transform: translateY(-2px);
}

.mobile-best-seller-content {
    display: flex;
    align-items: center;
    gap: 15px;
}

.mobile-best-seller-rank {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mobile-rank-number {
    font-size: 1.1rem;
    font-weight: bold;
    color: #6a0dad;
    min-width: 25px;
    text-align: center;
}

.mobile-best-seller-image-container {
    position: relative;
    flex-shrink: 0;
}

.mobile-best-seller-image {
    width: 70px;
    height: 70px;
    object-fit: cover;
    border-radius: 10px;
    border: 2px solid #f0f0f0;
    transition: all 0.3s ease;
}

.mobile-best-seller-item:hover .mobile-best-seller-image {
    border-color: #6a0dad;
    transform: scale(1.05);
}

.mobile-best-seller-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #333;
    border-radius: 50%;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 9px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    border: 2px solid white;
}

.mobile-best-seller-info {
    flex: 1;
    min-width: 0;
}

.mobile-best-seller-name {
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 8px 0;
    color: #333;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.mobile-best-seller-price {
    font-size: 1rem;
    font-weight: 600;
    color: #6a0dad;
    margin-bottom: 10px;
}

.mobile-best-seller-actions {
    display: flex;
    gap: 8px;
}

.mobile-best-seller-wishlist-btn {
    background: white;
    border: 2px solid #f0f0f0;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #999;
}

.mobile-best-seller-wishlist-btn:hover {
    border-color: #6a0dad;
    color: #6a0dad;
    transform: scale(1.1);
}

.mobile-best-seller-wishlist-btn.active {
    background: #6a0dad;
    border-color: #6a0dad;
    color: white;
}

.mobile-best-seller-wishlist-btn i {
    font-size: 12px;
}

/* رسائل التحميل للهواتف */
.mobile-best-sellers .loading-spinner {
    text-align: center;
    padding: 30px 20px;
    color: #6a0dad;
    font-size: 0.9rem;
}

.mobile-best-sellers .no-products-message,
.mobile-best-sellers .error-message {
    text-align: center;
    padding: 30px 20px;
    color: #666;
    font-size: 0.9rem;
    border-radius: 8px;
    background: #f8f9fa;
    border: 1px dashed #dee2e6;
}

/* إخفاء القسم في الشاشات الكبيرة */
@media (min-width: 992px) {
    .mobile-best-sellers {
        display: none !important;
    }
}
