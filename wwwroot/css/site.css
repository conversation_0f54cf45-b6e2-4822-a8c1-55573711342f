@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Almarai:wght@300;400;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Reem+Kufi:wght@400;500;600;700&display=swap');

:root {
  /* Main Theme Colors - Basado en ameeraa.online */
  --primary-color: #e83e8c; /* <PERSON>/Fu<PERSON><PERSON> (color principal de Ameera) */
  --secondary-color: #f06292; /* Rosa claro */
  --accent-color: #ff4081; /* Rosa intenso */
  --light-color: #f8f9fa; /* Gris muy claro */
  --dark-color: #212121; /* Negro intenso */

  /* Status Colors */
  --success-color: #4caf50; /* Verde */
  --info-color: #2196f3; /* Azul */
  --warning-color: #ff9800; /* Naranja */
  --danger-color: #f44336; /* Rojo */
  --available-color: rgba(106, 13, 173, 0.1); /* Color para "متوفر" */
  --available-text: #6a0dad; /* Color de texto para "متوفر" */

  /* Background & Surface Colors */
  --background-color: #ffffff; /* Blanco puro como en Ameera */
  --card-bg: #ffffff;
  --header-bg: #ffffff; /* Fondo blanco para el encabezado */
  --footer-bg: #f8f9fa; /* Gris muy claro para el pie de página */

  /* Text Colors */
  --text-color: #333333; /* Color de texto principal */
  --text-muted: #6c757d; /* Texto secundario */
  --text-light: #ffffff; /* Texto claro */
  --text-dark: #212121; /* Texto oscuro */
  --text-link: #e83e8c; /* Enlaces en rosa */

  /* Category Colors */
  --category-fabric: #8b5cf6; /* Violet 500 */
  --category-accessories: #ec4899; /* Pink 500 */
  --category-abaya: #6366f1; /* Indigo 500 */
  --category-mukhwar: #14b8a6; /* Teal 500 */

  /* UI Elements */
  --border-radius-sm: 6px;
  --border-radius: 12px;
  --border-radius-lg: 16px;
  --border-radius-xl: 24px;
  --border-radius-full: 9999px;

  --box-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --box-shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --box-shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --box-shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  --transition-fast: all 0.2s ease;
  --transition: all 0.3s ease;
  --transition-slow: all 0.5s ease;

  /* Layout */
  --container-max-width: 1280px;
  --section-padding: 5rem 0;
  --header-height: 70px;
  --footer-height: 60px;
  --sidebar-width: 280px;

  /* Z-index layers */
  --z-negative: -1;
  --z-elevate: 1;
  --z-dropdown: 10;
  --z-sticky: 100;
  --z-drawer: 200;
  --z-modal: 300;
  --z-popover: 400;
  --z-tooltip: 500;
}

html {
  font-size: 12px;
  position: relative;
  min-height: 100%;
  scroll-behavior: smooth;
}

@media (min-width: 768px) {
  html {
    font-size: 12px;
  }
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Almarai', 'Tajawal', sans-serif;
  background-color: var(--background-color);
  color: var(--text-color);
  line-height: 1.6;
  overflow-x: hidden;
  direction: rtl;
  text-align: right;
  font-size: 0.85rem;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.container {
  max-width: auto;
  margin: 0 auto;
  padding: 0 15px;
  width: 100%;
}

.container-fluid {
  width: 100%;
  padding: 0 15px;
  margin: 0 auto;
}

.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -15px;
  justify-content: space-around;
}

.col {
  padding: 0 15px;
  flex: 1;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Reem Kufi', 'Almarai', sans-serif;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--dark-color);
  line-height: 1.3;
}

h1 {
  font-size: 1.8rem;
  font-weight: 800;
}

h2 {
  font-size: 1.5rem;
  position: relative;
}

h2.section-title {
  text-align: center;
  margin-bottom: 1.8rem;
  font-weight: 800;
}

h2.section-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: var(--accent-color);
  border-radius: var(--border-radius-full);
}

h3 {
  font-size: 1.3rem;
}

h4 {
  font-size: 1.1rem;
}

h5 {
  font-size: 1rem;
}

h6 {
  font-size: 0.9rem;
}

.display-4 {
  font-weight: 800;
  margin-bottom: 1.2rem;
  color: var(--text-light);
  font-size: 2.2rem;
  line-height: 1.2;
}

.lead {
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
}

.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--secondary-color) !important; }
.text-accent { color: var(--accent-color) !important; }
.text-muted { color: var(--text-muted) !important; }
.text-light { color: var(--text-light) !important; }
.text-dark { color: var(--text-dark) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--secondary-color) !important; }
.bg-accent { background-color: var(--accent-color) !important; }
.bg-light { background-color: var(--light-color) !important; }
.bg-dark { background-color: var(--dark-color) !important; }

.fw-light { font-weight: 300 !important; }
.fw-normal { font-weight: 400 !important; }
.fw-medium { font-weight: 500 !important; }
.fw-semibold { font-weight: 600 !important; }
.fw-bold { font-weight: 700 !important; }
.fw-extrabold { font-weight: 800 !important; }

/* Animations */
@keyframes subtle-zoom {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.05);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* تنسيق أزرار المفضلة النشطة - دائرة بيضاء وقلب بنفسجي (للصفحات الأخرى) */
.wishlist-active {
  background-color: white !important;
  border: 1px solid #e0e0e0 !important;
}

/* تنسيق أزرار المقارنة النشطة */
.compare-active {
  background-color: #6a0dad !important;
  color: white !important;
}

/* تنسيق تأثير العدادات */
.badge-highlight {
  background-color: #6a0dad !important;
  animation: pulse 1.5s ease-in-out;
}

/* تنسيق أزرار المفضلة والمقارنة */
.wishlist-active {
  background-color: white !important;
  border: 1px solid #e0e0e0 !important;
}

.wishlist-active i {
  color: #6a0dad !important;
}

.compare-active {
  color: white !important;
  background-color: #6a0dad !important;
}

/* تأثيرات حركية للأزرار */
.pulse {
  animation: pulse 1.5s ease-in-out;
}

.bounce {
  animation: bounce 0.8s ease-in-out;
}

.flash {
  animation: flash 1s ease-in-out;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-5px); }
  60% { transform: translateY(-3px); }
}

@keyframes flash {
  0%, 50%, 100% { opacity: 1; }
  25%, 75% { opacity: 0.5; }
}

/* تنسيق رسائل التوست */
.toast-notification {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background-color: rgba(106, 13, 173, 0.9);
  color: white;
  padding: 12px 20px;
  border-radius: 4px;
  z-index: 1000;
  transition: opacity 0.5s ease-in-out;
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
  opacity: 0;
}

.toast-visible {
  opacity: 1;
}

/* Buttons - Estilo Ameera boutique */
.btn {
  border-radius: 0; /* Botones cuadrados como en Ameera */
  padding: 0.5rem 1.2rem;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  font-family: 'Almarai', sans-serif;
  letter-spacing: 0.5px;
  z-index: 1;
  font-size: 0.85rem;
  border: none;
  text-transform: none; /* Sin transformación de texto */
}

.btn-lg {
  padding: 0.6rem 1.5rem;
  font-size: 0.95rem;
}

.btn-sm {
  padding: 0.3rem 0.8rem;
  font-size: 0.75rem;
}

/* Botón principal - estilo Ameera */
.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover, .btn-primary:focus {
  background-color: #d81b60; /* Rosa más oscuro */
  color: white;
}

/* Botón secundario */
.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-secondary:hover, .btn-secondary:focus {
  background-color: #ec407a; /* Rosa más oscuro */
  color: white;
}

/* Botón de acento */
.btn-accent {
  background-color: var(--accent-color);
  color: white;
}

.btn-accent:hover, .btn-accent:focus {
  background-color: #f50057; /* Rosa más oscuro */
  color: white;
}

/* Botones outline */
.btn-outline-primary {
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
  background-color: transparent;
}

.btn-outline-primary:hover, .btn-outline-primary:focus {
  background-color: var(--primary-color);
  color: white;
}

.btn-outline-secondary {
  color: var(--secondary-color);
  border: 1px solid var(--secondary-color);
  background-color: transparent;
}

.btn-outline-secondary:hover, .btn-outline-secondary:focus {
  background-color: var(--secondary-color);
  color: white;
}

.btn-outline-accent {
  color: var(--accent-color);
  border: 1px solid var(--accent-color);
  background-color: transparent;
}

.btn-outline-accent:hover, .btn-outline-accent:focus {
  background-color: var(--accent-color);
  color: white;
}

/* Botones de acción para productos */
.product-action-btn {
  border-radius: 0;
  padding: 0.4rem 0.8rem;
  font-size: 0.8rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  transition: all 0.3s ease;
}

.product-action-btn:hover {
  background-color: #d81b60;
}

/* Botón de añadir al carrito */
.add-to-cart-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
  width: 100%;
  text-align: center;
}

.add-to-cart-btn:hover {
  background-color: #d81b60;
}

/* Botón de vista rápida */
.quick-view-btn {
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  padding: 0.4rem 0.8rem;
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.quick-view-btn:hover {
  background-color: rgba(0, 0, 0, 0.9);
}

/* Botón de favoritos */
.wishlist-btn {
  background-color: white;
  color: var(--primary-color);
  border: none;
  width: 35px;
  height: 35px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.wishlist-btn:hover {
  background-color: var(--primary-color);
  color: white;
}

/* Botón de WhatsApp */
.whatsapp-btn {
  background-color: #25D366;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.whatsapp-btn:hover {
  background-color: #128C7E;
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: none; /* Sin sombra en el foco */
}

/* Navbar styling - Estilo Ameera boutique */
.main-header {
  background-color: #ffffff;
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 1000;
  margin-bottom: 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.navbar {
  padding: 0.75rem 0;
  height: auto;
  display: flex;
  align-items: center;
}

.navbar-brand {
  font-family: 'Reem Kufi', 'Almarai', sans-serif;
  font-weight: 800;
  font-size: 1.6rem;
  color: var(--primary-color) !important;
  position: relative;
  padding: 0;
  margin-right: 2rem;
  display: flex;
  align-items: center;
}

.brand-text {
  color: var(--primary-color);
  letter-spacing: 0.5px;
}

.navbar-brand img {
  height: 80px;
  width: auto;
  margin-left: 0.75rem;
  object-fit: contain;
}

.navbar-items-container {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
}

/* تنسيق القائمة الرئيسية والثانوية */
.main-nav {
  margin-left: auto;
  display: flex;
  align-items: center;
}

.secondary-nav {
  margin-right: auto;
  display: flex;
  align-items: center;
}

.main-nav .nav-link,
.secondary-nav .nav-link {
  color: #333;
  font-weight: 600;
  padding: 0.5rem 1rem;
  transition: all 0.3s ease;
  position: relative;
}

.main-nav .nav-link:hover,
.main-nav .nav-link.active {
  color: #6a0dad;
}

.secondary-nav .nav-link:hover,
.secondary-nav .nav-link.active {
  color: #6a0dad;
}

.secondary-nav .dropdown-menu {
  min-width: 200px;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  border: none;
  padding: 10px 0;
}

.secondary-nav .dropdown-item {
  padding: 8px 20px;
  color: #333;
  transition: all 0.2s ease;
}

.secondary-nav .dropdown-item:hover,
.secondary-nav .dropdown-item:focus,
.secondary-nav .dropdown-item.active {
  background-color: rgba(106, 13, 173, 0.1);
  color: #6a0dad;
}

.secondary-nav .dropdown-divider {
  margin: 5px 0;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

/* Menú de navegación horizontal como en Ameera */
.navbar-nav {
  margin: 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center; /* Centrado como en Ameera */
  height: 100%;
  gap: 0; /* Sin espacio entre elementos como en Ameera */
}

.nav-item {
  position: relative;
  margin: 0;
  display: flex;
  align-items: center;
  height: 100%;
}

.nav-link {
  font-weight: 600;
  padding: 0.5rem 0.75rem !important; /* Más espacio horizontal */
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
  color: var(--dark-color) !important;
  display: flex;
  align-items: center;
  white-space: nowrap;
  margin: 0;
  font-size: 0.85rem;
  height: 100%;
  letter-spacing: 0.3px;
}

.nav-link:hover, .nav-link.active {
  color: var(--primary-color) !important;
}

/* Indicador de elemento activo como en Ameera (sin línea debajo) */
.nav-link.active::after {
  display: none;
}

.nav-link:hover::after {
  display: none;
}

.nav-link i {
  margin-left: 0.35rem;
  font-size: 1rem;
}

.auth-section {
  display: flex;
  align-items: center;
  margin-left: 0;
  margin-right: 0;
}

.auth-container {
  display: flex;
  align-items: center;
  gap: 15px; /* Más espacio entre elementos */
  justify-content: flex-end;
}

.user-email {
  color: var(--text-color);
  font-size: 0.75rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
  display: inline-flex;
  align-items: center;
}

.logout-form {
  margin: 0;
  display: flex;
  align-items: center;
}

/* Botón de carrito estilo Ameera */
.cart-btn {
  position: relative;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: transparent;
  color: var(--dark-color);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  border: none;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.cart-btn:hover {
  color: var(--primary-color);
}

/* Contador de carrito estilo Ameera */
.cart-count, .wishlist-count, .compare-count {
  position: absolute !important;
  top: -11px !important;
  right: -5px !important;
  background-color: var(--primary-color) !important;
  color: white !important;
  width: 20px !important;
  height: 20px !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 0.7rem !important;
  font-weight: 600 !important;
  z-index: 5 !important;
}

/* Botones de usuario estilo Ameera */
.navbar .btn {
  border-radius: 0; /* Sin bordes redondeados */
  padding: 0.4rem 1rem;
  font-weight: 600;
  letter-spacing: 0.5px;
  box-shadow: none; /* Sin sombra */
  font-size: 0.8rem;
  margin-left: 0.4rem;
  display: inline-flex;
  align-items: center;
  background-color: transparent;
  color: var(--dark-color);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.navbar .btn:hover {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* Botón de usuario estilo Ameera */
.user-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: transparent;
  color: var(--dark-color);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  border: none;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.user-btn:hover {
  color: var(--primary-color);
}

.navbar-collapse {
  transition: var(--transition);
  display: flex !important;
}

.logout-btn {
  height: 36px;
}

@media (max-width: 991.98px) {
  .navbar-collapse {
    display: flex !important;
    flex-wrap: wrap;
    justify-content: center;
  }

  .navbar-items-container {
    flex-direction: column;
    align-items: flex-start;
  }

  .navbar-nav {
    flex-direction: column;
    margin-bottom: 1rem;
    width: 100%;
    height: auto;
  }

  .nav-item {
    margin: 0.25rem 0;
    width: 100%;
    height: auto;
  }

  .nav-link {
    justify-content: flex-start;
    width: 100%;
    text-align: right;
    height: auto;
  }

  .navbar-auth {
    width: 100%;
    height: auto;
  }

  .auth-buttons {
    width: 100%;
    height: auto;
  }

  .user-info {
    flex-direction: row;
    justify-content: space-between;
    width: 100%;
    padding: 0.5rem 0;
    height: auto;
  }

  .user-email {
    max-width: 150px;
    height: auto;
  }

  .logout-form {
    height: auto;
  }

  .login-buttons {
    width: 100%;
    justify-content: space-between;
    height: auto;
  }

  .login-buttons .btn {
    flex: 1;
  }
}

/* Card styling */
.card {
  border: none;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  margin-bottom: 2rem;
  background-color: var(--card-bg);
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card:hover {
  transform: translateY(-10px);
  box-shadow: var(--box-shadow-lg);
}

.card-body {
  padding: 1.75rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.card-title {
  font-family: 'Reem Kufi', 'Almarai', sans-serif;
  font-weight: 700;
  margin-bottom: 0.8rem;
  color: var(--primary-color);
  font-size: 1.1rem;
  line-height: 1.3;
}

.card-text {
  color: var(--text-muted);
  margin-bottom: 1rem;
  line-height: 1.5;
  flex-grow: 1;
  font-size: 0.85rem;
}

.card-img-container {
  position: relative;
  overflow: hidden;
  height: 160px;
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  transition: all 0.3s ease;
}

.card-img-container .product-img-cart-form {
  position: absolute;
  bottom: 15px;
  left: 15px;
  z-index: 10;
}

.card-img-top {
  transition: transform 0.5s ease;
  height: 100%;
  width: 100%;
  object-fit: cover;
}

.card:hover .card-img-top,
.effect-zoom:hover .card-img-top,
.effect-zoom:hover .product-img {
  transform: scale(1.1);
}

.effect-fade .card-img-top,
.effect-fade .product-img {
  opacity: 0.8;
  transition: opacity 0.3s ease, transform 0.5s ease;
}

.effect-fade:hover .card-img-top,
.effect-fade:hover .product-img {
  opacity: 1;
}

.card-badge {
  position: absolute;
  top: 1rem;
  right: 1rem;
  z-index: 10;
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius-full);
  font-weight: 600;
  font-size: 0.8rem;
  letter-spacing: 0.5px;
  box-shadow: var(--box-shadow-sm);
}

.card-price {
  font-family: 'Amiri', serif;
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.8rem;
  display: flex;
  align-items: center;
}

.card-price .currency {
  font-size: 0.85rem;
  margin-right: 0.2rem;
  opacity: 0.8;
}

.card-category {
  display: inline-block;
  padding: 0.25rem 0.7rem;
  border-radius: var(--border-radius-full);
  font-size: 0.7rem;
  font-weight: 600;
  margin-bottom: 0.8rem;
  transition: var(--transition);
}

.card-category-fabric {
  background-color: rgba(139, 92, 246, 0.1);
  color: var(--category-fabric);
}

.card-category-accessories {
  background-color: rgba(236, 72, 153, 0.1);
  color: var(--category-accessories);
}

.card-category-abaya {
  background-color: rgba(99, 102, 241, 0.1);
  color: var(--category-abaya);
}

.card-category-mukhwar {
  background-color: rgba(20, 184, 166, 0.1);
  color: var(--category-mukhwar);
}

.card-footer {
  background-color: transparent;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding: 1.25rem 1.75rem;
}

.card-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-btn {
  border-radius: var(--border-radius-full);
  padding: 0.4rem 1rem;
  font-weight: 600;
  font-size: 0.75rem;
  transition: var(--transition);
}

.card-btn:hover {
  transform: translateY(-3px);
  box-shadow: var(--box-shadow-md);
}

.card-btn i {
  margin-left: 0.5rem;
}

/* Home page hero section - Completamente rediseñado */
.hero-section {
  background: linear-gradient(135deg, rgba(142, 36, 170, 0.9), rgba(171, 71, 188, 0.8)), url('/img/hero-bg.jpg');
  background-size: cover;
  background-position: center;
  color: white;
  padding: 5rem 0 4rem;
  margin-bottom: 3rem;
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: -80px;
  right: -80px;
  width: 250px;
  height: 250px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  z-index: 1;
}

.hero-section::after {
  content: '';
  position: absolute;
  bottom: -120px;
  left: -120px;
  width: 350px;
  height: 350px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
  padding: 0 1rem;
  text-align: center;
}

.hero-section h1 {
  color: white;
  font-size: 2.5rem;
  font-weight: 800;
  margin-bottom: 1.5rem;
  line-height: 1.2;
  text-shadow: 0 2px 15px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.5px;
}

.hero-section p {
  font-size: 1.1rem;
  opacity: 0.95;
  margin-bottom: 2rem;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  text-shadow: 0 1px 10px rgba(0, 0, 0, 0.2);
  line-height: 1.6;
}

.hero-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.hero-buttons .btn {
  min-width: 150px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  border-radius: 30px;
  font-weight: 700;
  letter-spacing: 0.5px;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.hero-buttons .btn:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

.hero-buttons .btn-primary {
  background-color: white;
  color: var(--primary-color);
  border: none;
}

.hero-buttons .btn-outline-light {
  border: 2px solid white;
  background-color: transparent;
}

.hero-buttons .btn-outline-light:hover {
  background-color: white;
  color: var(--primary-color);
}

@media (max-width: 767.98px) {
  .hero-section {
    padding: 4rem 0 3rem;
  }

  .hero-section h1 {
    font-size: 1.8rem;
  }

  .hero-section p {
    font-size: 0.9rem;
  }

  .hero-buttons .btn {
    min-width: 130px;
    font-size: 0.9rem;
    padding: 0.6rem 1.2rem;
  }
}

/* Carousel section */
.carousel-section {
  margin-bottom: 3rem;
  position: relative;
  padding: 0;
  overflow: hidden;
}

.carousel-container {
  width: 100%; /* Ancho completo como en Ameera */
  margin: 0 auto;
  position: relative;
  overflow: hidden;
  height: 500px;
}

.carousel.slide {
  height: 100%;
  width: 100%;
}

.carousel-item {
  height: 500px;
  position: relative;
  width: 100%;
  display: none; /* Ocultar todos los elementos por defecto */
  overflow: hidden;
}

.carousel-item.active {
  display: block !important; /* Mostrar solo el elemento activo */
  height: 100%;
}

.carousel-inner {
  overflow: hidden;
  position: relative;
  height: 100%;
  width: 100%;
}

.carousel-inner img,
.carousel-img {
  height: 500px;
  object-fit: cover;
  object-position: center;
  width: 100%;
  transition: transform 0.5s ease;
  transform-origin: center;
  max-height: 500px;
  margin: 0 auto;
  display: block;
}

/* تحسين عرض الصور داخل الروابط */
.carousel-item a {
  display: block;
  height: 100%;
  width: 100%;
  text-decoration: none;
}

/* تأثير تكبير الصورة عند التحويم */
.carousel-item:hover img {
  transform: scale(1.02);
}

/* تحسين عرض الصور على مختلف الشاشات */
@media (max-width: 992px) {
  .carousel-container {
    height: 400px;
  }

  .carousel-inner img,
  .carousel-img {
    height: 400px;
    max-height: 400px;
  }

  .carousel-item {
    height: 400px;
  }
}

@media (max-width: 768px) {
  .carousel-inner img,
  .carousel-img {
    height: 350px;
    max-height: 350px;
    object-fit: contain;
  }

  .carousel-item {
    height: 350px;
  }

  .carousel-container {
    width: 90%;
    height: 350px;
  }

  .carousel-caption {
    padding: 20px 15px 15px;
  }

  .carousel-caption h5 {
    font-size: 1.1rem;
  }

  .carousel-caption p {
    font-size: 0.9rem;
  }
}

@media (max-width: 576px) {
  .carousel-inner img,
  .carousel-img {
    height: 280px;
    max-height: 280px;
    object-fit: contain;
  }

  .carousel-item {
    height: 280px;
  }

  .carousel-container {
    width: 95%;
    height: 280px;
  }

  .carousel-control-prev,
  .carousel-control-next {
    width: 40px;
    height: 40px;
  }

  .carousel-control-prev {
    left: -20px;
  }

  .carousel-control-next {
    right: -20px;
  }

  .carousel-control-prev-icon,
  .carousel-control-next-icon {
    width: 20px;
    height: 20px;
  }
}

.carousel-item.active img {
  animation: subtle-zoom 15s infinite alternate;
}

/* تحسين التأثيرات الانتقالية */
.carousel-item {
  transition: opacity 0.6s ease-in-out;
}

.carousel-item:not(.active) {
  opacity: 0;
}

.carousel-item.active {
  opacity: 1;
  visibility: visible;
}

.carousel-item.active img {
  opacity: 1 !important;
  visibility: visible !important;
}

/* تأثير تدرج للعناوين */
.carousel-title {
  animation: fadeInUp 1s ease-out 0.5s both;
}

.carousel-description {
  animation: fadeInUp 1s ease-out 0.8s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* تحسين مظهر الكاروسيل العام */
.carousel-section {
  position: relative;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border-radius: 0;
  overflow: hidden;
}

/* تحسين أزرار التنقل */
.carousel-control-prev:focus,
.carousel-control-next:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(106, 13, 173, 0.3);
}

/* تحسين تأثير التحميل */
.carousel-img {
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
}

.carousel-img.loaded {
  opacity: 1;
}

/* تحسين مظهر الكاروسيل على الشاشات الكبيرة */
@media (min-width: 1200px) {
  .carousel-container {
    height: 403px;
  }

  .carousel-item {
    height: 600px;
  }

  .carousel-inner img,
  .carousel-img {
    height: 600px;
    max-height: 600px;
    object-fit: cover;
    object-position: center;
    display: block;
  }

  .carousel-title {
    font-size: 2.5rem;
  }

  .carousel-description {
    font-size: 1.2rem;
  }
}

/* تحسين الظلال والتأثيرات */
.carousel-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 0%, rgba(106, 13, 173, 0.05) 50%, transparent 100%);
  pointer-events: none;
  z-index: 1;
}

.carousel-inner {
  position: relative;
  z-index: 1;
}

.carousel-caption {
  background: linear-gradient(to top, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.2));
  padding: 40px 30px 30px;
  bottom: 0;
  left: 0;
  right: 0;
  opacity: 1;
  position: absolute;
  text-align: center;
  border-radius: 0;
}

.carousel-title {
  color: white;
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
  line-height: 1.2;
}

.carousel-description {
  color: rgba(255, 255, 255, 0.95);
  font-size: 1.1rem;
  margin-bottom: 0;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
  line-height: 1.4;
  max-width: 600px;
  margin: 0 auto;
}

.carousel-caption h2 {
  color: white;
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
}

/* تنسيق أزرار التنقل */
.carousel-control-prev,
.carousel-control-next {
  width: 50px;
  height: 50px;
  background-color: rgba(232, 62, 140, 0.7); /* Color rosa de Ameera */
  border-radius: 50%;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0.8;
  transition: all 0.3s ease;
}

.carousel-control-prev {
  left: 20px; /* Posicionado dentro del carrusel como en Ameera */
}

.carousel-control-next {
  right: 20px; /* Posicionado dentro del carrusel como en Ameera */
}

.carousel-control-prev:hover,
.carousel-control-next:hover {
  background-color: rgba(232, 62, 140, 1);
  opacity: 1;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
  width: 24px;
  height: 24px;
}

/* تنسيق مؤشرات الشرائح */
.carousel-indicators {
  bottom: 20px; /* Posicionado en la parte inferior como en Ameera */
}

.carousel-indicators button {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(232, 62, 140, 0.3); /* Color rosa de Ameera */
  border: none;
  margin: 0 5px;
  transition: all 0.3s ease;
}

.carousel-indicators button.active {
  background-color: rgba(232, 62, 140, 1);
  transform: scale(1.2);
}

.carousel-caption p {
  color: rgba(255, 255, 255, 0.9);
  font-size: 1rem;
  margin-bottom: 1.5rem;
  max-height: 4.5rem;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.carousel-price {
  font-size: 1.5rem;
  font-weight: 700;
  color: white;
  margin-bottom: 1.5rem;
}

/* Estos estilos se han movido arriba para evitar duplicación */

/* Responsive adjustments */
@media (max-width: 767.98px) {
  .carousel-item {
    height: 300px;
  }

  .carousel-title {
    font-size: 1.6rem;
    margin-bottom: 0.5rem;
  }

  .carousel-description {
    font-size: 0.95rem;
    line-height: 1.3;
  }

  .carousel-caption {
    padding: 25px 20px 20px;
  }

  .carousel-caption h2 {
    font-size: 1.5rem;
  }

  .carousel-caption p {
    font-size: 0.9rem;
    -webkit-line-clamp: 2;
    max-height: 3rem;
  }

  .carousel-control-prev,
  .carousel-control-next {
    width: 40px;
    height: 40px;
  }
}

@media (max-width: 480px) {
  .carousel-title {
    font-size: 1.3rem;
    margin-bottom: 0.3rem;
  }

  .carousel-description {
    font-size: 0.85rem;
    line-height: 1.2;
  }

  .carousel-caption {
    padding: 20px 15px 15px;
  }

  .carousel-indicators {
    bottom: 10px;
  }

  .carousel-indicators button {
    width: 8px;
    height: 8px;
    margin: 0 3px;
  }
}

.carousel-img-link {
  display: block;
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.carousel-img-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.view-details-text {
  color: white;
  font-size: 1.5rem;
  font-weight: 700;
  background-color: rgba(142, 36, 170, 0.8);
  padding: 0.5rem 1.5rem;
  border-radius: 30px;
  transform: translateY(20px);
  transition: transform 0.3s ease;
}

.carousel-img-link:hover .carousel-img-overlay {
  opacity: 1;
}

.carousel-img-link:hover .view-details-text {
  transform: translateY(0);
}

.carousel-item:hover .carousel-img {
  transform: scale(1.05);
}

/* Estos estilos se han movido arriba para evitar duplicación */

/* Estos estilos se han movido arriba para evitar duplicación */

/* Categories Section */
.categories-section {
  padding: 2rem 0 4rem;
  position: relative;
  background-color: var(--background-color);
  margin-bottom: 2rem;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 2rem;
  margin-top: 1rem;
}

.category-item {
  perspective: 1000px;
  height: 300px;
  margin-bottom: 1rem;
}

.category-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.8s;
  transform-style: preserve-3d;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-lg);
}

.category-item:hover .category-inner {
  transform: rotateY(180deg);
}

.category-front, .category-back {
  position: absolute;
  width: 100%;
  height: 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
}

.category-front {
  background-color: var(--card-bg);
}

.category-back {
  transform: rotateY(180deg);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: white;
  text-align: center;
}

.category-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.category-item:hover .category-img {
  transform: scale(1.05);
}

.category-content {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 1.5rem;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.9), transparent);
  text-align: right;
}

.category-name {
  color: white;
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.category-badge {
  display: inline-block;
  padding: 0.25rem 0.8rem;
  background-color: var(--accent-color);
  color: white;
  border-radius: var(--border-radius-full);
  font-size: 0.7rem;
  font-weight: 600;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.category-back-content {
  width: 100%;
}

.category-description {
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 1.5rem;
  font-size: 0.8rem;
  line-height: 1.5;
  padding: 0 0.5rem;
}

.category-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.category-btn {
  min-width: 140px;
  margin-bottom: 0.8rem;
  padding: 0.4rem 1rem;
  font-size: 0.8rem;
}

.admin-actions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
  justify-content: right;
  margin-top: 0.5rem;
}

/* Category backgrounds */
.category-back.category-abayas {
  background: linear-gradient(135deg, #6366f1, #4338ca);
}

.category-back.category-fabrics {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.category-back.category-accessories {
  background: linear-gradient(135deg, #ec4899, #db2777);
}

.category-back.category-mukhwar {
  background: linear-gradient(135deg, #14b8a6, #0d9488);
}

@media (max-width: 767.98px) {
  .categories-grid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  }

  .category-item {
    height: 220px;
  }

  .category-name {
    font-size: 1rem;
  }

  .category-description {
    font-size: 0.75rem;
    margin-bottom: 0.8rem;
  }
}

/* Featured Products Section */
.featured-products {
  padding: 4rem 0;
  background-color: var(--light-color);
  position: relative;
}

.featured-products::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: rgba(0, 0, 0, 0.05);
}

.featured-products::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: rgba(0, 0, 0, 0.05);
}

.product-slider {
  padding: 2rem 0;
}

.section-title {
  text-align: center;
  margin-bottom: 2.5rem;
  position: relative;
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--primary-color);
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 3px;
  background-color: var(--primary-color);
}

/* Testimonials Section */
.testimonials {
  padding: 7rem 0;
  position: relative;
  overflow: hidden;
}

.testimonial-card {
  background-color: var(--card-bg);
  border-radius: var(--border-radius-lg);
  padding: 2rem;
  box-shadow: var(--box-shadow);
  margin: 1rem;
  position: relative;
  transition: var(--transition);
}

.testimonial-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--box-shadow-lg);
}

.testimonial-quote {
  font-size: 4rem;
  position: absolute;
  top: -1rem;
  right: 1.5rem;
  color: var(--accent-color);
  opacity: 0.2;
}

.testimonial-text {
  font-style: italic;
  margin-bottom: 1.5rem;
  color: var(--text-color);
  line-height: 1.7;
}

.testimonial-author {
  display: flex;
  align-items: center;
}

.testimonial-author-img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 1rem;
}

.testimonial-author-name {
  font-weight: 700;
  margin-bottom: 0.25rem;
  color: var(--primary-color);
}

.testimonial-author-title {
  font-size: 0.875rem;
  color: var(--text-muted);
}

/* Call to Action Section */
.cta-section {
  background: var(--header-bg);
  color: var(--text-light);
  padding: 3rem 0;
  position: relative;
  overflow: hidden;
  text-align: center;
}

.cta-section::before {
  content: '';
  position: absolute;
  top: -50px;
  right: -50px;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
}

.cta-section::after {
  content: '';
  position: absolute;
  bottom: -100px;
  left: -100px;
  width: 300px;
  height: 300px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.05);
}

.cta-content {
  position: relative;
  z-index: 2;
  max-width: 800px;
  margin: 0 auto;
}

.cta-title {
  font-size: 2rem;
  font-weight: 800;
  margin-bottom: 1rem;
  color: var(--text-light);
}

.cta-text {
  font-size: 1.1rem;
  margin-bottom: 1.5rem;
  opacity: 0.9;
}

.cta-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

/* Footer - Completamente rediseñado */
.footer {
  width: 100%;
  background-color: #ffffff;
  color: var(--text-muted);
  padding: 3rem 0 1.5rem;
  font-size: 0.75rem;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  margin-top: 3rem;
}

.footer-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  margin-bottom: -2rem;
}

.footer-column {
  flex: 1;
  min-width: 20%;
  margin-bottom: 1.5rem;
  padding-right: 1.5rem;
}
.footer-column1 {
  flex: 1;
  min-width: 75%;
  margin-bottom: 1.5rem;
  padding-right: 1.5rem;
}

.footer-title {
  color: var(--primary-color);
  font-size: 1rem;
  font-weight: 700;
  margin-bottom: 1.25rem;
  position: relative;
  letter-spacing: 0.3px;
}

.footer-title::after {
  content: '';
  position: absolute;
  bottom: -8px;
  right: 0;
  width: 40px;
  height: 2px;
  background-color: var(--primary-color);
  opacity: 0.5;
}

.footer-links {
  list-style: none;
  padding: -1px;
  margin: 0;
  display: flex;
  flex-direction: row;
  
  gap: 0rem;
}

.footer-links li {
  margin-bottom: 0.75rem;
  width: 100%;
}

.footer-links a {
  color: var(--text-muted);
  text-decoration: none;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  font-size: 0.8rem;
}

.footer-links a:hover {
  color: var(--primary-color);
  transform: translateX(-5px);
}

.footer-social {
  display: flex;
  gap: 1rem;
  margin-top: 1.5rem;
}

.footer-social a {
  display: flex;
  align-items: center;
  justify-content: center;
  /*width: 36px;
  height: 36px;*/
  border-radius: 50%;
  background-color: rgba(142, 36, 170, 0.08);
  color: var(--primary-color);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  text-decoration: none;
}

.footer-social a i {
  font-size: 1rem;
}

.footer-social a:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-5px);
}

.footer-bottom {
  text-align: center;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  color: var(--text-muted);
  font-size: 0.75rem;
}

.footer-bottom a {
  color: var(--primary-color);
  text-decoration: none;
  transition: all 0.3s ease;
  font-weight: 600;
}

.footer-bottom a:hover {
  text-decoration: underline;
}

/* Category Filter */
.category-filter {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: center;
  margin-bottom: 2rem;
}

.category-filter-item {
  display: inline-block;
  padding: 0.5rem 1.25rem;
  border-radius: var(--border-radius-full);
  background-color: rgba(255, 255, 255, 0.2);
  color: var(--text-light);
  font-weight: 600;
  transition: var(--transition);
  text-decoration: none;
}

.category-filter-item:hover {
  background-color: rgba(255, 255, 255, 0.3);
  color: white;
  transform: translateY(-3px);
}

.category-filter-item.active {
  background-color: var(--accent-color);
  color: white;
  box-shadow: var(--box-shadow-sm);
}

/* Form controls */
.form-control {
  border-radius: var(--border-radius);
  padding: 0.75rem 1rem;
  border: 1px solid #ddd;
  transition: var(--transition);
}

.form-control:focus {
  border-color: var(--primary-color);
}

.form-label {
  font-weight: 500;
  margin-bottom: 0.5rem;
}

/* Auth pages */
.auth-container {
  max-width: 500px;
  margin: 2rem auto;
  padding: 10px;
  background-color:rgb(127, 155, 236) ;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.auth-container h2 {
  text-align: center;
  margin-bottom: 2rem;
  color: var(--primary-color);
}

/* Validation */
.validation-summary-errors {
  color: var(--danger-color);
  background-color: rgba(231, 76, 60, 0.1);
  padding: 1rem;
  border-radius: var(--border-radius);
  margin-bottom: 1rem;
}

/* Error Page Styling */
.error-page {
  min-height: 70vh;
  display: flex;
  align-items: center;
  padding: 3rem 0;
  background-color: var(--light-color);
}

.error-container {
  background-color: var(--card-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-lg);
  padding: 3rem;
  overflow: hidden;
  border: 1px solid rgba(142, 36, 170, 0.1);
}

.error-icon {
  font-size: 3.5rem;
  color: var(--primary-color);
  margin-bottom: 1.5rem;
  animation: pulse 2s infinite;
}

.error-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 1rem;
  font-family: 'Almarai', sans-serif;
}

.error-message {
  font-size: 1.2rem;
  color: var(--dark-color);
  margin-bottom: 1.5rem;
  line-height: 1.6;
  font-family: 'Tajawal', sans-serif;
}

.error-details {
  background-color: rgba(142, 36, 170, 0.05);
  padding: 1rem;
  border-radius: var(--border-radius);
  margin-bottom: 1.5rem;
  font-family: monospace;
  border-right: 3px solid var(--primary-color);
}

.error-details code {
  color: var(--primary-color);
  font-weight: bold;
  font-size: 1.1rem;
}

.error-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  flex-wrap: wrap;
}

.error-btn {
  padding: 0.75rem 1.5rem;
  border-radius: var(--border-radius-full);
  font-weight: 600;
  transition: var(--transition);
  font-family: 'Tajawal', sans-serif;
}

.error-btn:hover {
  transform: translateY(-5px);
  box-shadow: var(--box-shadow-md);
}

.error-support {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid rgba(142, 36, 170, 0.1);
}

.error-support h5 {
  color: var(--primary-color);
  font-weight: 600;
  margin-bottom: 0.75rem;
  font-family: 'Almarai', sans-serif;
}

.error-support a {
  color: var(--accent-color);
  font-weight: 600;
  text-decoration: none;
  transition: var(--transition);
}

.error-support a:hover {
  color: var(--primary-color);
  text-decoration: underline;
}

.error-image {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.error-image img {
  max-width: 100%;
  max-height: 400px;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

.slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
  0% {
    transform: translateX(-30px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@media (max-width: 991.98px) {
  .error-container {
    padding: 2rem;
  }

  .error-image {
    margin-top: 2rem;
  }

  .error-title {
    font-size: 2rem;
  }

  .error-icon {
    font-size: 3rem;
  }
}

.validation-summary-errors ul {
  margin-bottom: 0;
  padding-right: 1rem;
}

.field-validation-error {
  color: var(--danger-color);
  font-size: 0.875rem;
  display: block;
  margin-top: 0.25rem;
}

/* Products Page Styles */
.products-header {
  background-color: #f5f0f8; /* Fondo lila muy claro */
  padding: 3rem 0;
  margin-bottom: 2.5rem;
  color: var(--primary-color);
  position: relative;
  overflow: hidden;
  border-bottom: 1px solid rgba(142, 36, 170, 0.08);
}

.products-header::before {
  content: '';
  position: absolute;
  top: -50px;
  right: -50px;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: rgba(142, 36, 170, 0.03);
  z-index: 1;
}

.products-header::after {
  content: '';
  position: absolute;
  bottom: -80px;
  left: -80px;
  width: 250px;
  height: 250px;
  border-radius: 50%;
  background: rgba(142, 36, 170, 0.03);
  z-index: 1;
}

.products-header h1 {
  font-size: 2.2rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: var(--primary-color);
  position: relative;
  z-index: 2;
}

.products-header .lead {
  color: var(--text-muted);
  font-size: 1rem;
  max-width: 600px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 20px;
  margin-top: 2rem;
}

/* تنسيقات متجاوبة للشبكة */
@media (max-width: 576px) {
  .product-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 15px;
  }

  /* إجبار الشبكة على عرض عمودين في الهواتف */
  .product-grid.grid-6,
  .product-grid.items-per-row-6,
  .product-grid.grid-4,
  .product-grid.items-per-row-4 {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 10px !important;
  }
}

@media (min-width: 577px) and (max-width: 768px) {
  .product-grid {
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 18px;
  }

  .product-grid.grid-6,
  .product-grid.items-per-row-6 {
    grid-template-columns: repeat(3, 1fr) !important;
  }
}

@media (min-width: 769px) and (max-width: 992px) {
  .product-grid {
    grid-template-columns: repeat(4, 1fr) !important;
  }

  .product-grid.grid-6,
  .product-grid.items-per-row-6 {
    grid-template-columns: repeat(4, 1fr) !important;
  }
}

/* تأثيرات حركية لعداد السلة */
.cart-count {
    transition: all 0.3s ease;
    display: inline-block;
}

.cart-count.bounce {
    animation: cartBounce 0.6s ease-in-out;
}

.cart-count.pulse {
    animation: cartPulse 1s ease-in-out;
}

.cart-count.badge-highlight {
    background-color: #28a745 !important;
    transform: scale(1.1);
}

@keyframes cartBounce {
    0%, 20%, 60%, 100% {
        transform: translateY(0) scale(1);
    }
    40% {
        transform: translateY(-10px) scale(1.1);
    }
    80% {
        transform: translateY(-5px) scale(1.05);
    }
}

@keyframes cartPulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

/* Product Card Styling - Estilo Ameera boutique */
.product-card {
  background-color: var(--card-bg);
  border-radius: 0; /* Sin bordes redondeados como en Ameera */
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  height: 100%;
  position: relative;
  overflow: hidden;
  border: none;
  display: flex;
  flex-direction: column;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.product-img-container {
  position: relative;
  overflow: hidden;
  height: 200px;
  background-color: #f9f9f9;
}

/* Botones de acción flotantes */
.product-quick-actions {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  flex-direction: column;
  gap: 5px;
  z-index: 3;
  opacity: 0;
  transform: translateX(10px);
  transition: all 0.3s ease;
}

.product-card:hover .product-quick-actions {
  opacity: 1;
  transform: translateX(0);
}

.quick-action-btn {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background-color: white;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
}

.quick-action-btn:hover {
  background-color: var(--primary-color);
  color: white;
}

/* Botón de añadir al carrito */
.product-img-cart-form {
  position: absolute;
  bottom: 15px;
  left: 15px;
  z-index: 10;
  opacity: 0;
  transition: all 0.3s ease;
  transform: translateY(10px);
}

.product-card:hover .product-img-cart-form {
  opacity: 1;
  transform: translateY(0);
}

.product-img-cart-btn {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  background-color: white;
  color: var(--primary-color);
  border: none;
  font-size: 1rem;
}

.product-img-cart-btn:hover {
  background-color: var(--primary-color);
  color: white;
}

/* Botón de vista rápida */
.quick-view-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 10px;
  text-align: center;
  transform: translateY(100%);
  transition: all 0.3s ease;
}

.product-card:hover .quick-view-overlay {
  transform: translateY(0);
}

.product-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: transform 0.5s ease;
  padding: 5px;
}

.product-card:hover .product-img {
  transform: scale(1.05);
}

.product-badge {
  position: absolute;
  top: 10px;
  left: 10px; /* A la izquierda como en Ameera */
  padding: 0.25rem 0.75rem;
  font-size: 0.8rem;
  font-weight: 600;
  z-index: 2;
}

.product-badge.bg-success {
  background-color: var(--available-color);
  color: var(--available-text);
}

.product-badge.bg-danger {
  background-color: rgba(244, 67, 54, 0.1);
  color: var(--danger-color);
}

.product-content {
  padding: 1rem;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  text-align: center; /* Centrado como en Ameera */
}

.product-category {
  display: inline-block;
  padding: 0.2rem 0.6rem;
  border-radius: 30px;
  font-size: 0.7rem;
  font-weight: 600;
  background-color: rgba(232, 62, 140, 0.1);
  color: var(--primary-color);
  margin: 0 auto 0.5rem;
}

.product-title {
  font-weight: 600;
  font-size: 1rem;
  margin-bottom: 0.5rem;
  color: var(--text-dark);
  height: 2.4rem;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-description {
  color: var(--text-muted);
  margin-bottom: 0.75rem;
  flex-grow: 1;
  font-size: 0.8rem;
  height: 2.4rem;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-price {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center; /* Centrado como en Ameera */
}

.product-price .currency {
  font-size: 0.8rem;
  margin-right: 0.25rem; /* A la derecha en RTL */
  font-weight: 600;
}

.product-actions {
  display: flex;
  justify-content: center; /* Centrado como en Ameera */
  align-items: center;
  margin-top: auto;
  gap: 0.5rem;
}

.product-btn {
  padding: 0.5rem 1rem;
  border-radius: 0; /* Sin bordes redondeados */
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  background-color: var(--primary-color);
  color: white;
  border: none;
}

.product-btn:hover {
  background-color: #d81b60; /* Rosa más oscuro */
}

.product-btn i {
  margin-left: 0.35rem;
}

/* Etiqueta de descuento */
.discount-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: var(--primary-color);
  color: white;
  padding: 0.25rem 0.5rem;
  font-size: 0.8rem;
  font-weight: 600;
  z-index: 2;
}

/* Etiqueta de agotado */
.sold-out-badge {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-15deg);
  background-color: rgba(244, 67, 54, 0.8);
  color: white;
  padding: 0.5rem 1rem;
  font-size: 1rem;
  font-weight: 700;
  z-index: 2;
  width: 80%;
  text-align: center;
}

/* Product Details Page */
.product-details {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  margin-bottom: 3rem;
}

.product-gallery {
  background-color: #f8f9fa;
  border-radius: var(--border-radius) 0 0 var(--border-radius);
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-main-image {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
}

.zoom-effect {
  transition: transform 0.5s ease;
}

.zoom-effect:hover {
  transform: scale(1.05);
}

.product-details-img {
  height: 500px;
  width: 100%;
  object-fit: contain;
  transition: transform 0.5s ease;
}

.product-details-content {
  padding: 2rem;
}

.product-details-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.product-details-price {
  font-family: 'Amiri', serif;
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 1.5rem;
}

.product-details-description {
  color: var(--text-muted);
  margin-bottom: 2rem;
  line-height: 1.8;
}

/* تنسيق أزرار المقارنة والمفضلة في صفحة تفاصيل المنتج */
.product-action-buttons {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

/* تصميم موحد لجميع أزرار التفاصيل مماثل لزر السلة */
.product-details-btn {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  font-size: 16px;
  transition: all 0.3s ease;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  min-height: 10px;
}

.product-action-buttons .product-details-btn {
  flex: 1;
}

/* زر بنفسجي للمفضلة */
.btn-purple {
  background-color: #6a0dad;
  border-color: #6a0dad;
  color: white;
}

.btn-purple:hover {
  background-color: #5a0b9a;
  border-color: #5a0b9a;
  color: white;
}

.btn-purple:focus,
.btn-purple.focus {
  box-shadow: 0 0 0 0.2rem rgba(106, 13, 173, 0.5);
}

.btn-purple:active,
.btn-purple.active {
  background-color: #4a0a87;
  border-color: #4a0a87;
}

/* زر أزرق للمقارنة */
.btn-info {
  background-color: #17a2b8;
  border-color: #17a2b8;
  color: white;
}

.btn-info:hover {
  background-color: #138496;
  border-color: #117a8b;
  color: white;
}

.btn-info:focus,
.btn-info.focus {
  box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.5);
}

.btn-info:active,
.btn-info.active {
  background-color: #117a8b;
  border-color: #10707f;
}

/* تأثيرات خاصة للأزرار النشطة */
.product-action-buttons .add-to-wishlist-btn.active {
  background-color: #d1ced7  !important;
  border-color: #4a0a87 !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(106, 13, 173, 0.3);
}

.product-action-buttons .add-to-compare-btn.active {
  background-color: #117a8b !important;
  border-color: #117a8b !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(23, 162, 184, 0.3);
}

/* تأثيرات hover إضافية */
.product-action-buttons .product-details-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* تأثيرات الحركة للأزرار */
.product-action-buttons .product-details-btn.adding {
  animation: pulse 0.6s infinite;
}

.product-action-buttons .product-details-btn.added {
  animation: bounce 0.5s;
}

.product-action-buttons .product-details-btn.removed {
  animation: shake 0.5s;
}

/* تحسين أيقونات الأزرار */
.product-action-buttons .product-details-btn i {
  font-size: 16px;
  line-height: 1;
}

/* تأثيرات خاصة للأيقونات */
.add-to-wishlist-btn .bi-heart-fill {
  display: none;
}

.add-to-wishlist-btn.active .bi-heart {
  display: none !important;
}

.add-to-wishlist-btn.active .bi-heart-fill {
  display: inline-block !important;
}

/* تأثيرات أزرار المقارنة */
.add-to-compare-btn.adding {
  animation: pulse 0.6s infinite;
}

.add-to-compare-btn.removing {
  animation: shake 0.5s;
}

/* رسائل Toast للمقارنة */
.toast-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  border-radius: 8px;
  padding: 15px 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 9999;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  border-left: 4px solid #17a2b8;
  max-width: 300px;
}

.toast-notification.show {
  transform: translateX(0);
}

.toast-notification.toast-success {
  border-left-color: #28a745;
}

.toast-notification.toast-error {
  border-left-color: #dc3545;
}

.toast-content {
  display: flex;
  align-items: center;
  gap: 10px;
}

.toast-content i {
  font-size: 18px;
}

.toast-success .toast-content i {
  color: #28a745;
}

.toast-error .toast-content i {
  color: #dc3545;
}

/* تأثير تحديث العداد */
.compare-count.updated {
  animation: bounce 0.5s;
}





/* تحسين المظهر العام للأزرار */
.product-action-buttons .product-item-action-btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(106, 13, 173, 0.2);
}

.product-action-buttons .add-to-compare-btn:focus {
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.2);
}

/* تحسين النصوص داخل الأزرار */
.product-action-buttons .product-item-action-btn span {
  font-weight: 500;
  margin-right: 0.25rem;
}

/* تحسين المسافات بين العناصر */
.product-action-buttons .product-item-action-btn i + span {
  margin-right: 0.5rem;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {transform: translateY(0);}
  40% {transform: translateY(-10px);}
  60% {transform: translateY(-5px);}
}

@keyframes shake {
  0%, 100% {transform: translateX(0);}
  10%, 30%, 50%, 70%, 90% {transform: translateX(-5px);}
  20%, 40%, 60%, 80% {transform: translateX(5px);}
}

.product-details-meta {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
}

.product-details-meta-item {
  padding: 0.5rem 1rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 0.9rem;
}

.product-details-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

.product-details-btn {
  padding: 0.5rem 1.4rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1rem;
  transition: var(--transition);
}

.product-details-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(106, 13, 173, 0.2);
}

.product-details-btn i {
  margin-left: 0.5rem;
}

.admin-actions {
  background-color: rgba(106, 13, 173, 0.1);
  border-radius: var(--border-radius);
  padding: 1rem;
  border: 1px solid rgba(106, 13, 173, 0.2);
}

.admin-section-title {
  color: var(--primary-color);
  font-size: 1rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  position: relative;
  padding-right: 1rem;
}

.admin-section-title::before {
  content: '';
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background-color: var(--primary-color);
  border-radius: 2px;
}

/* Form Styling for Product Management */
.form-card {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: 2rem;
  margin-bottom: 3rem;
}

.form-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 2rem;
  position: relative;
  padding-bottom: 0.75rem;
}

.form-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
  background: var(--accent-color);
  border-radius: 10px;
}

/* Order Details Modal Styling */
.modal-body.p-0 {
  padding: 0 !important;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 1.5rem;
}

.order-products-table {
  margin-bottom: 0;
}

.order-products-table th {
  background-color: rgba(30, 58, 138, 0.05);
  font-weight: 600;
}

.order-products-table .total-amount {
  font-size: 1.1rem;
  color: var(--primary-color);
}

.info-item {
  margin-bottom: 1rem;
}

.info-label {
  font-weight: 600;
  color: var(--text-muted);
  font-size: 0.9rem;
}

.info-value {
  font-weight: 500;
  color: var(--text-dark);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-control {
  border-radius: 50px;
  padding: 0.75rem 1.25rem;
  border: 1px solid rgba(106, 13, 173, 0.2);
  transition: var(--transition);
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(106, 13, 173, 0.15);
}

textarea.form-control {
  border-radius: var(--border-radius);
  min-height: 120px;
}

.form-label {
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: var(--text-color);
}

.form-check-label {
  font-weight: 500;
  color: var(--text-color);
}

.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 2rem;
}

.form-btn {
  padding: 0.75rem 2rem;
  border-radius: 50px;
  font-weight: 600;
  font-size: 1rem;
  transition: var(--transition);
}

.form-btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(106, 13, 173, 0.2);
}

.form-btn i {
  margin-left: 0.5rem;
}

/* Custom animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.fade-in {
  animation: fadeIn 0.5s ease forwards;
}

@keyframes fadeInDown {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

.fade-in-down {
  animation: fadeInDown 0.5s ease forwards;
}

@keyframes slideInRight {
  from { opacity: 0; transform: translateX(50px); }
  to { opacity: 1; transform: translateX(0); }
}

.slide-in-right {
  animation: slideInRight 0.5s ease forwards;
}

@keyframes slideInLeft {
  from { opacity: 0; transform: translateX(-50px); }
  to { opacity: 1; transform: translateX(0); }
}

.slide-in-left {
  animation: slideInLeft 0.5s ease forwards;
}

@keyframes zoomIn {
  from { opacity: 0; transform: scale(0.8); }
  to { opacity: 1; transform: scale(1); }
}

.zoom-in {
  animation: zoomIn 0.5s ease forwards;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.pulse {
  animation: pulse 2s infinite;
}

/* Animación para el contador del carrito */
@keyframes cartPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.3); }
  100% { transform: scale(1); }
}

.cart-count.pulse {
  animation: cartPulse 0.5s ease-in-out;
}

/* Feature list */
.feature-list {
  list-style: none;
  padding: 0;
  margin: 0 0 2rem;
  text-align: right;
}

.cart-btn {
  min-width: 180px;
  text-align: center;
  justify-content: center;
}

.feature-list li {
  margin-bottom: 1.25rem;
  padding-right: 2.5rem;
  position: relative;
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.1rem;
}

.feature-list li i {
  position: absolute;
  right: 0;
  top: 0.25rem;
  color: var(--accent-color);
  font-size: 1.25rem;
}

/* تنسيق أيقونات المنتج أسفل الصورة */
.product-item-actions {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 8px;
    width: auto;
    z-index: 10;
    background-color: rgba(255, 255, 255, 0.95);
    padding: 6px 12px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
}

/* تصميم موحد وجميل لأزرار المفضلة والمقارنة */
.product-item-action-btn {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: 2px solid #e0e0e0;
    background-color: white;
    color: #666;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    flex-shrink: 0;
    margin: 0;
    position: relative;
    overflow: hidden;
}

/* ضمان عدم تداخل الأيقونات */
.product-item-action-btn:not(:last-child) {
    margin-left: 0;
}

.product-item-action-btn + .product-item-action-btn {
    margin-right: 0;
}

/* تأثيرات hover موحدة لجميع الأزرار */
.product-item-action-btn:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    border-color: #6a0dad;
    color: #6a0dad;
}

/* تأثيرات خاصة لزر المفضلة */
.add-to-wishlist-btn:hover {
    background-color: #f8f0ff;
    border-color: #6a0dad;
    color: #6a0dad;
}

.add-to-wishlist-btn.active {
    background-color: #6a0dad;
    border-color: #6a0dad;
    color: white;
    transform: scale(1.02);
}

.add-to-wishlist-btn.active .bi-heart-fill {
    display: inline-block !important;
    color: white;
}

.add-to-wishlist-btn.active .bi-heart {
    display: none !important;
}

/* تأثيرات خاصة لزر المقارنة */
.add-to-compare-btn:hover {
    background-color: #f0f8ff;
    border-color: #007bff;
    color: #007bff;
}

.add-to-compare-btn.active {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
    transform: scale(1.02);
}

.product-item-action-btn.adding {
    animation: pulse 0.6s infinite;
}

.product-item-action-btn.added {
    animation: bounce 0.5s;
}

/* إخفاء الأيقونات المملوءة بشكل افتراضي */
.add-to-wishlist-btn .bi-heart-fill {
    display: none;
}

/* تحسينات إضافية للأزرار */
.product-item-action-btn {
    position: relative;
    overflow: hidden;
}

.product-item-action-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.product-item-action-btn:hover::before {
    width: 100%;
    height: 100%;
}

/* تأثيرات خاصة للأزرار النشطة */
.product-item-action-btn.active::after {
    content: '';
    position: absolute;
    top: 2px;
    right: 2px;
    width: 6px;
    height: 6px;
    background: #fff;
    border-radius: 50%;
    opacity: 0.8;
}

/* التأكد من محاذاة جميع الأيقونات */
.product-item-action-btn i {
    line-height: 1;
    vertical-align: middle;
}

/* إزالة أي margin أو padding إضافي */
.product-item-action-btn * {
    margin: 0;
    padding: 0;
}

/* تأثيرات خاصة لزر المقارنة */
.add-to-compare-btn.active {
    background-color: #6a0dad !important;
    color: white !important;
    transform: scale(1.05) !important;
}

.add-to-compare-btn:hover {
    background-color: #6a0dad !important;
    color: white !important;
}

/* تأثيرات خاصة لزر السلة */
.add-to-cart-btn:hover {
    background-color: #6a0dad !important;
    color: white !important;
}

/* تأثيرات الحركة */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }
    40% { transform: translateY(-8px) scale(1.1); }
    60% { transform: translateY(-4px) scale(1.05); }
}

/* تأثير ظهور الأيقونات عند التمرير على المنتج */
.product-item:hover .product-item-actions {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
}

.product-item-actions {
    opacity: 0.9;
    transition: all 0.3s ease;
}

/* تحسين الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .product-item-action-btn {
        width: 30px;
        height: 30px;
        font-size: 13px;
        border-width: 1.5px;
    }

    .product-item-action-btn:hover {
        transform: translateY(-1px) scale(1.03);
    }

    .product-item-actions {
        gap: 6px;
        padding: 6px;
        border-radius: 18px;
        bottom: 8px;
    }

    /* تحسين أزرار صفحة التفاصيل للشاشات الصغيرة */
    .product-action-buttons {
        flex-direction: row;
        gap: 0.75rem;
    }

    .product-action-buttons .product-details-btn {
        padding: 0.6rem 1rem;
        font-size: 14px;
        min-height: 44px;
    }

    .product-action-buttons .product-details-btn i {
        font-size: 14px;
    }
}

/* تحسين للشاشات المتوسطة */
@media (min-width: 769px) and (max-width: 1024px) {
    .product-item-action-btn {
        width: 30px;
        height: 30px;
        font-size: 13px;
    }

    .product-action-buttons .product-details-btn {
        padding: 0.7rem 1.2rem;
        font-size: 15px;
        min-height: 46px;
    }
}
