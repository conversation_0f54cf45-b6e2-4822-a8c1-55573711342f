/* Estilos para la visualización de productos según el nuevo diseño */

/* Contenedor de la cuadrícula de productos */
.products-grid, .product-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-top: 20px;
}

/* أنماط العرض المختلفة للشبكة */
.product-grid.grid-2 {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 25px !important;
}

.product-grid.grid-3 {
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 20px !important;
}

.product-grid.grid-4 {
    grid-template-columns: repeat(4, 1fr) !important;
    gap: 18px !important;
}

.product-grid.grid-6 {
    grid-template-columns: repeat(6, 1fr) !important;
    gap: 15px !important;
}

/* أنماط أزرار العرض */
.view-mode {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
}

.view-mode-option {
    width: 40px;
    height: 40px;
    border: 2px solid #ddd;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    color: #666;
}

.view-mode-option:hover {
    border-color: #6a0dad;
    color: #6a0dad;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(106, 13, 173, 0.15);
}

.view-mode-option.active {
    border-color: #6a0dad;
    background: #6a0dad;
    color: white;
    box-shadow: 0 4px 12px rgba(106, 13, 173, 0.25);
}

.view-mode-option i {
    font-size: 16px;
}

/* تنسيقات متجاوبة للشبكة */
/* أزرار العرض للشاشات المختلفة */
@media (max-width: 576px) {
    .products-grid.grid-2, .product-grid.grid-2 {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 15px;
    }

    .products-grid.grid-3, .product-grid.grid-3 {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 15px;
    }

    .products-grid.grid-4, .product-grid.grid-4 {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 15px;
    }
}

@media (min-width: 577px) and (max-width: 768px) {
    .products-grid.grid-2, .product-grid.grid-2 {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 18px;
    }

    .products-grid.grid-3, .product-grid.grid-3 {
        grid-template-columns: repeat(3, 1fr) !important;
        gap: 18px;
    }

    .products-grid.grid-4, .product-grid.grid-4 {
        grid-template-columns: repeat(3, 1fr) !important;
        gap: 18px;
    }
}

@media (min-width: 769px) and (max-width: 992px) {
    .products-grid.grid-2, .product-grid.grid-2 {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 20px;
    }

    .products-grid.grid-3, .product-grid.grid-3 {
        grid-template-columns: repeat(3, 1fr) !important;
        gap: 20px;
    }

    .products-grid.grid-4, .product-grid.grid-4 {
        grid-template-columns: repeat(4, 1fr) !important;
        gap: 20px;
    }
}

/* للشاشات الكبيرة */
@media (min-width: 993px) {
    .products-grid.grid-2, .product-grid.grid-2 {
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 25px;
    }

    .products-grid.grid-3, .product-grid.grid-3 {
        grid-template-columns: repeat(3, 1fr) !important;
        gap: 22px;
    }

    .products-grid.grid-4, .product-grid.grid-4 {
        grid-template-columns: repeat(4, 1fr) !important;
        gap: 20px;
    }
}

/* Tarjeta de producto */
.product-item {
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
    transition: transform 0.3s ease;
    margin-bottom: 20px;
}

.product-item:hover {
    transform: translateY(-5px);
}

/* Contenedor de imagen */
.product-item-img-container {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    width: 100%;
    height: 0;
    padding-bottom: 100%; /* Relación de aspecto cuadrada */
    cursor: pointer;
    transition: all 0.3s ease;
}

.product-item-img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
    cursor: pointer;
}

.product-item:hover .product-item-img {
    transform: scale(1.05);
}

/* تأثير النقر على الصورة */
.product-item-img-container:hover {
    box-shadow: 0 4px 15px rgba(106, 13, 173, 0.2);
}

.product-item-img-container:active {
    transform: scale(0.98);
}

/* أزرار الإجراءات التي تظهر عند تمرير المؤشر */
.product-item-actions {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 20px;
    width: 100%;
    z-index: 20;
    padding: 6px 12px;
    opacity: 0;
    transition: all 0.3s ease;
    pointer-events: none;
}

/* إصلاح مشكلة الرابط الممتد */
.product-link {
    z-index: 5;
    pointer-events: auto;
}

/* تعديل الرابط الممتد لتجنب تداخله مع الأزرار */
.product-item .stretched-link::after {
    z-index: 10;
    pointer-events: none;
}

.product-item:hover .product-item-actions {
    opacity: 1;
    transform: translateX(-50%);
    pointer-events: auto;
}

.product-item-action-btn {
    width: 25px !important;
    height: 25px !important;
    border-radius: 50%;
    border: none;
    background-color: white;
    color: #6a0dad;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease, transform 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
    margin: 0;
    transform: translateY(5px);
}

.product-item:hover .product-item-action-btn {
    transform: translateY(0);
}

.product-item-action-btn:hover {
    background-color: rgba(255, 255, 255, 0.98);
    color: #6a0dad;
    border-color: #6a0dad;
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 15px rgba(106, 13, 173, 0.2);
}

.product-item-action-btn.active {
    background-color: rgba(255, 255, 255, 0.95);
    color: #6a0dad;
    border-color: rgba(106, 13, 173, 0.3);
    box-shadow: 0 3px 12px rgba(106, 13, 173, 0.25);
}

/* ترتيب أفضل للأزرار */
.product-item-action-btn + .product-item-action-btn {
    margin-right: 0;
}

/* تأثيرات إضافية للأزرار */
.product-item-action-btn i {
    font-size: 18px;
    transition: transform 0.2s ease;
}

.product-item-action-btn:hover i {
    transform: scale(1.1);
}

/* زر التعديل للمدير */
.product-item-action-btn.edit-product-btn {
    background-color: #f8f9fa;
    border-color: #6c757d;
    color: #6c757d;
}

.product-item-action-btn.edit-product-btn:hover {
    background-color: #6c757d;
    color: white;
    border-color: #6c757d;
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

/* إعادة ضبط زر المفضلة ليتماشى مع باقي الأزرار */
.product-item-actions .add-to-wishlist-btn,
.product-item .product-item-actions .add-to-wishlist-btn,
button.product-item-action-btn.add-to-wishlist-btn {
    width: 28px !important;
    height: 28px !important;
    border-radius: 50% !important;
    border: none !important;
    background-color: white !important;
    color: #666 !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    font-size: 12px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
    flex-shrink: 0 !important;
    margin: 0 !important;
    position: static !important;
}

.product-item-actions .add-to-wishlist-btn:hover,
.product-item .product-item-actions .add-to-wishlist-btn:hover,
button.product-item-action-btn.add-to-wishlist-btn:hover {
    background-color: rgba(255, 255, 255, 0.98) !important;
    color: #6a0dad !important;
    border-color: #6a0dad !important;
    transform: translateY(-2px) scale(1.05) !important;
    box-shadow: 0 4px 15px rgba(106, 13, 173, 0.2) !important;
    width: 28px !important;
    height: 28px !important;
}

.product-item-actions .add-to-wishlist-btn.active,
.product-item .product-item-actions .add-to-wishlist-btn.active,
button.product-item-action-btn.add-to-wishlist-btn.active {
    background-color: rgba(255, 255, 255, 0.95) !important;
    color: #6a0dad !important;
    border-color: rgba(106, 13, 173, 0.3) !important;
    box-shadow: 0 3px 12px rgba(106, 13, 173, 0.25) !important;
    width: 28px !important;
    height: 28px !important;
}

/* تنسيق أيقونات زر المفضلة */
.product-item-actions .add-to-wishlist-btn i,
.product-item .product-item-actions .add-to-wishlist-btn i,
button.product-item-action-btn.add-to-wishlist-btn i {
    font-size: 14px !important;
    transition: transform 0.2s ease !important;
}

.product-item-actions .add-to-wishlist-btn:hover i,
.product-item .product-item-actions .add-to-wishlist-btn:hover i,
button.product-item-action-btn.add-to-wishlist-btn:hover i {
    transform: scale(1.1) !important;
}

/* تحسين ظهور الأزرار على الهواتف */
@media (max-width: 768px) {
    .product-item-actions {
        gap: 15px;
        padding: 0 10px;
    }

    .product-item-action-btn {
        width: 28px;
        height: 28px;
        font-size: 14px;
    }

    .product-item-action-btn i {
        font-size: 16px;
    }
}

/* Etiqueta de descuento - دائرة وردية في أعلى اليمين */
.product-item-discount {
    position: absolute;
    top: 8px;
    right: 8px;
    background-color: #ffd1dc; /* لون وردي أفتح */
    color: #666; /* لون نص أفتح */
    padding: 6px; /* تقليل المسافة الداخلية */
    border-radius: 50%; /* دائرة */
    font-size: 12px; /* تقليل حجم الخط */
    font-weight: 500; /* وزن خط أقل */
    z-index: 2;
    width: 25px; /* تقليل العرض */
    height: 25px; /* تقليل الارتفاع */
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05); /* ظل أخف */
    transition: transform 0.3s ease;
}

/* تأثير حركي عند تمرير المؤشر */
.product-item:hover .product-item-discount {
    transform: scale(1.1);
}

/* Etiqueta de disponibilidad - نقل إلى اليسار */
.product-item-availability {
    position: absolute;
    top: 10px;
    left: 10px; /* تغيير من right إلى left */
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    z-index: 2;
}

.product-item-availability.available {
    background-color: rgba(106, 13, 173, 0.1);
    color: #6a0dad;
}

.product-item-availability.not-available {
    background-color: rgba(255, 0, 0, 0.1);
    color: #ff0000;
}

/* Información del producto */
.product-item-info {
    padding: 10px 0;
    text-align: center;
}

.product-item-title {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 5px;
    color: #333;
    text-align: center;
}

.product-item-price {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
}

.product-item-price-current {
    font-size: 16px;
    font-weight: bold;
    color: #6a0dad;
}

.product-item-price-original {
    font-size: 14px;
    color: #999;
    text-decoration: line-through;
}

/* Título de sección */
.section-title {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 20px;
    text-align: center;
    color: #333;
    position: relative;
    padding-bottom: 10px;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background-color: #6a0dad;
}

/* Responsive - CSS عام فقط للشاشات الكبيرة بدون أزرار عرض */
@media (min-width: 993px) {
    .products-grid:not(.grid-2):not(.grid-3):not(.grid-4) {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (max-width: 1200px) and (min-width: 993px) {
    .products-grid:not(.grid-2):not(.grid-3):not(.grid-4) {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* زر تحديد العرض الصغير للهواتف */
.mobile-display-toggle {
    margin-bottom: 20px;
    text-align: left;
}

.mobile-display-btn {
    width: 45px;
    height: 45px;
    border-radius: 12px;
    border: 2px solid #6a0dad;
    background: white;
    color: #6a0dad;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.mobile-display-btn:hover {
    background: #6a0dad;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(106, 13, 173, 0.25);
}

/* النافذة الجانبية للهواتف */
.mobile-sidebar-overlay {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: rgba(0, 0, 0, 0.5) !important;
    z-index: 999999 !important;
    opacity: 0 !important;
    visibility: hidden !important;
    transition: all 0.3s ease !important;
    pointer-events: none !important;
}

.mobile-sidebar-overlay.active {
    opacity: 1 !important;
    visibility: visible !important;
    pointer-events: auto !important;
}

.mobile-sidebar {
    position: absolute !important;
    top: 0 !important;
    right: 0 !important;
    width: 320px !important;
    max-width: 85vw !important;
    height: 100% !important;
    background: white !important;
    transform: translateX(100%) !important;
    transition: transform 0.3s ease !important;
    display: flex !important;
    flex-direction: column !important;
    box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15) !important;
    overflow-y: auto !important;
}

.mobile-sidebar-overlay.active .mobile-sidebar {
    transform: translateX(0) !important;
}

/* تأكيد إضافي لظهور النافذة */
@media (max-width: 768px) {
    .mobile-sidebar-overlay {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 100% !important;
        height: 100% !important;
        background: rgba(0, 0, 0, 0.5) !important;
        z-index: 1000 !important;
        opacity: 0 !important;
        visibility: hidden !important;
        transition: all 0.3s ease !important;
        display: block !important;
    }

    .mobile-sidebar-overlay.active {
        opacity: 1 !important;
        visibility: visible !important;
    }

    /* إضافة تصميم أفضل للنافذة */
    .mobile-sidebar {
        background: white !important;
        width: 320px !important;
        height: 100vh !important;
        position: absolute !important;
        right: -320px !important;
        top: 0 !important;
        transition: right 0.3s ease !important;
        box-shadow: -2px 0 10px rgba(0,0,0,0.1) !important;
        z-index: 1001 !important;
        display: flex !important;
        flex-direction: column !important;
    }

    .mobile-sidebar-overlay.active .mobile-sidebar {
        right: 0 !important;
    }

    /* تنسيق محتوى النافذة الجانبية */
    .mobile-sidebar-header {
        padding: 15px 20px;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #f8f9fa;
    }

    .mobile-sidebar-title {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
    }

    .mobile-sidebar-close {
        background: none;
        border: none;
        font-size: 18px;
        color: #666;
        cursor: pointer;
        padding: 5px;
        border-radius: 4px;
    }

    .mobile-sidebar-close:hover {
        background: #e9ecef;
        color: #333;
    }

    .mobile-sidebar-content {
        padding: 20px;
        flex: 1;
        overflow-y: auto;
    }

    .mobile-sidebar-section {
        margin-bottom: 25px;
    }

    .mobile-sidebar-section-title {
        font-size: 14px;
        font-weight: 600;
        color: #333;
        margin-bottom: 12px;
        display: flex;
        align-items: center;
    }

    .mobile-grid-controls {
        display: flex;
        gap: 10px;
        justify-content: center;
        flex-wrap: wrap;
    }

    .mobile-grid-option {
        width: 80px;
        height: 60px;
        border: 2px solid #ddd;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background: white;
        padding: 5px;
        text-align: center;
    }

    .mobile-grid-option i {
        font-size: 16px;
        margin-bottom: 4px;
    }

    .mobile-grid-option span {
        font-size: 10px;
        line-height: 1.2;
        color: #666;
    }

    .mobile-grid-option:hover {
        border-color: #007bff;
        background: #f8f9fa;
    }

    .mobile-grid-option.active {
        border-color: #007bff;
        background: #007bff;
        color: white;
    }

    .mobile-grid-option.active span {
        color: white;
    }

    /* تحسين عرض العناصر في النافذة الجانبية */
    .mobile-price-inputs {
        display: flex;
        gap: 10px;
        margin: 10px 0;
        justify-content: space-between;
    }

    .mobile-price-input {
        flex: 1;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        text-align: center;
        font-size: 12px;
        background: #f8f9fa;
    }

    /* التأكد من ظهور جميع العناصر */
    .mobile-sidebar-section {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    .mobile-sidebar-section-title {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    .mobile-grid-controls,
    .mobile-sort-select,
    .mobile-items-select,
    .mobile-price-filter {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    .mobile-grid-controls {
        display: flex !important;
    }

    /* تصحيح مشكلة عدم ظهور المحتوى */
    .mobile-sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .mobile-sidebar-overlay.active {
        opacity: 1 !important;
        visibility: visible !important;
    }

    /* تصحيح النافذة الجانبية */
    .mobile-sidebar-overlay .mobile-sidebar {
        background: white;
        width: 320px;
        height: 100vh;
        position: absolute;
        right: -320px;
        top: 0;
        transition: right 0.3s ease;
        box-shadow: -2px 0 10px rgba(0,0,0,0.1);
        display: flex;
        flex-direction: column;
        overflow-y: auto;
        z-index: 1001;
    }

    .mobile-sidebar-overlay.active .mobile-sidebar {
        right: 0 !important;
    }

    /* التأكد من ظهور المحتوى */
    .mobile-sidebar * {
        box-sizing: border-box;
    }

    .mobile-sidebar-content {
        flex: 1;
        padding: 20px;
        overflow-y: auto;
        min-height: 0; /* مهم للتمرير */
        background: white;
    }

    /* إضافة header للنافذة */
    .mobile-sidebar-header {
        padding: 15px 20px;
        border-bottom: 1px solid #eee;
        background: #f8f9fa;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-shrink: 0;
    }

    .mobile-sidebar-header h5 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
    }

    .mobile-sidebar-close {
        background: none;
        border: none;
        font-size: 18px;
        color: #666;
        cursor: pointer;
        padding: 5px;
        border-radius: 4px;
        transition: all 0.2s ease;
    }

    .mobile-sidebar-close:hover {
        background: #f0f0f0;
        color: #333;
    }

    .mobile-sidebar-section {
        margin-bottom: 25px;
        border-bottom: 1px solid #f0f0f0;
        padding-bottom: 20px;
    }

    .mobile-sidebar-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .mobile-sidebar-section-title {
        font-size: 14px;
        font-weight: 600;
        color: #333;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
    }

    .mobile-sidebar-section-title i {
        color: #007bff;
    }

    /* تحسين عرض القوائم المنسدلة */
    .mobile-sort-select,
    .mobile-items-select {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 6px;
        background: white;
        font-size: 14px;
        color: #333;
    }

    /* تحسين شريط السعر */
    .mobile-price-slider {
        width: 100%;
        margin: 10px 0;
    }

    .mobile-price-apply-btn {
        width: 100%;
        padding: 10px;
        background: #007bff;
        color: white;
        border: none;
        border-radius: 6px;
        font-size: 14px;
        cursor: pointer;
        margin-top: 10px;
        transition: background 0.2s ease;
    }

    .mobile-price-apply-btn:hover {
        background: #0056b3;
    }

    /* إجبار ظهور جميع العناصر */
    .mobile-sidebar * {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    .mobile-sidebar-header,
    .mobile-sidebar-content,
    .mobile-sidebar-section,
    .mobile-sidebar-section-title,
    .mobile-grid-controls,
    .mobile-grid-option,
    .mobile-sort-select,
    .mobile-items-select,
    .mobile-price-filter {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    .mobile-grid-controls {
        display: flex !important;
    }

    .mobile-sidebar-header {
        display: flex !important;
    }

    .mobile-sidebar {
        display: flex !important;
    }

    /* تصحيح أي مشاكل في الارتفاع */
    .mobile-sidebar-content {
        min-height: 400px !important;
        max-height: calc(100vh - 80px) !important;
    }

    .mobile-sidebar-section {
        min-height: 50px !important;
    }

    .mobile-sort-select,
    .mobile-items-select {
        width: 100%;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 6px;
        font-size: 14px;
        background: white;
    }

    .mobile-price-filter {
        text-align: center;
    }

    .mobile-price-slider {
        width: 100%;
        margin: 10px 0;
    }

    .mobile-price-display {
        font-size: 14px;
        color: #666;
        margin: 10px 0;
    }

    .mobile-price-apply-btn {
        background: #28a745;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        font-size: 12px;
        cursor: pointer;
    }

    .mobile-sidebar-footer {
        padding: 15px 20px;
        border-top: 1px solid #eee;
        background: #f8f9fa;
    }

    .mobile-sidebar-apply-btn {
        width: 100%;
        background: #007bff;
        color: white;
        border: none;
        padding: 12px;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .mobile-sidebar-apply-btn:hover {
        background: #0056b3;
    }

    /* خيارات عرض الشبكة للهواتف */
    .products-grid.mobile-grid-1 .product-item {
        width: 100% !important;
        max-width: 100% !important;
        flex: 0 0 100% !important;
    }

    .products-grid.mobile-grid-2 .product-item {
        width: calc(50% - 10px) !important;
        max-width: calc(50% - 10px) !important;
        flex: 0 0 calc(50% - 10px) !important;
    }

    .products-grid.mobile-grid-3 .product-item {
        width: calc(33.333% - 10px) !important;
        max-width: calc(33.333% - 10px) !important;
        flex: 0 0 calc(33.333% - 10px) !important;
    }

    /* تحسين عرض المنتجات في العرض الواحد */
    .products-grid.mobile-grid-1 .product-card {
        display: flex;
        align-items: center;
        padding: 15px;
        gap: 15px;
    }

    .products-grid.mobile-grid-1 .product-image {
        width: 120px;
        height: 120px;
        flex-shrink: 0;
    }

    .products-grid.mobile-grid-1 .product-info {
        flex: 1;
        text-align: right;
    }

    .products-grid.mobile-grid-1 .product-actions {
        flex-direction: column;
        gap: 8px;
    }

    /* تحسين عرض المنتجات في العرض الثلاثي */
    .products-grid.mobile-grid-3 .product-card {
        padding: 8px;
    }

    .products-grid.mobile-grid-3 .product-name {
        font-size: 12px;
        line-height: 1.3;
    }

    .products-grid.mobile-grid-3 .product-price {
        font-size: 13px;
    }

    .products-grid.mobile-grid-3 .product-actions {
        gap: 4px;
    }

    .products-grid.mobile-grid-3 .product-action-btn {
        width: 28px;
        height: 28px;
        font-size: 11px;
    }
}

/* رأس النافذة الجانبية */
.mobile-sidebar-header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f8f9fa;
}

.mobile-sidebar-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 700;
    color: #333;
    display: flex;
    align-items: center;
}

.mobile-sidebar-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #666;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.mobile-sidebar-close:hover {
    background: #e9ecef;
    color: #333;
}

/* محتوى النافذة الجانبية */
.mobile-sidebar-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.mobile-sidebar-section {
    margin-bottom: 30px;
}

.mobile-sidebar-section-title {
    font-size: 1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    padding-bottom: 8px;
    border-bottom: 2px solid #f0f0f0;
}

/* أيقونات شبكة العرض في النافذة الجانبية */
.mobile-grid-controls {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.mobile-grid-option {
    padding: 15px;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: white;
    color: #6c757d;
}

.mobile-grid-option:hover {
    border-color: #6a0dad;
    color: #6a0dad;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(106, 13, 173, 0.15);
}

.mobile-grid-option.active {
    border-color: #6a0dad;
    background: #6a0dad;
    color: white;
    box-shadow: 0 4px 12px rgba(106, 13, 173, 0.25);
}

.mobile-grid-option i {
    font-size: 18px;
    flex-shrink: 0;
}

.mobile-grid-option span {
    font-weight: 500;
}

/* عناصر التحكم في النافذة الجانبية */
.mobile-sort-select,
.mobile-items-select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    background: white;
    color: #333;
    font-size: 14px;
    transition: all 0.3s ease;
}

.mobile-sort-select:focus,
.mobile-items-select:focus {
    outline: none;
    border-color: #6a0dad;
    box-shadow: 0 0 0 3px rgba(106, 13, 173, 0.1);
}

/* تصفية السعر في النافذة الجانبية */
.mobile-price-filter {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.mobile-price-slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: #e9ecef;
    outline: none;
    -webkit-appearance: none;
}

.mobile-price-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #6a0dad;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(106, 13, 173, 0.3);
}

.mobile-price-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #6a0dad;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(106, 13, 173, 0.3);
}

.mobile-price-inputs {
    display: flex;
    gap: 10px;
}

.mobile-price-input {
    flex: 1;
    padding: 10px 12px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    background: #f8f9fa;
    color: #666;
    font-size: 13px;
    text-align: center;
}

.mobile-price-apply-btn {
    padding: 10px 20px;
    background: #6a0dad;
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.mobile-price-apply-btn:hover {
    background: #5a0b8a;
    transform: translateY(-1px);
}

/* ذيل النافذة الجانبية */
.mobile-sidebar-footer {
    padding: 20px;
    border-top: 1px solid #f0f0f0;
    background: #f8f9fa;
}

.mobile-sidebar-apply-btn {
    width: 100%;
    padding: 15px;
    background: #6a0dad;
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.mobile-sidebar-apply-btn:hover {
    background: #5a0b8a;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(106, 13, 173, 0.3);
}

/* تنسيقات شبكة المنتجات للهواتف */
@media (max-width: 991px) {
    /* منتج واحد في الصف */
    .product-grid.mobile-grid-1 {
        display: grid !important;
        grid-template-columns: 1fr !important;
        gap: 20px !important;
    }

    /* منتجين جنب بعض */
    .product-grid.mobile-grid-2 {
        display: grid !important;
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 15px !important;
    }

    /* ثلاث منتجات في الصف */
    .product-grid.mobile-grid-3 {
        display: grid !important;
        grid-template-columns: repeat(3, 1fr) !important;
        gap: 10px !important;
    }

    /* تحسينات للمنتجات في العرض الثلاثي */
    .product-grid.mobile-grid-3 .product-item-title {
        font-size: 12px !important;
        line-height: 1.3 !important;
    }

    .product-grid.mobile-grid-3 .product-item-price-current {
        font-size: 14px !important;
    }

    .product-grid.mobile-grid-3 .product-item-price-original {
        font-size: 12px !important;
    }

    .product-grid.mobile-grid-3 .product-item-action-btn {
        width: 24px !important;
        height: 24px !important;
        font-size: 10px !important;
    }

    .product-grid.mobile-grid-3 .product-item-actions {
        gap: 8px !important;
    }
}

/* قاعدة نهائية لضمان تجميع أزرار المفضلة مع باقي الأزرار */
.product-item .product-item-actions {
    display: flex !important;
    gap: 8% !important;
    align-items: center !important;
    justify-content: center !important;
    background: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    backdrop-filter: none !important;
    opacity: 0 !important;
    transition: all 0.3s ease !important;
    pointer-events: none !important;
}

.product-item:hover .product-item-actions {
    opacity: 1 !important;
    pointer-events: auto !important;
}

.product-item .product-item-actions .add-to-wishlist-btn {
    position: static !important;
    margin: 0 !important;
    flex-shrink: 0 !important;
    order: 0 !important;
}
