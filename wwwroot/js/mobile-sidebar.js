// التحكم في النافذة الجانبية في وضع الهاتف

document.addEventListener('DOMContentLoaded', function() {
    // العناصر الرئيسية
    const mobileSidebarOverlay = document.getElementById('mobileSidebarOverlay');
    const mobileSidebarPanel = document.getElementById('mobileSidebarPanel');
    const mobileSidebarClose = document.getElementById('mobileSidebarClose');

    // إغلاق النافذة الجانبية عند النقر على زر الإغلاق
    if (mobileSidebarClose) {
        mobileSidebarClose.addEventListener('click', function() {
            closeMobileSidebar();
        });
    }

    // إغلاق النافذة الجانبية عند النقر خارجها
    if (mobileSidebarOverlay) {
        mobileSidebarOverlay.addEventListener('click', function(e) {
            if (e.target === mobileSidebarOverlay) {
                closeMobileSidebar();
            }
        });
    }

    // إغلاق النافذة الجانبية عند الضغط على زر ESC
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && mobileSidebarOverlay && mobileSidebarOverlay.classList.contains('active')) {
            closeMobileSidebar();
        }
    });
});

// فتح النافذة الجانبية
function openMobileSidebar() {
    console.log('openMobileSidebar called!');

    const mobileSidebarOverlay = document.getElementById('mobileSidebarOverlay');
    const mobileSidebarPanel = document.getElementById('mobileSidebarPanel');

    console.log('mobileSidebarOverlay:', mobileSidebarOverlay);
    console.log('mobileSidebarPanel:', mobileSidebarPanel);

    if (mobileSidebarOverlay && mobileSidebarPanel) {
        console.log('Both elements found, opening sidebar...');
        mobileSidebarOverlay.classList.add('active');
        setTimeout(function() {
            mobileSidebarPanel.classList.add('active');
            console.log('Active classes added to both overlay and panel');

            // فحص الـ styles المطبقة
            const overlayStyles = window.getComputedStyle(mobileSidebarOverlay);
            const panelStyles = window.getComputedStyle(mobileSidebarPanel);
            console.log('Overlay display:', overlayStyles.display);
            console.log('Overlay z-index:', overlayStyles.zIndex);
            console.log('Panel left:', panelStyles.left);
            console.log('Panel width:', panelStyles.width);
            console.log('Panel z-index:', panelStyles.zIndex);
            console.log('Panel position:', panelStyles.position);
            console.log('Overlay classes:', mobileSidebarOverlay.className);
            console.log('Panel classes:', mobileSidebarPanel.className);
        }, 100);

        // منع التمرير في الخلفية
        document.body.classList.add('sidebar-open');
        document.body.style.overflow = 'hidden';
        document.body.style.position = 'fixed';
        document.body.style.width = '100%';
    } else {
        console.error('Elements not found!');
        console.log('All elements with mobileSidebarOverlay:', document.querySelectorAll('#mobileSidebarOverlay'));
        console.log('All elements with mobileSidebarPanel:', document.querySelectorAll('#mobileSidebarPanel'));
    }
}

// إغلاق النافذة الجانبية
function closeMobileSidebar() {
    const mobileSidebarOverlay = document.getElementById('mobileSidebarOverlay');
    const mobileSidebarPanel = document.getElementById('mobileSidebarPanel');

    if (mobileSidebarOverlay && mobileSidebarPanel) {
        mobileSidebarPanel.classList.remove('active');
        setTimeout(function() {
            mobileSidebarOverlay.classList.remove('active');
        }, 300);

        // استعادة التمرير في الخلفية
        document.body.classList.remove('sidebar-open');
        document.body.style.overflow = '';
        document.body.style.position = '';
        document.body.style.width = '';
    }
}
