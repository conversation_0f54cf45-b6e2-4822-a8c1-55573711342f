/**
 * حل مشكلة التركيز في النوافذ المنبثقة
 * يساعد هذا الملف في حل مشكلة احتفاظ العناصر بالتركيز بعد إغلاق النوافذ المنبثقة
 */
document.addEventListener('DOMContentLoaded', function() {
    // تطبيق الحل على جميع النوافذ المنبثقة في التطبيق
    var modals = document.querySelectorAll('.modal');

    modals.forEach(function(modal) {
        // قبل إغلاق النافذة المنبثقة
        modal.addEventListener('hide.bs.modal', function() {
            // إزالة التركيز من العنصر النشط
            if (document.activeElement) {
                document.activeElement.blur();
            }

            // إزالة التركيز من أزرار الإغلاق
            var closeButtons = modal.querySelectorAll('[data-bs-dismiss="modal"]');
            closeButtons.forEach(function(btn) {
                btn.blur();
            });
        });

        // بعد إغلاق النافذة المنبثقة
        modal.addEventListener('hidden.bs.modal', function() {
            // نقل التركيز إلى عنصر آمن في الصفحة
            setTimeout(function() {
                document.body.focus();
            }, 10);
        });

        // معالجة أزرار الإغلاق
        var closeButtons = modal.querySelectorAll('[data-bs-dismiss="modal"]');
        closeButtons.forEach(function(btn) {
            btn.addEventListener('click', function() {
                // إغلاق النافذة المنبثقة يدويًا
                var modalInstance = bootstrap.Modal.getInstance(modal);
                if (modalInstance) {
                    modalInstance.hide();
                }
            });

            btn.addEventListener('mousedown', function(e) {
                // منع التركيز على الزر عند النقر
                e.preventDefault();
                // إزالة التركيز قبل إغلاق النافذة
                this.blur();
            });
        });
    });

    // إضافة معالج عام لأزرار الإغلاق
    document.addEventListener('click', function(e) {
        if (e.target.hasAttribute('data-bs-dismiss') && e.target.getAttribute('data-bs-dismiss') === 'modal') {
            var modalElement = e.target.closest('.modal');
            if (modalElement) {
                var modalInstance = bootstrap.Modal.getInstance(modalElement);
                if (modalInstance) {
                    modalInstance.hide();
                }
            }
        }
    });
});
