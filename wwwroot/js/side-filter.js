// النافذة الجانبية للتصفية والمنتجات الأكثر مبيعاً

(function() {
    'use strict';

    // متغيرات عامة
    let sidebarOpen = false;

    // تعطيل النافذة الجانبية تماماً
    function isMobile() {
        return false; // تم تعطيل النافذة الجانبية
    }

    // إنشاء النافذة الجانبية
    function createSidebar() {
        // إزالة النافذة السابقة إن وجدت
        const existing = document.querySelector('.mobile-filter-sidebar');
        if (existing) {
            existing.remove();
        }

        // إنشاء HTML للنافذة الجانبية
        const sidebarHTML = `
            <div class="mobile-filter-sidebar" id="mobileFilterSidebar">
                <div class="sidebar-overlay" onclick="closeSidebar()"></div>
                <div class="sidebar-content">
                    <div class="sidebar-header">
                        <h3>تصفية المنتجات</h3>
                        <button class="close-btn" onclick="closeSidebar()">&times;</button>
                    </div>

                    <div class="sidebar-body">
                        <!-- رسالة ترحيبية -->
                        <div class="welcome-message">
                            <i class="bi bi-funnel"></i>
                            <span>اكتشف منتجاتنا المميزة والأكثر مبيعاً</span>
                        </div>

                        <!-- قسم تصفية السعر -->
                        <div class="filter-section">
                            <h4>تصفية حسب السعر</h4>
                            <div class="price-filter">
                                <label for="maxPrice">الحد الأقصى للسعر: <span id="priceValue">1000</span> ر.ع</label>
                                <input type="range" id="maxPrice" min="0" max="1000" value="1000" class="price-slider">
                                <button id="applyPriceFilter" style="margin-top: 10px; background: #6a0dad; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer; width: 100%;">تطبيق التصفية</button>
                            </div>
                        </div>

                        <!-- قسم المنتجات الأكثر مبيعاً -->
                        <div class="filter-section">
                            <h4 id="bestSellersTitle">المنتجات الأكثر مبيعاً</h4>
                            <div id="bestSellersContainer" class="best-sellers-container">
                                <div class="loading">جاري التحميل...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // إضافة HTML للصفحة
        document.body.insertAdjacentHTML('beforeend', sidebarHTML);

        // إضافة الأنماط
        addStyles();

        // تحميل المنتجات الأكثر مبيعاً
        loadBestSellers();

        // إضافة مستمعات الأحداث
        setupEventListeners();
    }

    // إعداد مستمعات الأحداث
    function setupEventListeners() {
        // شريط السعر
        const priceSlider = document.getElementById('maxPrice');
        const priceValue = document.getElementById('priceValue');

        if (priceSlider && priceValue) {
            priceSlider.addEventListener('input', function() {
                priceValue.textContent = this.value;
            });
        }

        // زر تطبيق التصفية
        const applyButton = document.getElementById('applyPriceFilter');
        if (applyButton) {
            applyButton.addEventListener('click', function() {
                const maxPrice = document.getElementById('maxPrice').value;
                applyPriceFilter(maxPrice);
            });
        }
    }

    // إضافة الأنماط
    function addStyles() {
        if (document.getElementById('sidebarStyles')) return;

        const styles = `
            <style id="sidebarStyles">
                .mobile-filter-sidebar {
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    z-index: 9999;
                    display: none;
                }

                .mobile-filter-sidebar.active {
                    display: block;
                }

                .sidebar-overlay {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.5);
                }

                .sidebar-content {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 300px;
                    height: 100%;
                    background: white;
                    transform: translateX(-100%);
                    transition: transform 0.3s ease;
                    overflow-y: auto;
                }

                .mobile-filter-sidebar.active .sidebar-content {
                    transform: translateX(0);
                }

                .sidebar-header {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 20px;
                    border-bottom: 1px solid #eee;
                    background: #6a0dad;
                    color: white;
                }

                .sidebar-header h3 {
                    margin: 0;
                    font-size: 18px;
                }

                .close-btn {
                    background: none;
                    border: none;
                    color: white;
                    font-size: 24px;
                    cursor: pointer;
                    padding: 0;
                    width: 30px;
                    height: 30px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .sidebar-body {
                    padding: 20px;
                }

                .filter-section {
                    margin-bottom: 30px;
                }

                .filter-section h4 {
                    margin: 0 0 15px 0;
                    color: #6a0dad;
                    font-size: 16px;
                }

                .price-filter label {
                    display: block;
                    margin-bottom: 10px;
                    font-size: 14px;
                }

                .price-slider {
                    width: 100%;
                    height: 5px;
                    border-radius: 5px;
                    background: #ddd;
                    outline: none;
                }

                .price-slider::-webkit-slider-thumb {
                    appearance: none;
                    width: 20px;
                    height: 20px;
                    border-radius: 50%;
                    background: #6a0dad;
                    cursor: pointer;
                }

                .best-sellers-container {
                    max-height: 400px;
                    overflow-y: auto;
                }

                .best-seller-item {
                    display: flex;
                    align-items: center;
                    padding: 10px;
                    border: 1px solid #eee;
                    border-radius: 8px;
                    margin-bottom: 10px;
                    cursor: pointer;
                    transition: all 0.3s ease;
                }

                .best-seller-item:hover {
                    background: #f8f9fa;
                    border-color: #6a0dad;
                }

                .best-seller-image {
                    width: 50px;
                    height: 50px;
                    object-fit: cover;
                    border-radius: 5px;
                    margin-left: 10px;
                }

                .best-seller-info {
                    flex: 1;
                }

                .best-seller-name {
                    font-size: 14px;
                    font-weight: bold;
                    margin: 0 0 5px 0;
                    color: #333;
                }

                .best-seller-price {
                    font-size: 13px;
                    color: #6a0dad;
                    font-weight: bold;
                }

                .loading, .error, .no-products {
                    text-align: center;
                    padding: 20px;
                    color: #666;
                    font-size: 14px;
                }

                .error {
                    color: #dc3545;
                }

                .filter-toggle-btn {
                    position: fixed;
                    top: 50%;
                    left: 0;
                    transform: translateY(-50%);
                    background: #6a0dad;
                    color: white;
                    border: none;
                    padding: 15px 8px;
                    border-radius: 0 8px 8px 0;
                    cursor: pointer;
                    z-index: 1000;
                    box-shadow: 2px 0 10px rgba(0,0,0,0.1);
                    display: none;
                }

                .filter-toggle-btn.show {
                    display: block;
                }

                .filter-toggle-btn:hover {
                    background: #5a0b9a;
                }

                @media (max-width: 768px) {
                    .filter-toggle-btn.show {
                        display: block;
                    }
                }
            </style>
        `;

        document.head.insertAdjacentHTML('beforeend', styles);
    }

    // تحميل المنتجات الأكثر مبيعاً
    function loadBestSellers() {
        const container = document.getElementById('bestSellersContainer');
        if (!container) return;

        container.innerHTML = '<div class="loading">جاري التحميل...</div>';

        fetch('/Products/GetBestSellers')
            .then(response => response.json())
            .then(data => {
                if (data.success && data.products && data.products.length > 0) {
                    displayBestSellers(data.products);
                } else {
                    container.innerHTML = '<div class="no-products">لا توجد منتجات أكثر مبيعاً حالياً</div>';
                }
            })
            .catch(error => {
                console.error('خطأ في تحميل المنتجات:', error);
                container.innerHTML = '<div class="error">حدث خطأ أثناء تحميل المنتجات</div>';
            });
    }

    // عرض المنتجات الأكثر مبيعاً
    function displayBestSellers(products) {
        const container = document.getElementById('bestSellersContainer');
        const titleElement = document.getElementById('bestSellersTitle');

        if (!container) return;

        // تحديث العنوان مع العداد
        if (titleElement) {
            titleElement.innerHTML = `المنتجات الأكثر مبيعاً <span class="products-count">(${products.length})</span>`;
        }

        let html = '';
        products.forEach(product => {
            html += `
                <div class="best-seller-item">
                    <div class="best-seller-image-container" onclick="goToProduct(${product.Id})">
                        <img src="${product.ImageUrl}" alt="${product.Name}" class="best-seller-image" onerror="this.src='/images/placeholder.jpg'">
                        <div class="best-seller-badge">
                            <i class="bi bi-star-fill"></i>
                        </div>
                    </div>
                    <div class="best-seller-info" onclick="goToProduct(${product.Id})">
                        <div class="best-seller-name">${product.Name}</div>
                        <div class="best-seller-price">${product.Price} ر.ع</div>
                    </div>
                    <div class="best-seller-actions">
                        <button class="wishlist-btn" onclick="event.stopPropagation(); addToWishlistFromSidebar(${product.Id})" title="إضافة للمفضلة">
                            <i class="bi bi-heart"></i>
                        </button>
                    </div>
                </div>
            `;
        });

        container.innerHTML = html;
    }

    // إنشاء زر التبديل
    function createToggleButton() {
        if (document.querySelector('.filter-toggle-btn')) return;

        const button = document.createElement('button');
        button.className = 'filter-toggle-btn';
        button.innerHTML = '<i class="bi bi-funnel-fill"></i>';
        button.title = 'تصفية المنتجات والمنتجات الأكثر مبيعاً';
        button.onclick = openSidebar;

        document.body.appendChild(button);

        // إظهار الزر على جميع أحجام الشاشات
        function toggleButtonVisibility() {
            button.classList.add('show');
        }

        toggleButtonVisibility();
        window.addEventListener('resize', toggleButtonVisibility);
    }

    // فتح النافذة الجانبية
    function openSidebar() {
        const sidebar = document.getElementById('mobileFilterSidebar');
        if (sidebar) {
            sidebar.classList.add('active');
            sidebarOpen = true;
            document.body.style.overflow = 'hidden';
        }
    }

    // إغلاق النافذة الجانبية
    window.closeSidebar = function() {
        const sidebar = document.getElementById('mobileFilterSidebar');
        if (sidebar) {
            sidebar.classList.remove('active');
            sidebarOpen = false;
            document.body.style.overflow = '';
        }
    }

    // الانتقال إلى صفحة المنتج
    window.goToProduct = function(productId) {
        window.location.href = `/Products/Details/${productId}`;
    }

    // تطبيق تصفية السعر
    function applyPriceFilter(maxPrice) {
        const currentUrl = new URL(window.location);
        currentUrl.searchParams.set('maxPrice', maxPrice);
        window.location.href = currentUrl.toString();
    }

    // إضافة للمفضلة من النافذة الجانبية
    window.addToWishlistFromSidebar = function(productId) {
        // الحصول على رمز التحقق من CSRF
        const token = document.querySelector('input[name="__RequestVerificationToken"]')?.value;

        if (!token) {
            alert('حدث خطأ أثناء إضافة المنتج للمفضلة');
            return;
        }

        // إرسال طلب AJAX
        fetch('/Wishlist/AddToWishlist', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'RequestVerificationToken': token,
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: `productId=${productId}&__RequestVerificationToken=${token}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث أيقونة القلب
                const button = document.querySelector(`button[onclick*="${productId}"]`);
                if (button) {
                    const icon = button.querySelector('i');
                    if (data.isInWishlist) {
                        icon.className = 'bi bi-heart-fill';
                        button.classList.add('active');
                        showSidebarToast('تمت إضافة المنتج للمفضلة ❤️');
                    } else {
                        icon.className = 'bi bi-heart';
                        button.classList.remove('active');
                        showSidebarToast('تمت إزالة المنتج من المفضلة');
                    }
                }

                // تحديث عداد المفضلة في الصفحة الرئيسية
                const wishlistBadges = document.querySelectorAll('.wishlist-count');
                wishlistBadges.forEach(badge => {
                    badge.textContent = data.count;
                });
            } else {
                showSidebarToast('حدث خطأ أثناء تحديث المفضلة');
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            showSidebarToast('حدث خطأ أثناء تحديث المفضلة');
        });
    }

    // عرض رسالة توست في النافذة الجانبية
    function showSidebarToast(message) {
        const sidebar = document.getElementById('mobileFilterSidebar');
        if (!sidebar) return;

        // إنشاء عنصر التوست
        const toast = document.createElement('div');
        toast.className = 'sidebar-toast';
        toast.textContent = message;

        sidebar.appendChild(toast);

        // إظهار التوست
        setTimeout(() => toast.classList.add('show'), 100);

        // إخفاء التوست بعد 3 ثوان
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }

    // تهيئة النافذة الجانبية
    function init() {
        if (isMobile()) {
            createSidebar();
            createToggleButton();
        }
    }

    // إدارة إظهار/إخفاء النافذة حسب حجم الشاشة
    function handleResize() {
        if (isMobile()) {
            if (!document.querySelector('.mobile-filter-sidebar')) {
                createSidebar();
                createToggleButton();
            }
        } else {
            // إزالة النافذة الجانبية على الشاشات الكبيرة
            const sidebar = document.querySelector('.mobile-filter-sidebar');
            const button = document.querySelector('.filter-toggle-btn');
            if (sidebar) sidebar.remove();
            if (button) button.remove();
        }
    }

    // تشغيل التهيئة عند تحميل الصفحة
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

    // مراقبة تغيير حجم الشاشة
    window.addEventListener('resize', handleResize);

})();
