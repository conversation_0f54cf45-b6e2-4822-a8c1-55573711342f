// التحكم في نافذة التصفية الجانبية للهواتف

document.addEventListener('DOMContentLoaded', function() {
    // العناصر الرئيسية
    const fixedFilterButton = document.getElementById('fixedFilterButton');
    const sideFilterOverlay = document.getElementById('sideFilterOverlay');
    const sideFilterPanel = document.getElementById('sideFilterPanel');
    const sideFilterClose = document.getElementById('sideFilterClose');
    
    // عناصر التصفية
    const mobilePriceRange = document.getElementById('mobilePriceRange');
    const mobilePriceValue = document.getElementById('mobilePriceValue');
    const mobilePriceMin = document.getElementById('mobilePriceMin');
    const mobilePriceMax = document.getElementById('mobilePriceMax');
    const mobileApplyPriceFilter = document.getElementById('mobileApplyPriceFilter');
    const mobileAvailableOnly = document.getElementById('mobileAvailableOnly');
    const mobileOutOfStock = document.getElementById('mobileOutOfStock');
    const mobileClearFilters = document.getElementById('mobileClearFilters');
    const mobileApplyFilters = document.getElementById('mobileApplyFilters');

    // فتح النافذة الجانبية عند النقر على الزر الثابت
    if (fixedFilterButton) {
        fixedFilterButton.addEventListener('click', function(e) {
            e.preventDefault();
            openSideFilter();
        });
    }

    // إغلاق النافذة الجانبية عند النقر على زر الإغلاق
    if (sideFilterClose) {
        sideFilterClose.addEventListener('click', function() {
            closeSideFilter();
        });
    }

    // إغلاق النافذة الجانبية عند النقر خارجها
    if (sideFilterOverlay) {
        sideFilterOverlay.addEventListener('click', function(e) {
            if (e.target === sideFilterOverlay) {
                closeSideFilter();
            }
        });
    }

    // تحديث قيمة السعر عند تحريك الشريط
    if (mobilePriceRange && mobilePriceValue) {
        mobilePriceRange.addEventListener('input', function() {
            mobilePriceValue.textContent = this.value + ' ر.ع';
            if (mobilePriceMax) {
                mobilePriceMax.value = this.value;
            }
        });
    }

    // تحديث الشريط عند تغيير قيم الإدخال
    if (mobilePriceMin) {
        mobilePriceMin.addEventListener('input', function() {
            if (mobilePriceRange) {
                mobilePriceRange.min = this.value || 0;
            }
        });
    }

    if (mobilePriceMax) {
        mobilePriceMax.addEventListener('input', function() {
            if (mobilePriceRange) {
                mobilePriceRange.value = this.value || 1000;
                if (mobilePriceValue) {
                    mobilePriceValue.textContent = this.value + ' ر.ع';
                }
            }
        });
    }

    // تطبيق تصفية السعر
    if (mobileApplyPriceFilter) {
        mobileApplyPriceFilter.addEventListener('click', function() {
            applyPriceFilter();
        });
    }

    // تطبيق جميع التصفيات وإغلاق النافذة
    if (mobileApplyFilters) {
        mobileApplyFilters.addEventListener('click', function() {
            applyAllFilters();
            closeSideFilter();
        });
    }

    // إعادة تعيين التصفيات
    if (mobileClearFilters) {
        mobileClearFilters.addEventListener('click', function() {
            clearAllFilters();
        });
    }

    // وظائف فتح وإغلاق النافذة الجانبية
    function openSideFilter() {
        if (sideFilterOverlay && sideFilterPanel) {
            sideFilterOverlay.style.display = 'block';
            setTimeout(function() {
                sideFilterPanel.classList.add('active');
            }, 10);
            document.body.style.overflow = 'hidden';
        }
    }

    function closeSideFilter() {
        if (sideFilterOverlay && sideFilterPanel) {
            sideFilterPanel.classList.remove('active');
            setTimeout(function() {
                sideFilterOverlay.style.display = 'none';
            }, 300);
            document.body.style.overflow = '';
        }
    }

    // تطبيق تصفية السعر
    function applyPriceFilter() {
        const minPrice = mobilePriceMin ? mobilePriceMin.value : 0;
        const maxPrice = mobilePriceMax ? mobilePriceMax.value : 1000;
        
        // تطبيق التصفية على المنتجات المعروضة
        filterProductsByPrice(minPrice, maxPrice);
        
        // إظهار رسالة تأكيد
        showToast('تم تطبيق تصفية السعر');
    }

    // تطبيق جميع التصفيات
    function applyAllFilters() {
        const minPrice = mobilePriceMin ? mobilePriceMin.value : 0;
        const maxPrice = mobilePriceMax ? mobilePriceMax.value : 1000;
        const availableOnly = mobileAvailableOnly ? mobileAvailableOnly.checked : false;
        const outOfStock = mobileOutOfStock ? mobileOutOfStock.checked : false;
        
        // تطبيق التصفيات
        filterProducts(minPrice, maxPrice, availableOnly, outOfStock);
        
        // إظهار رسالة تأكيد
        showToast('تم تطبيق جميع التصفيات');
    }

    // إعادة تعيين التصفيات
    function clearAllFilters() {
        if (mobilePriceRange) mobilePriceRange.value = 1000;
        if (mobilePriceValue) mobilePriceValue.textContent = '1000 ر.ع';
        if (mobilePriceMin) mobilePriceMin.value = '';
        if (mobilePriceMax) mobilePriceMax.value = '';
        if (mobileAvailableOnly) mobileAvailableOnly.checked = false;
        if (mobileOutOfStock) mobileOutOfStock.checked = false;
        
        // إعادة عرض جميع المنتجات
        showAllProducts();
        
        // إظهار رسالة تأكيد
        showToast('تم إعادة تعيين جميع التصفيات');
    }

    // تصفية المنتجات حسب السعر
    function filterProductsByPrice(minPrice, maxPrice) {
        const products = document.querySelectorAll('.product-item');
        
        products.forEach(product => {
            const priceElement = product.querySelector('.product-price');
            if (priceElement) {
                const priceText = priceElement.textContent.replace(/[^\d.]/g, '');
                const price = parseFloat(priceText);
                
                if (price >= minPrice && price <= maxPrice) {
                    product.style.display = 'block';
                } else {
                    product.style.display = 'none';
                }
            }
        });
    }

    // تصفية المنتجات حسب جميع المعايير
    function filterProducts(minPrice, maxPrice, availableOnly, outOfStock) {
        const products = document.querySelectorAll('.product-item');
        
        products.forEach(product => {
            let showProduct = true;
            
            // تصفية السعر
            const priceElement = product.querySelector('.product-price');
            if (priceElement) {
                const priceText = priceElement.textContent.replace(/[^\d.]/g, '');
                const price = parseFloat(priceText);
                
                if (price < minPrice || price > maxPrice) {
                    showProduct = false;
                }
            }
            
            // تصفية التوفر
            const availabilityElement = product.querySelector('.availability-status');
            if (availabilityElement) {
                const isAvailable = availabilityElement.textContent.includes('متوفر');
                
                if (availableOnly && !isAvailable) {
                    showProduct = false;
                }
                
                if (outOfStock && isAvailable) {
                    showProduct = false;
                }
            }
            
            product.style.display = showProduct ? 'block' : 'none';
        });
    }

    // إظهار جميع المنتجات
    function showAllProducts() {
        const products = document.querySelectorAll('.product-item');
        products.forEach(product => {
            product.style.display = 'block';
        });
    }

    // إظهار رسالة توست
    function showToast(message) {
        // إنشاء عنصر التوست
        const toast = document.createElement('div');
        toast.className = 'mobile-filter-toast';
        toast.textContent = message;
        
        // إضافة التوست إلى الصفحة
        document.body.appendChild(toast);
        
        // إظهار التوست
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);
        
        // إخفاء التوست بعد 3 ثوان
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 3000);
    }

    // إغلاق النافذة الجانبية عند الضغط على زر ESC
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && sideFilterOverlay && sideFilterOverlay.style.display === 'block') {
            closeSideFilter();
        }
    });

    // تعريف الوظائف كوظائف عالمية
    window.openSideFilter = openSideFilter;
    window.closeSideFilter = closeSideFilter;
});
