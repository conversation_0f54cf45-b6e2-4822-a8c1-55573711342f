// Opciones de visualización para productos e imágenes
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar las opciones de visualización desde localStorage o usar valores predeterminados
    initDisplayOptions();

    // Aplicar las opciones de visualización actuales
    applyDisplayOptions();

    // Configurar los controles de visualización
    setupDisplayControls();
});

// Inicializar opciones de visualización
function initDisplayOptions() {
    // Si no existen opciones guardadas, establecer valores predeterminados
    if (!localStorage.getItem('displayOptions')) {
        const defaultOptions = {
            imageSize: 'small', // tiny, small, medium, large
            displayStyle: 'grid', // grid, list
            itemsPerRow: 4, // 2, 3, 4, 6
            imageEffect: 'zoom', // zoom, fade, none
            showQuickAdd: true // true, false
        };

        localStorage.setItem('displayOptions', JSON.stringify(defaultOptions));
    }
}

// Obtener las opciones de visualización actuales
function getDisplayOptions() {
    return JSON.parse(localStorage.getItem('displayOptions'));
}

// Guardar opciones de visualización
function saveDisplayOptions(options) {
    localStorage.setItem('displayOptions', JSON.stringify(options));
}

// Aplicar opciones de visualización a la página
function applyDisplayOptions() {
    const options = getDisplayOptions();
    const productGrid = document.querySelector('.product-grid');
    const categoryCards = document.querySelectorAll('.category-card');
    const productCards = document.querySelectorAll('.product-card');
    const cardImgContainers = document.querySelectorAll('.card-img-container, .product-img-container');

    // Si no hay elementos para aplicar estilos, salir
    if (!productGrid && !categoryCards.length && !productCards.length) {
        return;
    }

    // Aplicar estilo de visualización (grid o list)
    if (productGrid) {
        productGrid.className = 'product-grid';
        productGrid.classList.add(`display-${options.displayStyle}`);

        // Aplicar número de elementos por fila usando clases en lugar de estilos directos
        productGrid.classList.remove('items-per-row-2', 'items-per-row-3', 'items-per-row-4', 'items-per-row-6', 'grid-2', 'grid-4', 'grid-6', 'grid-16');
        productGrid.classList.add(`items-per-row-${options.itemsPerRow}`);
    }

    // Aplicar tamaño de imagen
    cardImgContainers.forEach(container => {
        container.style.height = getImageHeight(options.imageSize);
    });

    // Aplicar efecto de imagen
    productCards.forEach(card => {
        // Eliminar clases de efectos anteriores
        card.classList.remove('effect-zoom', 'effect-fade', 'effect-none');
        // Agregar la clase de efecto actual
        card.classList.add(`effect-${options.imageEffect}`);

        // Mostrar/ocultar botón de agregar rápido
        const quickAddBtn = card.querySelector('.product-img-cart-btn');
        if (quickAddBtn) {
            quickAddBtn.style.display = options.showQuickAdd ? 'flex' : 'none';
        }
    });

    // Aplicar estilos a las tarjetas de categoría
    categoryCards.forEach(card => {
        card.style.height = getImageHeight(options.imageSize);
    });
}

// Obtener altura de imagen según el tamaño seleccionado
function getImageHeight(size) {
    switch(size) {
        case 'tiny': return '120px';
        case 'small': return '160px';
        case 'medium': return '200px';
        case 'large': return '260px';
        default: return '200px';
    }
}

// Configurar controles de visualización
function setupDisplayControls() {
    // Buscar el contenedor de controles de visualización
    const displayControls = document.getElementById('displayControls');
    if (!displayControls) {
        // إذا لم يوجد displayControls، جرب إعداد أزرار العرض المباشرة
        setupViewModeButtons();
        return;
    }

    const options = getDisplayOptions();

    // Configurar eventos para los controles
    const sizeControls = displayControls.querySelectorAll('.size-control');
    sizeControls.forEach(control => {
        // Marcar el control activo
        if (control.dataset.size === options.imageSize) {
            control.classList.add('active');
        }

        // Agregar evento de clic
        control.addEventListener('click', function() {
            const size = this.dataset.size;
            options.imageSize = size;
            saveDisplayOptions(options);

            // Actualizar UI
            sizeControls.forEach(c => c.classList.remove('active'));
            this.classList.add('active');

            applyDisplayOptions();
        });
    });

    // Configurar control de estilo de visualización
    const styleControls = displayControls.querySelectorAll('.style-control');
    styleControls.forEach(control => {
        // Marcar el control activo
        if (control.dataset.style === options.displayStyle) {
            control.classList.add('active');
        }

        // Agregar evento de clic
        control.addEventListener('click', function() {
            const style = this.dataset.style;
            options.displayStyle = style;
            saveDisplayOptions(options);

            // Actualizar UI
            styleControls.forEach(c => c.classList.remove('active'));
            this.classList.add('active');

            applyDisplayOptions();
        });
    });

    // Configurar control de elementos por fila
    const rowControls = displayControls.querySelectorAll('.row-control');
    rowControls.forEach(control => {
        // Marcar el control activo
        if (parseInt(control.dataset.items) === options.itemsPerRow) {
            control.classList.add('active');
        }

        // Agregar evento de clic
        control.addEventListener('click', function() {
            const items = parseInt(this.dataset.items);
            options.itemsPerRow = items;
            saveDisplayOptions(options);

            // Actualizar UI
            rowControls.forEach(c => c.classList.remove('active'));
            this.classList.add('active');

            applyDisplayOptions();
        });
    });

    // Configurar control de efecto de imagen
    const effectControls = displayControls.querySelectorAll('.effect-control');
    effectControls.forEach(control => {
        // Marcar el control activo
        if (control.dataset.effect === options.imageEffect) {
            control.classList.add('active');
        }

        // Agregar evento de clic
        control.addEventListener('click', function() {
            const effect = this.dataset.effect;
            options.imageEffect = effect;
            saveDisplayOptions(options);

            // Actualizar UI
            effectControls.forEach(c => c.classList.remove('active'));
            this.classList.add('active');

            applyDisplayOptions();
        });
    });

    // Configurar control de agregar rápido
    const quickAddControl = displayControls.querySelector('.quick-add-control');
    if (quickAddControl) {
        // Establecer estado inicial
        quickAddControl.checked = options.showQuickAdd;

        // Agregar evento de cambio
        quickAddControl.addEventListener('change', function() {
            options.showQuickAdd = this.checked;
            saveDisplayOptions(options);
            applyDisplayOptions();
        });
    }
}

// إعداد أزرار العرض المباشرة (للصفحات التي لا تحتوي على displayControls)
function setupViewModeButtons() {
    const viewModeOptions = document.querySelectorAll('.view-mode-option');
    if (viewModeOptions.length === 0) return;

    console.log('Setting up view mode buttons:', viewModeOptions.length);

    viewModeOptions.forEach(option => {
        option.addEventListener('click', function() {
            const view = this.getAttribute('data-view').trim(); // إزالة المسافات الإضافية
            console.log('View mode clicked:', view);

            // إزالة الفئة النشطة من جميع الأزرار
            viewModeOptions.forEach(opt => opt.classList.remove('active'));

            // إضافة الفئة النشطة للزر المضغوط
            this.classList.add('active');

            // تطبيق التغيير على الشبكة
            applyViewMode(view);

            // حفظ التفضيل
            localStorage.setItem('productViewMode', view);
        });
    });

    // تحميل التفضيل المحفوظ
    const savedView = localStorage.getItem('productViewMode') || 'grid-2';
    const savedViewOption = document.querySelector(`.view-mode-option[data-view="${savedView}"]`);

    if (savedViewOption) {
        // إزالة الفئة النشطة من جميع الأزرار
        viewModeOptions.forEach(opt => opt.classList.remove('active'));
        // إضافة الفئة النشطة للزر المحفوظ
        savedViewOption.classList.add('active');
        // تطبيق العرض المحفوظ
        applyViewMode(savedView);
    }
}

// تطبيق نمط العرض على الشبكة
function applyViewMode(viewMode) {
    const productGrid = document.querySelector('.product-grid');
    if (!productGrid) return;

    // تنظيف viewMode من المسافات الإضافية
    viewMode = viewMode.trim();

    console.log('Applying view mode:', viewMode, 'to grid:', productGrid);

    // إزالة جميع فئات العرض السابقة
    productGrid.classList.remove('grid-6', 'grid-4', 'grid-2', 'items-per-row-6', 'items-per-row-4', 'items-per-row-2');

    // إضافة الفئة الجديدة
    productGrid.classList.add(viewMode);

    // إضافة فئة items-per-row المقابلة
    if (viewMode === 'grid-6') {
        productGrid.classList.add('items-per-row-6');
    } else if (viewMode === 'grid-4') {
        productGrid.classList.add('items-per-row-4');
    } else if (viewMode === 'grid-2') {
        productGrid.classList.add('items-per-row-2');
    }

    console.log('Grid classes after applying view mode:', productGrid.className);
}

// Función para alternar la visibilidad del panel de opciones de visualización
function toggleDisplayOptions() {
    const panel = document.getElementById('displayOptionsPanel');
    if (panel) {
        panel.classList.toggle('show');
    }
}
