// Opciones de visualización para productos e imágenes
document.addEventListener('DOMContentLoaded', function() {
    // Inicializar las opciones de visualización desde localStorage o usar valores predeterminados
    initDisplayOptions();

    // Aplicar las opciones de visualización actuales
    applyDisplayOptions();

    // Configurar los controles de visualización
    setupDisplayControls();
});

// Inicializar opciones de visualización
function initDisplayOptions() {
    // Si no existen opciones guardadas, establecer valores predeterminados
    if (!localStorage.getItem('displayOptions')) {
        const defaultOptions = {
            imageSize: 'small', // tiny, small, medium, large
            displayStyle: 'grid', // grid, list
            itemsPerRow: 4, // 2, 3, 4, 6
            imageEffect: 'zoom', // zoom, fade, none
            showQuickAdd: true // true, false
        };

        localStorage.setItem('displayOptions', JSON.stringify(defaultOptions));
    }
}

// Obtener las opciones de visualización actuales
function getDisplayOptions() {
    return JSON.parse(localStorage.getItem('displayOptions'));
}

// Guardar opciones de visualización
function saveDisplayOptions(options) {
    localStorage.setItem('displayOptions', JSON.stringify(options));
}

// Aplicar opciones de visualización a la página
function applyDisplayOptions() {
    const options = getDisplayOptions();
    const productGrid = document.querySelector('.product-grid');
    const categoryCards = document.querySelectorAll('.category-card');
    const productCards = document.querySelectorAll('.product-card');
    const cardImgContainers = document.querySelectorAll('.card-img-container, .product-img-container');

    // Si no hay elementos para aplicar estilos, salir
    if (!productGrid && !categoryCards.length && !productCards.length) {
        return;
    }

    // Aplicar estilo de visualización (grid o list)
    if (productGrid) {
        productGrid.className = 'product-grid';
        productGrid.classList.add(`display-${options.displayStyle}`);

        // Aplicar número de elementos por fila usando clases en lugar de estilos directos
        productGrid.classList.remove('items-per-row-2', 'items-per-row-3', 'items-per-row-4', 'items-per-row-6', 'grid-2', 'grid-4', 'grid-6', 'grid-16');
        productGrid.classList.add(`items-per-row-${options.itemsPerRow}`);
    }

    // Aplicar tamaño de imagen
    cardImgContainers.forEach(container => {
        container.style.height = getImageHeight(options.imageSize);
    });

    // Aplicar efecto de imagen
    productCards.forEach(card => {
        // Eliminar clases de efectos anteriores
        card.classList.remove('effect-zoom', 'effect-fade', 'effect-none');
        // Agregar la clase de efecto actual
        card.classList.add(`effect-${options.imageEffect}`);

        // Mostrar/ocultar botón de agregar rápido
        const quickAddBtn = card.querySelector('.product-img-cart-btn');
        if (quickAddBtn) {
            quickAddBtn.style.display = options.showQuickAdd ? 'flex' : 'none';
        }
    });

    // Aplicar estilos a las tarjetas de categoría
    categoryCards.forEach(card => {
        card.style.height = getImageHeight(options.imageSize);
    });
}

// Obtener altura de imagen según el tamaño seleccionado
function getImageHeight(size) {
    switch(size) {
        case 'tiny': return '120px';
        case 'small': return '160px';
        case 'medium': return '200px';
        case 'large': return '260px';
        default: return '200px';
    }
}

// Configurar controles de visualización
function setupDisplayControls() {
    // Buscar el contenedor de controles de visualización
    const displayControls = document.getElementById('displayControls');
    if (!displayControls) {
        // إذا لم يوجد displayControls، جرب إعداد أزرار العرض المباشرة
        setupViewModeButtons();
        return;
    }

    const options = getDisplayOptions();

    // Configurar eventos para los controles
    const sizeControls = displayControls.querySelectorAll('.size-control');
    sizeControls.forEach(control => {
        // Marcar el control activo
        if (control.dataset.size === options.imageSize) {
            control.classList.add('active');
        }

        // Agregar evento de clic
        control.addEventListener('click', function() {
            const size = this.dataset.size;
            options.imageSize = size;
            saveDisplayOptions(options);

            // Actualizar UI
            sizeControls.forEach(c => c.classList.remove('active'));
            this.classList.add('active');

            applyDisplayOptions();
        });
    });

    // Configurar control de estilo de visualización
    const styleControls = displayControls.querySelectorAll('.style-control');
    styleControls.forEach(control => {
        // Marcar el control activo
        if (control.dataset.style === options.displayStyle) {
            control.classList.add('active');
        }

        // Agregar evento de clic
        control.addEventListener('click', function() {
            const style = this.dataset.style;
            options.displayStyle = style;
            saveDisplayOptions(options);

            // Actualizar UI
            styleControls.forEach(c => c.classList.remove('active'));
            this.classList.add('active');

            applyDisplayOptions();
        });
    });

    // Configurar control de elementos por fila
    const rowControls = displayControls.querySelectorAll('.row-control');
    rowControls.forEach(control => {
        // Marcar el control activo
        if (parseInt(control.dataset.items) === options.itemsPerRow) {
            control.classList.add('active');
        }

        // Agregar evento de clic
        control.addEventListener('click', function() {
            const items = parseInt(this.dataset.items);
            options.itemsPerRow = items;
            saveDisplayOptions(options);

            // Actualizar UI
            rowControls.forEach(c => c.classList.remove('active'));
            this.classList.add('active');

            applyDisplayOptions();
        });
    });

    // Configurar control de efecto de imagen
    const effectControls = displayControls.querySelectorAll('.effect-control');
    effectControls.forEach(control => {
        // Marcar el control activo
        if (control.dataset.effect === options.imageEffect) {
            control.classList.add('active');
        }

        // Agregar evento de clic
        control.addEventListener('click', function() {
            const effect = this.dataset.effect;
            options.imageEffect = effect;
            saveDisplayOptions(options);

            // Actualizar UI
            effectControls.forEach(c => c.classList.remove('active'));
            this.classList.add('active');

            applyDisplayOptions();
        });
    });

    // Configurar control de agregar rápido
    const quickAddControl = displayControls.querySelector('.quick-add-control');
    if (quickAddControl) {
        // Establecer estado inicial
        quickAddControl.checked = options.showQuickAdd;

        // Agregar evento de cambio
        quickAddControl.addEventListener('change', function() {
            options.showQuickAdd = this.checked;
            saveDisplayOptions(options);
            applyDisplayOptions();
        });
    }
}

// إعداد أزرار العرض المباشرة (للصفحات التي لا تحتوي على displayControls)
function setupViewModeButtons() {
    const viewModeOptions = document.querySelectorAll('.view-mode-option');
    if (viewModeOptions.length === 0) {
        console.log('⚠️ No view mode buttons found');
        return;
    }

    console.log('🎛️ Setting up view mode buttons:', viewModeOptions.length);
    console.log('📱 Screen width:', window.innerWidth);

    viewModeOptions.forEach((option, index) => {
        const dataView = option.getAttribute('data-view');
        const isMobile = option.classList.contains('mobile-view');
        console.log(`Button ${index + 1}: data-view="${dataView}", mobile: ${isMobile}`);

        option.addEventListener('click', function() {
            const view = this.getAttribute('data-view').trim();
            console.log('🖱️ View mode clicked:', view);

            // تحديد نطاق الأزرار للتحديث (desktop أو mobile)
            const isMobileButton = this.classList.contains('mobile-view');
            const buttonsToUpdate = isMobileButton
                ? document.querySelectorAll('.view-mode-option.mobile-view')
                : document.querySelectorAll('.view-mode-option:not(.mobile-view)');

            // إزالة الفئة النشطة من الأزرار في نفس النطاق
            buttonsToUpdate.forEach(opt => opt.classList.remove('active'));

            // إضافة الفئة النشطة للزر المضغوط
            this.classList.add('active');

            // تطبيق التغيير على الشبكة
            applyViewMode(view);

            // حفظ التفضيل
            localStorage.setItem('productViewMode', view);
            console.log('💾 Saved view mode:', view);
        });
    });

    // تحميل التفضيل المحفوظ
    // تحديد التفضيل الافتراضي بناءً على حجم الشاشة
    const defaultView = window.innerWidth <= 576 ? 'mobile-grid-2' : 'grid-2';
    const savedView = localStorage.getItem('productViewMode') || defaultView;

    // البحث عن الزر المحفوظ في أزرار الشاشات الكبيرة أولاً
    let savedViewOption = document.querySelector(`.view-mode-option:not(.mobile-view)[data-view="${savedView}"]`);

    // إذا لم يوجد، ابحث في أزرار الشاشات الصغيرة
    if (!savedViewOption) {
        savedViewOption = document.querySelector(`.view-mode-option.mobile-view[data-view="${savedView}"]`);
    }

    if (savedViewOption) {
        // تحديد نطاق الأزرار للتحديث
        const isMobileButton = savedViewOption.classList.contains('mobile-view');
        const buttonsToUpdate = isMobileButton
            ? document.querySelectorAll('.view-mode-option.mobile-view')
            : document.querySelectorAll('.view-mode-option:not(.mobile-view)');

        // إزالة الفئة النشطة من الأزرار في نفس النطاق
        buttonsToUpdate.forEach(opt => opt.classList.remove('active'));

        // إضافة الفئة النشطة للزر المحفوظ
        savedViewOption.classList.add('active');

        // تطبيق العرض المحفوظ
        applyViewMode(savedView);
    }
}

// تطبيق نمط العرض على الشبكة
function applyViewMode(viewMode) {
    const productGrid = document.querySelector('.product-grid') || document.querySelector('.products-grid');
    if (!productGrid) {
        console.error('❌ Product grid not found');
        return;
    }

    // تنظيف viewMode من المسافات الإضافية
    viewMode = viewMode.trim();

    console.log('🎯 Applying view mode:', viewMode, 'to grid:', productGrid);
    console.log('📱 Current screen width:', window.innerWidth);

    // إزالة جميع فئات العرض السابقة
    productGrid.classList.remove('grid-6', 'grid-4', 'grid-3','grid-2', 'items-per-row-6', 'items-per-row-4','items-per-row-3' ,'items-per-row-2', 'mobile-grid-1', 'mobile-grid-2', 'mobile-grid-3');

    // إضافة الفئة الجديدة
    productGrid.classList.add(viewMode);

    // إضافة فئة items-per-row المقابلة للأزرار العادية
    if (viewMode === 'grid-6') {
        productGrid.classList.add('items-per-row-6');
    } else if (viewMode === 'grid-4') {
        productGrid.classList.add('items-per-row-4');
    } else if (viewMode === 'grid-3') {
        productGrid.classList.add('items-per-row-3');
    } else if (viewMode === 'grid-2') {
        productGrid.classList.add('items-per-row-2');
    }
    // لا نحتاج items-per-row للأزرار المحمولة لأن CSS يتعامل معها مباشرة

    // إضافة تأثير بصري لتأكيد التغيير
    productGrid.style.transition = 'all 0.3s ease';
    productGrid.style.transform = 'scale(0.98)';
    setTimeout(() => {
        productGrid.style.transform = 'scale(1)';
    }, 150);

    console.log('✅ Grid classes after applying view mode:', productGrid.className);

    // فرض إعادة حساب التخطيط
    productGrid.style.display = 'none';
    productGrid.offsetHeight; // trigger reflow
    productGrid.style.display = 'grid';

    console.log('🔄 Layout recalculated');
}

// Función para alternar la visibilidad del panel de opciones de visualización
function toggleDisplayOptions() {
    const panel = document.getElementById('displayOptionsPanel');
    if (panel) {
        panel.classList.toggle('show');
    }
}
