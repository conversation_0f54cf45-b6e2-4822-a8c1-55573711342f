// التحكم في شريط التنقل العلوي القابل للاختفاء

document.addEventListener('DOMContentLoaded', function() {
    // العناصر الرئيسية
    const stickyNav = document.querySelector('.sticky-nav');
    const header = document.querySelector('header');
    const navbar = document.querySelector('.main-navigation');

    // متغيرات للتحكم في التمرير
    let lastScrollTop = 0;
    let headerHeight = 0;

    // حساب الارتفاع الكلي للهيدر وشريط التنقل
    if (header) headerHeight += header.offsetHeight;
    if (navbar) headerHeight += navbar.offsetHeight;

    // التحقق من وجود العناصر
    if (!stickyNav) return;

    // تحديث عدادات الشريط العلوي من العدادات الرئيسية
    function updateStickyCounters() {
        // لا نحتاج لتحديث عداد المفضلة هنا لأن الدالة الموحدة تحدث جميع العدادات
        // فقط نحدث عدادات المقارنة والسلة

        // تحديث عداد المقارنة
        const compareCount = document.querySelector('.compare-count:not(.sticky-badge)');
        const stickyCompareCount = document.querySelector('.sticky-badge.compare-count');
        if (compareCount && stickyCompareCount) {
            stickyCompareCount.textContent = compareCount.textContent;
        }

        // تحديث عداد السلة
        const cartCount = document.querySelector('.cart-count:not(.sticky-badge)');
        const stickyCartCount = document.querySelector('.sticky-badge.cart-count');
        if (cartCount && stickyCartCount) {
            stickyCartCount.textContent = cartCount.textContent;
        }
    }

    // وظيفة التحكم في ظهور الشريط عند التمرير
    function handleScroll() {
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

        // إظهار الشريط فقط عند التمرير لأعلى وبعد تجاوز ارتفاع الهيدر
        if (scrollTop < lastScrollTop && scrollTop > headerHeight) {
            // التمرير لأعلى - إظهار الشريط
            stickyNav.classList.add('visible');
            // تحديث العدادات
            updateStickyCounters();
        } else {
            // التمرير لأسفل أو في أعلى الصفحة - إخفاء الشريط
            stickyNav.classList.remove('visible');
        }

        // تحديث آخر موضع تمرير (فقط إذا كان التغيير كبيرًا بما يكفي لتجنب الحساسية المفرطة)
        if (Math.abs(scrollTop - lastScrollTop) > 5) {
            lastScrollTop = scrollTop;
        }
    }

    // إضافة مستمع حدث التمرير
    window.addEventListener('scroll', handleScroll);

    // تحديث ارتفاع الهيدر عند تغيير حجم النافذة
    window.addEventListener('resize', function() {
        headerHeight = 0;
        if (header) headerHeight += header.offsetHeight;
        if (navbar) headerHeight += navbar.offsetHeight;
    });

    // تحديث العدادات عند تحديث أي من العدادات الرئيسية
    document.addEventListener('wishlist:updated', updateStickyCounters);
    document.addEventListener('compare:updated', updateStickyCounters);
    document.addEventListener('cart:updated', updateStickyCounters);

    // تحديث العدادات عند تحميل الصفحة
    updateStickyCounters();
});
