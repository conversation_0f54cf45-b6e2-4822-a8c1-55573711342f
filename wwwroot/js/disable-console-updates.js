// ملف JavaScript لإيقاف تحديثات وحدة التحكم (Console)

// تنفيذ الكود عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // إيقاف تحديثات وحدة التحكم
    disableConsoleUpdates();
});

/**
 * وظيفة لإيقاف تحديثات وحدة التحكم
 */
function disableConsoleUpdates() {
    // حفظ وظائف وحدة التحكم الأصلية
    const originalConsole = {
        log: console.log,
        error: console.error,
        warn: console.warn,
        info: console.info,
        debug: console.debug
    };

    // استبدال وظائف وحدة التحكم بوظائف فارغة
    console.log = function() {};
    console.error = function() {};
    console.warn = function() {};
    console.info = function() {};
    console.debug = function() {};

    // إيقاف التحديثات الدورية
    stopPeriodicUpdates();

    // إضافة زر لإعادة تفعيل وحدة التحكم (للمطورين فقط)
    addConsoleToggleButton(originalConsole);
}

/**
 * وظيفة لإيقاف التحديثات الدورية
 */
function stopPeriodicUpdates() {
    // إيقاف جميع الفترات الزمنية بطريقة آمنة
    try {
        // الحصول على أعلى معرف للفترات الزمنية
        const highestTimeoutId = setTimeout(() => {}, 0);
        clearTimeout(highestTimeoutId);

        // إيقاف جميع الفترات الزمنية
        for (let i = 0; i < highestTimeoutId; i++) {
            clearTimeout(i);
            clearInterval(i);
        }
    } catch (e) {
        // تجاهل الأخطاء
    }

    // إيقاف وظيفة تحديث العدادات في ملف _Layout.cshtml
    if (window.updateAllCounters) {
        window.updateAllCounters = function() {};
    }

    // إيقاف الفترات الزمنية المحددة
    if (window.counterUpdateInterval) {
        clearInterval(window.counterUpdateInterval);
    }
}

/**
 * وظيفة لإضافة زر لإعادة تفعيل وحدة التحكم (للمطورين فقط)
 * @param {Object} originalConsole - وظائف وحدة التحكم الأصلية
 */
function addConsoleToggleButton(originalConsole) {
    // إنشاء زر مخفي يمكن تفعيله بضغط مفاتيح معينة
    document.addEventListener('keydown', function(e) {
        // Ctrl + Alt + D لإعادة تفعيل وحدة التحكم
        if (e.ctrlKey && e.altKey && e.key === 'd') {
            console = originalConsole;
            console.log('تم إعادة تفعيل وحدة التحكم');
        }
    });
}
