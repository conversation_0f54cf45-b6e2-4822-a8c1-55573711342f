// المنتجات الأكثر مبيعاً في الصفحة الرئيسية

(function() {
    'use strict';

    // تحميل المنتجات الأكثر مبيعاً
    function loadBestSellers() {
        const container = document.getElementById('bestSellersContainer');
        const countElement = document.getElementById('bestSellersCount');
        
        if (!container) return;

        container.innerHTML = '<div class="loading-spinner">جاري التحميل...</div>';

        fetch('/Products/GetBestSellers')
            .then(response => response.json())
            .then(data => {
                console.log('استجابة المنتجات الأكثر مبيعاً:', data);

                if (data.success && data.products && data.products.length > 0) {
                    console.log('تم العثور على منتجات:', data.products);
                    displayBestSellers(data.products);
                    if (countElement) {
                        countElement.textContent = '(' + data.products.length + ')';
                    }
                } else {
                    console.log('لا توجد منتجات أو فشل في التحميل');
                    container.innerHTML = '<div class="no-products-message"><i class="bi bi-star" style="font-size: 2rem; margin-bottom: 10px; display: block; color: #dee2e6;"></i>لا توجد منتجات متوفرة حالياً<br><small style="color: #adb5bd; margin-top: 5px; display: block;">سيتم عرض المنتجات الأكثر مبيعاً هنا</small></div>';
                    if (countElement) {
                        countElement.textContent = '(0)';
                    }
                }
            })
            .catch(error => {
                console.error('خطأ في تحميل المنتجات:', error);
                container.innerHTML = '<div class="error-message">حدث خطأ أثناء تحميل المنتجات</div>';
                if (countElement) {
                    countElement.textContent = '(0)';
                }
            });
    }

    // عرض المنتجات الأكثر مبيعاً
    function displayBestSellers(products) {
        const container = document.getElementById('bestSellersContainer');
        if (!container) return;

        let html = '';
        products.forEach((product, index) => {
            // التأكد من وجود البيانات
            const productId = product.Id || product.id || 0;
            const productName = product.Name || product.name || 'منتج غير محدد';
            const productPrice = product.DiscountedPrice || product.discountedPrice || product.Price || product.price || 0;
            const productImage = product.ImageUrl || product.imageUrl || '/images/placeholder.jpg';

            const rankIcon = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '#' + (index + 1);

            html += '<div class="best-seller-item" onclick="goToBestSellerProduct(' + productId + ')">' +
                    '<div class="best-seller-rank">' +
                        '<span class="rank-number">' + rankIcon + '</span>' +
                    '</div>' +
                    '<div class="best-seller-image-container">' +
                        '<img src="' + productImage + '" alt="' + productName + '" class="best-seller-image" onerror="this.src=\'/images/placeholder.jpg\'">' +
                        '<div class="best-seller-badge">' +
                            '<i class="bi bi-star-fill"></i>' +
                        '</div>' +
                    '</div>' +
                    '<div class="best-seller-info">' +
                        '<div class="best-seller-name">' + productName + '</div>' +
                        '<div class="best-seller-price">' + productPrice + ' ر.ع</div>' +
                    '</div>' +
                    '<div class="best-seller-actions">' +
                        '<button class="best-seller-wishlist-btn" onclick="event.stopPropagation(); addToBestSellerWishlist(' + productId + ')" title="إضافة للمفضلة">' +
                            '<i class="bi bi-heart"></i>' +
                        '</button>' +
                    '</div>' +
                '</div>';
        });

        container.innerHTML = html;
    }

    // الانتقال إلى صفحة المنتج
    window.goToBestSellerProduct = function(productId) {
        window.location.href = '/Products/Details/' + productId;
    }

    // إضافة للمفضلة
    window.addToBestSellerWishlist = function(productId) {
        const token = document.querySelector('input[name="__RequestVerificationToken"]')?.value;
        
        if (!token) {
            showBestSellerToast('حدث خطأ أثناء إضافة المنتج للمفضلة');
            return;
        }

        fetch('/Wishlist/AddToWishlist', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'RequestVerificationToken': token,
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: 'productId=' + productId + '&__RequestVerificationToken=' + token
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const button = document.querySelector('button[onclick*="addToBestSellerWishlist(' + productId + ')"]');
                if (button) {
                    const icon = button.querySelector('i');
                    if (data.isInWishlist) {
                        icon.className = 'bi bi-heart-fill';
                        button.classList.add('active');
                        showBestSellerToast('تمت إضافة المنتج للمفضلة ❤️');
                    } else {
                        icon.className = 'bi bi-heart';
                        button.classList.remove('active');
                        showBestSellerToast('تمت إزالة المنتج من المفضلة');
                    }
                }
                
                // تحديث عداد المفضلة
                const wishlistBadges = document.querySelectorAll('.wishlist-count');
                wishlistBadges.forEach(badge => {
                    badge.textContent = data.count;
                });
            } else {
                showBestSellerToast('حدث خطأ أثناء تحديث المفضلة');
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            showBestSellerToast('حدث خطأ أثناء تحديث المفضلة');
        });
    }

    // تبديل إظهار/إخفاء قسم المنتجات الأكثر مبيعاً
    window.toggleBestSellersSection = function() {
        const container = document.getElementById('bestSellersContainer');
        const icon = document.getElementById('bestSellersToggle');

        if (container && icon) {
            if (container.style.display === 'none') {
                container.style.display = 'flex';
                icon.className = 'bi bi-chevron-down toggle-icon';
            } else {
                container.style.display = 'none';
                icon.className = 'bi bi-chevron-left toggle-icon';
            }
        }
    }

    // عرض رسالة توست
    function showBestSellerToast(message) {
        // إنشاء عنصر التوست
        const toast = document.createElement('div');
        toast.className = 'best-seller-toast';
        toast.textContent = message;
        toast.style.cssText = 'position: fixed; top: 20px; right: 20px; background: #6a0dad; color: white; padding: 12px 20px; border-radius: 8px; font-size: 0.9rem; z-index: 10000; transform: translateX(100%); opacity: 0; transition: all 0.3s ease; box-shadow: 0 4px 12px rgba(106, 13, 173, 0.3);';
        
        document.body.appendChild(toast);
        
        // إظهار التوست
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
            toast.style.opacity = '1';
        }, 100);
        
        // إخفاء التوست بعد 3 ثوان
        setTimeout(() => {
            toast.style.transform = 'translateX(100%)';
            toast.style.opacity = '0';
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }

    // عرض المنتجات الأكثر مبيعاً للهواتف
    function displayMobileBestSellers(products) {
        const container = document.getElementById('mobileBestSellersContainer');
        if (!container) return;

        let html = '';
        products.forEach((product, index) => {
            // التأكد من وجود البيانات
            const productId = product.Id || product.id || 0;
            const productName = product.Name || product.name || 'منتج غير محدد';
            const productPrice = product.DiscountedPrice || product.discountedPrice || product.Price || product.price || 0;
            const productImage = product.ImageUrl || product.imageUrl || '/images/placeholder.jpg';

            const rankIcon = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '#' + (index + 1);

            html += '<div class="mobile-best-seller-item" onclick="goToBestSellerProduct(' + productId + ')">' +
                    '<div class="mobile-best-seller-content">' +
                        '<div class="mobile-best-seller-rank">' +
                            '<span class="mobile-rank-number">' + rankIcon + '</span>' +
                        '</div>' +
                        '<div class="mobile-best-seller-image-container">' +
                            '<img src="' + productImage + '" alt="' + productName + '" class="mobile-best-seller-image" onerror="this.src=\'/images/placeholder.jpg\'">' +
                            '<div class="mobile-best-seller-badge">' +
                                '<i class="bi bi-star-fill"></i>' +
                            '</div>' +
                        '</div>' +
                        '<div class="mobile-best-seller-info">' +
                            '<div class="mobile-best-seller-name">' + productName + '</div>' +
                            '<div class="mobile-best-seller-price">' + productPrice + ' ر.ع</div>' +
                            '<div class="mobile-best-seller-actions">' +
                                '<button class="mobile-best-seller-wishlist-btn" onclick="event.stopPropagation(); addToBestSellerWishlist(' + productId + ')" title="إضافة للمفضلة">' +
                                    '<i class="bi bi-heart"></i>' +
                                '</button>' +
                            '</div>' +
                        '</div>' +
                    '</div>' +
                '</div>';
        });

        container.innerHTML = html;
    }

    // تحميل المنتجات الأكثر مبيعاً للهواتف
    function loadMobileBestSellers() {
        const container = document.getElementById('mobileBestSellersContainer');
        const countElement = document.getElementById('mobileBestSellersCount');

        if (!container) return;

        container.innerHTML = '<div class="loading-spinner">جاري التحميل...</div>';

        fetch('/Products/GetBestSellers')
            .then(response => response.json())
            .then(data => {
                console.log('استجابة المنتجات الأكثر مبيعاً للهواتف:', data);

                if (data.success && data.products && data.products.length > 0) {
                    console.log('تم العثور على منتجات للهواتف:', data.products);
                    displayMobileBestSellers(data.products);
                    if (countElement) {
                        countElement.textContent = '(' + data.products.length + ')';
                    }
                } else {
                    console.log('لا توجد منتجات أو فشل في التحميل للهواتف');
                    container.innerHTML = '<div class="no-products-message"><i class="bi bi-star" style="font-size: 2rem; margin-bottom: 10px; display: block; color: #dee2e6;"></i>لا توجد منتجات متوفرة حالياً<br><small style="color: #adb5bd; margin-top: 5px; display: block;">سيتم عرض المنتجات الأكثر مبيعاً هنا</small></div>';
                    if (countElement) {
                        countElement.textContent = '(0)';
                    }
                }
            })
            .catch(error => {
                console.error('خطأ في تحميل المنتجات للهواتف:', error);
                container.innerHTML = '<div class="error-message">حدث خطأ أثناء تحميل المنتجات</div>';
                if (countElement) {
                    countElement.textContent = '(0)';
                }
            });
    }

    // تهيئة المنتجات الأكثر مبيعاً
    function init() {
        // التحقق من وجود العنصر في الصفحة (للشاشات الكبيرة)
        if (document.getElementById('bestSellersContainer')) {
            loadBestSellers();
        }

        // التحقق من وجود العنصر في الصفحة (للهواتف)
        if (document.getElementById('mobileBestSellersContainer')) {
            loadMobileBestSellers();
        }
    }

    // تشغيل التهيئة عند تحميل الصفحة
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
