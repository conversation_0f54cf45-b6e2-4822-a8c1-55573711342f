// ملف إدارة وظائف المقارنة
console.log('تم تحميل ملف compare-actions.js');
(function() {
    'use strict';

    // متغيرات عامة
    let isUpdatingCompareCount = false;
    let compareList = [];

    // تهيئة وظائف المقارنة عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        console.log('بدء تهيئة وظائف المقارنة - compare-actions.js محمل بنجاح');
        initializeCompare();

        // تحديث فوري من الـ cookies
        updateCompareCountFromCookies();

        // تحديث العداد من الخادم بعد فترة قصيرة
        setTimeout(updateCompareCount, 500);
    });

    // تهيئة وظائف المقارنة
    function initializeCompare() {
        // تحديث عداد المقارنة
        updateCompareCount();
        
        // تهيئة أزرار المقارنة
        initCompareButtons();
        
        // تحديث حالة الأزرار
        updateCompareButtonsState();
    }

    // تهيئة أزرار المقارنة
    function initCompareButtons() {
        const compareButtons = document.querySelectorAll('.add-to-compare-btn');
        
        compareButtons.forEach(button => {
            // إزالة event listeners السابقة لتجنب التكرار
            button.removeEventListener('click', handleCompareClick);
            
            // إضافة event listener جديد
            button.addEventListener('click', handleCompareClick);
        });
        
        console.log(`تم تهيئة ${compareButtons.length} زر مقارنة`);
    }

    // معالج النقر على زر المقارنة
    function handleCompareClick(e) {
        e.preventDefault();
        e.stopPropagation();
        
        const button = e.currentTarget;
        const productId = parseInt(button.getAttribute('data-product-id'));
        
        if (!productId) {
            console.error('معرف المنتج غير صحيح');
            return;
        }
        
        console.log('تم النقر على زر المقارنة للمنتج:', productId);
        
        // إضافة تأثير التحميل
        button.classList.add('adding');
        
        // تبديل حالة المقارنة
        toggleCompareItem(productId, button);
    }

    // تبديل حالة المنتج في المقارنة
    function toggleCompareItem(productId, buttonElement) {
        fetch('/Compare/ToggleCompareItem', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({ productId: productId })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('استجابة الخادم:', data);
            
            if (data.success) {
                // تحديث حالة الزر
                updateCompareButtonState(productId, data.isInCompareList);
                
                // تحديث العداد فوراً
                setTimeout(updateCompareCount, 100);
                
                // عرض رسالة نجاح
                showToast(data.message, 'success');
                
                // إذا تم إزالة منتج آخر، تحديث حالته
                if (data.removedId) {
                    updateCompareButtonState(data.removedId, false);
                }

                // تحديث العداد مرة أخرى للتأكد
                setTimeout(updateCompareCount, 500);
            } else {
                showToast(data.message || 'حدث خطأ أثناء تحديث المقارنة', 'error');
            }
        })
        .catch(error => {
            console.error('خطأ في طلب المقارنة:', error);
            showToast('حدث خطأ أثناء تحديث المقارنة', 'error');
        })
        .finally(() => {
            // إزالة تأثير التحميل
            if (buttonElement) {
                buttonElement.classList.remove('adding');
            }
        });
    }

    // تحديث حالة زر المقارنة
    function updateCompareButtonState(productId, isInCompare) {
        const buttons = document.querySelectorAll(`.add-to-compare-btn[data-product-id="${productId}"]`);
        
        buttons.forEach(button => {
            if (isInCompare) {
                button.classList.add('active');
                button.setAttribute('title', 'إزالة من المقارنة');
            } else {
                button.classList.remove('active');
                button.setAttribute('title', 'إضافة للمقارنة');
            }
        });
    }

    // تحديث حالة جميع أزرار المقارنة
    function updateCompareButtonsState() {
        fetch('/Compare/GetCompareItems')
        .then(response => response.json())
        .then(data => {
            if (data && data.items) {
                compareList = data.items;
                
                // تحديث حالة جميع الأزرار
                data.items.forEach(productId => {
                    updateCompareButtonState(productId, true);
                });
                
                console.log(`تم تحديث حالة ${data.items.length} منتج في المقارنة`);
            }
        })
        .catch(error => {
            console.error('خطأ في جلب عناصر المقارنة:', error);
        });
    }

    // دالة مساعدة لقراءة الـ cookies
    function getCookie(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) {
            const cookieValue = parts.pop().split(';').shift();
            try {
                // فك تشفير URL encoding
                return decodeURIComponent(cookieValue);
            } catch (e) {
                console.warn('خطأ في فك تشفير cookie:', name, e);
                return cookieValue;
            }
        }
        return null;
    }

    // تحديث عداد المقارنة من الـ cookies مباشرة
    function updateCompareCountFromCookies() {
        try {
            const compareList = getCookie('compareList');
            let count = 0;

            if (compareList && compareList !== '[]' && compareList !== '') {
                try {
                    const items = JSON.parse(compareList);
                    count = Array.isArray(items) ? items.length : 0;
                } catch (parseError) {
                    console.warn('خطأ في تحليل JSON للمقارنة:', parseError, 'القيمة:', compareList);
                    count = 0;
                }
            }

            console.log(`تحديث عداد المقارنة من الـ cookies: ${count}`);

            const countElement = document.querySelector('.compare-count');
            if (countElement) {
                countElement.textContent = count;
            }

            const mobileCountElement = document.querySelector('.mobile-compare-count');
            if (mobileCountElement) {
                mobileCountElement.textContent = count;
            }

            return count;
        } catch (error) {
            console.error('خطأ في قراءة عداد المقارنة من الـ cookies:', error);
            return 0;
        }
    }

    // تحديث عداد المقارنة
    function updateCompareCount() {
        if (isUpdatingCompareCount) {
            return;
        }

        // تحديث فوري من الـ cookies
        const cookieCount = updateCompareCountFromCookies();

        isUpdatingCompareCount = true;
        console.log('بدء تحديث عداد المقارنة من الخادم...');

        fetch('/Compare/GetCompareItemsCount', {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('استجابة عداد المقارنة من الخادم:', data);
            const countElement = document.querySelector('.compare-count');
            if (countElement && data.count !== undefined) {
                const oldCount = countElement.textContent;
                countElement.textContent = data.count;
                console.log(`تم تحديث عداد المقارنة من ${oldCount} إلى ${data.count}`);

                // إضافة تأثير بصري للتحديث
                countElement.classList.add('updated');
                setTimeout(() => {
                    countElement.classList.remove('updated');
                }, 300);

                // تحديث العداد في الـ mobile header أيضاً
                const mobileCountElement = document.querySelector('.mobile-compare-count');
                if (mobileCountElement) {
                    mobileCountElement.textContent = data.count;
                }
            } else {
                console.error('عنصر العداد غير موجود أو البيانات غير صحيحة');
            }
        })
        .catch(error => {
            console.error('خطأ في تحديث عداد المقارنة من الخادم:', error);
            // في حالة الخطأ، استخدم القيمة من الـ cookies
            console.log('استخدام القيمة من الـ cookies بدلاً من الخادم');
        })
        .finally(() => {
            isUpdatingCompareCount = false;
        });
    }

    // عرض رسالة Toast
    function showToast(message, type = 'info') {
        // إنشاء عنصر Toast
        const toast = document.createElement('div');
        toast.className = `toast-notification toast-${type}`;
        toast.innerHTML = `
            <div class="toast-content">
                <i class="bi bi-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
                <span>${message}</span>
            </div>
        `;
        
        // إضافة Toast إلى الصفحة
        document.body.appendChild(toast);
        
        // إظهار Toast
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);
        
        // إخفاء Toast بعد 3 ثوان
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 3000);
    }

    // إزالة منتج من المقارنة (للاستخدام في صفحة المقارنة)
    function removeFromCompare(productId, buttonElement) {
        if (buttonElement) {
            buttonElement.classList.add('removing');
        }
        
        fetch('/Compare/ToggleCompareItem', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({ productId: productId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // إزالة المنتج من الواجهة
                const productCell = document.querySelector(`[data-product-id="${productId}"]`);
                if (productCell) {
                    productCell.remove();
                }
                
                // تحديث العداد فوراً
                setTimeout(updateCompareCount, 100);
                
                // إعادة تحميل صفحة المقارنة إذا لم تعد هناك منتجات
                if (data.count === 0) {
                    location.reload();
                }
                
                showToast(data.message, 'success');
            } else {
                showToast(data.message || 'حدث خطأ أثناء إزالة المنتج', 'error');
            }
        })
        .catch(error => {
            console.error('خطأ في إزالة المنتج من المقارنة:', error);
            showToast('حدث خطأ أثناء إزالة المنتج', 'error');
        })
        .finally(() => {
            if (buttonElement) {
                buttonElement.classList.remove('removing');
            }
        });
    }

    // تصدير الدوال للاستخدام العام
    window.removeFromCompare = removeFromCompare;
    window.updateCompareCount = updateCompareCount;
    window.updateCompareCountFromCookies = updateCompareCountFromCookies;
    window.initializeCompare = initializeCompare;

    // إشارة أن الملف تم تحميله بنجاح
    window.compareActionsLoaded = true;
    console.log('تم تصدير دوال المقارنة بنجاح');

})();
