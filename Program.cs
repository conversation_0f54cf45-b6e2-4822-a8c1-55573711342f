using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

using Abayat.Data;
using Abayat.Models;
using Microsoft.Extensions.Logging;
using Abayat.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllersWithViews();
builder.Services.AddRazorPages();

// Add Session services
builder.Services.AddDistributedMemoryCache();
builder.Services.AddSession(options =>
{
    options.IdleTimeout = TimeSpan.FromHours(2);
    options.Cookie.HttpOnly = true;
    options.Cookie.IsEssential = true;
});

// Add DbContext
builder.Services.AddDbContext<ApplicationDbContext>(options =>
{
    options.UseSqlServer(builder.Configuration.GetConnectionString("DefaultConnection"),
    sqlServerOptionsAction: sqlOptions =>
    {
        sqlOptions.EnableRetryOnFailure(
            maxRetryCount: 10,
            maxRetryDelay: TimeSpan.FromSeconds(60),
            errorNumbersToAdd: null);
        sqlOptions.CommandTimeout(120);
    });
    options.LogTo(Console.WriteLine, LogLevel.Information);
});

// Add Identity
builder.Services.AddIdentity<ApplicationUser, IdentityRole>(options => {
    // إعدادات كلمة المرور
    options.Password.RequireDigit = true;
    options.Password.RequireLowercase = true;
    options.Password.RequireUppercase = true;
    options.Password.RequireNonAlphanumeric = true;
    options.Password.RequiredLength = 6;

    // إعدادات تسجيل الدخول
    options.SignIn.RequireConfirmedAccount = false;
    options.SignIn.RequireConfirmedEmail = false;
})
    .AddEntityFrameworkStores<ApplicationDbContext>()
    .AddDefaultTokenProviders();

// إعدادات ملفات تعريف الارتباط (Cookie)
builder.Services.ConfigureApplicationCookie(options => {
    options.LoginPath = "/Identity/Account/Login";
    options.LogoutPath = "/Identity/Account/Logout";
    options.AccessDeniedPath = "/Identity/Account/AccessDenied";
    options.SlidingExpiration = true;
    options.ExpireTimeSpan = TimeSpan.FromDays(7);
});

// تسجيل خدمات سلة المشتريات والطلبات
builder.Services.AddScoped<IShoppingCartService, ShoppingCartService>();
builder.Services.AddScoped<IOrderService, OrderService>();
builder.Services.AddScoped<IFeaturedProductService, FeaturedProductService>();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    app.UseHsts();
}

// Add custom 404 page
app.UseStatusCodePagesWithReExecute("/Home/PageNotFound", "?statusCode={0}");

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();

app.UseAuthentication();
app.UseAuthorization();

// Use session middleware
app.UseSession();

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Splash}/{id?}");

app.MapRazorPages();

// تهيئة قاعدة البيانات والأدوار
using (var scope = app.Services.CreateScope())
{
    var services = scope.ServiceProvider;
    var logger = services.GetRequiredService<ILogger<Program>>();

    try
    {
        var context = services.GetRequiredService<ApplicationDbContext>();

        // التحقق من إمكانية الاتصال بقاعدة البيانات
        bool canConnect = context.Database.CanConnect();
        logger.LogInformation("حالة الاتصال بقاعدة البيانات: {CanConnect}", canConnect);

        // تطبيق الهجرات وإنشاء قاعدة البيانات إذا لم تكن موجودة
        try {
            // تعديل يدوي لجدول الطلبات للسماح بقيم فارغة في عمود UserId
            context.Database.ExecuteSqlRaw("ALTER TABLE Orders ALTER COLUMN UserId nvarchar(450) NULL");
            logger.LogInformation("تم تعديل جدول الطلبات للسماح بقيم فارغة في عمود UserId");
        }
        catch (Exception ex) {
            logger.LogError("خطأ أثناء تعديل جدول الطلبات: {Message}", ex.Message);
        }

        // تطبيق الهجرات لإنشاء الجداول المطلوبة
        try {
            context.Database.Migrate();
            logger.LogInformation("تم تطبيق الهجرات بنجاح");
        } catch (Exception ex) {
            logger.LogError("خطأ أثناء تطبيق الهجرات: {Message}", ex.Message);
        }

        // إنشاء جدول CarouselImages إذا لم يكن موجوداً
        try {
            context.Database.ExecuteSqlRaw(@"
                IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'CarouselImages')
                BEGIN
                    CREATE TABLE [CarouselImages] (
                        [Id] int NOT NULL IDENTITY,
                        [Title] nvarchar(max) NOT NULL,
                        [Description] nvarchar(max) NULL,
                        [ImageUrl] nvarchar(max) NOT NULL,
                        [LinkUrl] nvarchar(max) NULL,
                        [DisplayOrder] int NOT NULL,
                        [IsActive] bit NOT NULL,
                        [CreatedAt] datetime2 NOT NULL,
                        CONSTRAINT [PK_CarouselImages] PRIMARY KEY ([Id])
                    );
                    PRINT 'تم إنشاء جدول CarouselImages بنجاح';
                END
                ELSE
                BEGIN
                    PRINT 'جدول CarouselImages موجود بالفعل';
                END
            ");
            logger.LogInformation("تم التحقق من وجود جدول CarouselImages");
        } catch (Exception ex) {
            logger.LogError("خطأ أثناء إنشاء جدول CarouselImages: {Message}", ex.Message);
        }

        // إصلاح عمود ShowInCarousel
        await DatabaseFixer.FixShowInCarouselColumn(builder.Configuration, logger);
        logger.LogInformation("تم التحقق من وجود عمود ShowInCarousel");

        // إضافة عمود DiscountPercentage
        await DatabaseFixer.AddDiscountPercentageColumn(builder.Configuration, logger);
        logger.LogInformation("تم التحقق من وجود عمود DiscountPercentage");

        // إضافة أعمدة طرق الدفع المفقودة
        try
        {
            await SqlScriptExecutor.ExecuteScript("add_payment_columns.sql", builder.Configuration, logger);
            logger.LogInformation("تم تنفيذ script إضافة أعمدة طرق الدفع بنجاح");
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "خطأ في تنفيذ script إضافة أعمدة طرق الدفع");
        }

        // تهيئة الأدوار والمستخدمين
        await DbInitializer.Initialize(services, builder.Configuration);
        logger.LogInformation("تم تهيئة الأدوار والمستخدمين بنجاح");
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "حدث خطأ أثناء تهيئة قاعدة البيانات: {Message}", ex.Message);

        if (ex.InnerException != null)
        {
            logger.LogError("الخطأ الداخلي: {InnerMessage}", ex.InnerException.Message);
        }
    }
}

app.Run();