using System.ComponentModel.DataAnnotations;
using Abayat.Models;
using Microsoft.AspNetCore.Http;

namespace Abayat.ViewModels
{
    public class CheckoutViewModel
    {
        [Required(ErrorMessage = "الاسم الكامل مطلوب")]
        [Display(Name = "الاسم الكامل")]
        [StringLength(100, ErrorMessage = "يجب أن يكون الاسم بين {2} و {1} حرفًا", MinimumLength = 3)]
        public string FullName { get; set; } = string.Empty;

        [Required(ErrorMessage = "رقم الهاتف مطلوب")]
        [Display(Name = "رقم الهاتف")]
        [RegularExpression(@"^[0-9]{8,15}$", ErrorMessage = "يرجى إدخال رقم هاتف صحيح (8 أرقام على الأقل)")]
        public string PhoneNumber { get; set; } = string.Empty;

        [Required(ErrorMessage = "العنوان مطلوب")]
        [Display(Name = "العنوان")]
        [StringLength(200, ErrorMessage = "يجب أن يكون العنوان بين {2} و {1} حرفًا", MinimumLength = 3)]
        public string Address { get; set; } = string.Empty;

        [Display(Name = "ملاحظات")]
        [StringLength(500, ErrorMessage = "يجب أن تكون الملاحظات أقل من {1} حرفًا")]
        public string? Notes { get; set; }

        // طرق الدفع
        [Required(ErrorMessage = "طريقة الدفع مطلوبة")]
        [Display(Name = "طريقة الدفع")]
        public PaymentMethod PaymentMethod { get; set; } = PaymentMethod.CashOnDelivery;

        // للتحويل عبر الهاتف
        [Display(Name = "رقم الهاتف للتحويل")]
        [RegularExpression(@"^[0-9]{8,15}$", ErrorMessage = "يرجى إدخال رقم هاتف صحيح")]
        public string? PaymentPhoneNumber { get; set; }

        [Display(Name = "وصل التحويل")]
        public IFormFile? PaymentReceipt { get; set; }

        [Display(Name = "ملاحظات الدفع")]
        [StringLength(1000, ErrorMessage = "يجب أن تكون ملاحظات الدفع أقل من {1} حرفًا")]
        public string? PaymentNotes { get; set; }

        // معلومات السلة
        public List<CartItemViewModel> CartItems { get; set; } = new List<CartItemViewModel>();
        public decimal SubTotal { get; set; }
        public decimal ShippingCost { get; set; } = 0; // مجاني
        public decimal Total { get; set; }
    }

    public class CartItemViewModel
    {
        public int Id { get; set; }
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public string ProductImageUrl { get; set; } = string.Empty;
        public decimal Price { get; set; }
        public decimal? DiscountPercentage { get; set; }
        public decimal FinalPrice { get; set; }
        public int Quantity { get; set; }
        public decimal Total { get; set; }
    }

    public class LoginViewModel
    {
        [Required(ErrorMessage = "البريد الإلكتروني مطلوب")]
        [EmailAddress(ErrorMessage = "البريد الإلكتروني غير صحيح")]
        [Display(Name = "البريد الإلكتروني")]
        public string Email { get; set; } = string.Empty;

        [Required(ErrorMessage = "كلمة المرور مطلوبة")]
        [DataType(DataType.Password)]
        [Display(Name = "كلمة المرور")]
        public string Password { get; set; } = string.Empty;

        [Display(Name = "تذكرني")]
        public bool RememberMe { get; set; }
    }
}
