using System.ComponentModel.DataAnnotations;

namespace Abayat.ViewModels
{
    public class BestSellerViewModel
    {
        public int ProductId { get; set; }
        
        [Display(Name = "اسم المنتج")]
        public string ProductName { get; set; } = string.Empty;
        
        [Display(Name = "سعر المنتج")]
        public decimal ProductPrice { get; set; }
        
        [Display(Name = "صورة المنتج")]
        public string? ProductImageUrl { get; set; }
        
        [Display(Name = "الكمية المباعة")]
        public int TotalQuantity { get; set; }
        
        [Display(Name = "إجمالي الإيرادات")]
        public decimal TotalRevenue { get; set; }
        
        [Display(Name = "متوفر")]
        public bool IsAvailable { get; set; }
        
        [Display(Name = "في الشريط المتحرك")]
        public bool ShowInCarousel { get; set; }
        
        [Display(Name = "منتج مميز")]
        public bool IsFeatured { get; set; }
        
        [Display(Name = "ترتيب المبيعات")]
        public int SalesRank { get; set; }
    }
}
