namespace Abayat.ViewModels
{
    public class FeaturedProductViewModel
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; } = string.Empty;
        public string ProductImageUrl { get; set; } = string.Empty;
        public decimal ProductPrice { get; set; }
        public int TotalQuantity { get; set; }
        public decimal TotalRevenue { get; set; }
        public bool IsAvailable { get; set; }
        public bool ShowInCarousel { get; set; }
        public int DisplayOrder { get; set; }
        public DateTime CreatedAt { get; set; }
    }

    public class ManageFeaturedProductsViewModel
    {
        public List<FeaturedProductViewModel> FeaturedProducts { get; set; } = new();
        public List<AvailableProductViewModel> AvailableProducts { get; set; } = new();
        public int MaxFeaturedProducts { get; set; } = 5;
        public bool CanAddMore => FeaturedProducts.Count < MaxFeaturedProducts;
    }

    public class AvailableProductViewModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
        public string ImageUrl { get; set; } = string.Empty;
        public decimal Price { get; set; }
        public bool IsAvailable { get; set; }
        public string CategoryName { get; set; } = string.Empty;
        public bool IsAlreadyFeatured { get; set; }
    }
}
