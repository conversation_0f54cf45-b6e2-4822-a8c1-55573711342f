using System;
using System.Collections.Generic;
using Abayat.Models;

namespace Abayat.ViewModels
{
    public class AdminDashboardViewModel
    {
        // إحصائيات عامة
        public int TotalProducts { get; set; }
        public int TotalCategories { get; set; }
        public int TotalUsers { get; set; }
        public int TotalOrders { get; set; }
        public decimal TotalSales { get; set; }
        
        // الطلبات حسب الحالة
        public int PendingOrders { get; set; }
        public int ConfirmedOrders { get; set; }
        public int ProcessingOrders { get; set; }
        public int ShippedOrders { get; set; }
        public int DeliveredOrders { get; set; }
        public int CancelledOrders { get; set; }
        
        // آخر المنتجات المضافة
        public List<Product> RecentProducts { get; set; }
        
        // آخر الطلبات
        public List<Order> RecentOrders { get; set; }
        
        // المنتجات الأكثر مبيعًا
        public List<TopSellingProductViewModel> TopSellingProducts { get; set; }
    }

    public class TopSellingProductViewModel
    {
        public int ProductId { get; set; }
        public string ProductName { get; set; }
        public decimal ProductPrice { get; set; }
        public string ProductImageUrl { get; set; }
        public int TotalQuantity { get; set; }
    }
}
