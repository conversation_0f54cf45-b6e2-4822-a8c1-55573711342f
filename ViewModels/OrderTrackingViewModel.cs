using System.ComponentModel.DataAnnotations;

namespace Abayat.ViewModels
{
    public class OrderTrackingViewModel
    {
        [Display(Name = "رقم الطلب")]
        public int? OrderId { get; set; }

        [Display(Name = "رقم الهاتف")]
        [RegularExpression(@"^[0-9]{8,15}$", ErrorMessage = "يرجى إدخال رقم هاتف صحيح (8 أرقام على الأقل)")]
        public string PhoneNumber { get; set; } = string.Empty;
    }
}
