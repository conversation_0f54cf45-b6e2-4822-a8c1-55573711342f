# تعليمات إصلاح مشكلة عمود ShowInCarousel

## المشكلة
تظهر رسالة خطأ `SqlException: Invalid column name 'ShowInCarousel'` عند تشغيل التطبيق، مما يشير إلى أن عمود `ShowInCarousel` غير موجود في جدول `Products` في قاعدة البيانات.

## الإصلاحات التي تم تنفيذها

1. **تعديل HomeController.cs**:
   - تم تعديل طريقة `Index` و `Carousel` لتجنب استخدام عمود `ShowInCarousel` حتى يتم إصلاح المشكلة
   - سيتم عرض المنتجات المتاحة في الشريط المتحرك بدلاً من المنتجات التي تم تحديدها للعرض في الشريط المتحرك

2. **إضافة أداة إصلاح قاعدة البيانات**:
   - تم إنشاء فئة `DatabaseFixer` في `Data/DatabaseFixer.cs` للتحقق من وجود العمود وإضافته إذا لم يكن موجودًا
   - تم تعديل ملف `Program.cs` لاستدعاء أداة الإصلاح عند بدء التشغيل

3. **إنشاء ملفات SQL للإصلاح اليدوي**:
   - `fix_show_in_carousel.sql`: لإضافة العمود مباشرة إلى قاعدة البيانات
   - `direct_fix.sql`: ملف SQL أكثر تفصيلاً للتحقق من وجود الجدول والعمود وإضافته إذا لم يكن موجودًا

## الخطوات الإضافية للإصلاح

إذا استمرت المشكلة بعد تنفيذ الإصلاحات المذكورة أعلاه، يمكنك اتباع الخطوات التالية:

### الحل 1: تنفيذ ملف SQL مباشرة
1. افتح SQL Server Management Studio (SSMS)
2. اتصل بقاعدة البيانات باستخدام بيانات الاتصال الموجودة في ملف `appsettings.json`
3. نفذ الاستعلام الموجود في ملف `direct_fix.sql`

### الحل 2: إعادة إنشاء قاعدة البيانات
إذا لم تنجح الحلول السابقة، يمكنك إعادة إنشاء قاعدة البيانات بالكامل:

1. افتح SQL Server Management Studio (SSMS)
2. اتصل بقاعدة البيانات
3. نفذ الاستعلام التالي:

```sql
USE master;

IF EXISTS (SELECT * FROM sys.databases WHERE name = 'DBAbayat')
BEGIN
    ALTER DATABASE DBAbayat SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
    DROP DATABASE DBAbayat;
    PRINT 'تم حذف قاعدة البيانات DBAbayat';
END

CREATE DATABASE DBAbayat;
PRINT 'تم إنشاء قاعدة البيانات DBAbayat من جديد';
```

4. قم بتشغيل التطبيق مرة أخرى، وسيقوم بإنشاء قاعدة البيانات وتطبيق الهجرات تلقائيًا

## ملاحظات إضافية
- تم تعديل ملف `Program.cs` لاستخدام `context.Database.Migrate()` بدلاً من `context.Database.EnsureCreated()` لضمان تطبيق جميع الهجرات
- تم إضافة ملف هجرة جديد `20240701000000_AddShowInCarouselColumn.cs` لإضافة العمود إذا لم يكن موجودًا
- تم إضافة أداة إصلاح قاعدة البيانات `DatabaseFixer` للتحقق من وجود العمود وإضافته عند بدء التشغيل

بعد تنفيذ أحد هذه الحلول، يجب أن يعمل التطبيق بشكل صحيح دون ظهور رسالة الخطأ.
